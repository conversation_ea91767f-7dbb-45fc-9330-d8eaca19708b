import importlib
import mines2
import os
import sys
from subprocess import STDOUT, CalledProcessError, check_output


def install(path: str):
    print("Installing Library from Path: ", path)
    try:
        output = check_output(
            [sys.executable, "-m", "pip", "install", path], stderr=STDOUT
        )
    except CalledProcessError as exc:
        print(exc.output)
        raise


def get_dbutils():
    if "DATABRICKS_RUNTIME_VERSION" in os.environ:
        from databricks.sdk.runtime import dbutils

        return dbutils
    else:
        from databricks.sdk import WorkspaceClient

        return WorkspaceClient().dbutils


def get_catalog():
    """Get Environment from databricks"""
    ENVIRONMENT_DEV = "DEV"
    ENVIRONMENT_UAT = "UAT"
    ENVIRONMENT_PROD = "PROD"
    assert os.environ["MINES_ENVIRONMENT"] in (
        ENVIRONMENT_DEV,
        ENVIRONMENT_UAT,
        ENVIRONMENT_PROD,
    ), "Must be a Known Environment"
    catalog = f'{os.environ["MINES_ENVIRONMENT"].lower()}_europeandatalake_01'
    return catalog


def _get_wheel_path(path: str) -> str:
    """Get the wheel file from a path."""
    wheel_paths = []
    dbutils = get_dbutils()
    for file in dbutils.fs.ls(path):
        if file.name.endswith(".whl"):
            wheel_paths.append(os.path.join(path, file.name))
    assert len(wheel_paths) == 1, f"{wheel_paths=} must be 1!"
    wheel_path = wheel_paths[0]

    return wheel_path


def install_models_scripts(catalog: str, path_root: str) -> None:
    """Install custom transformation."""
    model_name = "EDF"
    models_scripts_path = os.path.join(
        "/Volumes", catalog, "default", "models", path_root, "package", model_name
    )
    wheel_path = _get_wheel_path(models_scripts_path)
    install(wheel_path)


def install_mines(catalog: str, path_root: str) -> None:
    """Install custom transformation."""
    mines_path = os.path.join("/Volumes", catalog, "default", "scripts", path_root)
    wheel_path = _get_wheel_path(mines_path)
    install(wheel_path)


def main():
    module_path = sys.argv[1]
    path_root = sys.argv[2]
    catalog = get_catalog()

    try:
        import models_scripts.transformations as transformations
    except:
        install_models_scripts(catalog, path_root)

    module = importlib.import_module(f"mines2.{module_path}")
    module.entrypoint()


if __name__ == "__main__":
    main()
