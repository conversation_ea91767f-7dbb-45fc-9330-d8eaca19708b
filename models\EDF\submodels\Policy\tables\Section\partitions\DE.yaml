Country: custom
existCustomTransformation: 'True'

dataSource:
- name: main
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: dev_europeandatalake_01
      uat: uat_europeandatalake_01
      prod: prod_europeandatalake_01
    schema: DE_Direct
    table: Policy_PolicyArraySection
    sourceSystem: SPARK_EDF_EXPORT

- name: policy_policy
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Policy
    Table: Policy

- name: exchange_rate
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: test_mintdatalake_02
      uat: test_mintdatalake_02
      prod: prod_mintdatalake_02
    schema: rdm_dbo
    table: mint_mapping_roe
    sourceSystem: MINT_RAW_DATALAKE

- name: policy_limit
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Policy
    Table: Limit

ColumnSpecs:
  EstSignedDown:
    locale: en_US.utf8
  InsurerCarrierPercentage:
    locale: en_US.utf8
  ProfitCommission:
    locale: en_US.utf8
  SignedLine:
    locale: en_US.utf8
  SignedOrder:
    locale: en_US.utf8
  WrittenLine:
    locale: en_US.utf8
  WrittenOrder:
    locale: en_US.utf8
  ClaimsSectionPolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: DE
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: DE
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  PolicySectionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: DE
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  SectionEffectiveFromDate:
    dateTimeFormat: ISO
  SectionEffectiveToDate:
    dateTimeFormat: ISO
  KeyReserving:
    NotInSource: True
