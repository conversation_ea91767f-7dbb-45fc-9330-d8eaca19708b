import mines2.core.constants as const
import pyspark.sql.functions as F
from pyspark.sql import Column, DataFrame

from models_scripts.transformations.common.currency import CurrencyConversorBase
from models_scripts.transformations.common.traits import transformation


def conform_euro_currency(df: DataFrame, currency_column: str) -> DataFrame:
    """Standardize the EURO currency notation in the DataFrame.

    Args:
        df: Input DataFrame that needs currency normalization.
        currency_column: Name of the column that contains currency information.

    Returns:
        DataFrame after replacing the different Euro notations with 'EUR'.
    """
    euro_condition = F.col(currency_column).isin(["EURO", ""])
    null_condition = F.col(currency_column).isNull()

    df = df.withColumn(
        currency_column,
        F.when(euro_condition | null_condition, "EUR").otherwise(
            F.col(currency_column)
        ),
    )
    return df


class EDFTransComponentCurrencyConversorBase(CurrencyConversorBase):
    """Class to aggregate the functions to add new columns to the dataframe with the exchange rate applied."""

    TRANSACTION_COMPONENT_JOIN_COLS = None
    TRANSACTION_CURRENCY_COL = None
    AMOUNT_COL = None

    def __init__(self, amount_col: str = None):
        """We expect that amount_col is defined in the subclass. But keep here to align with the parent class."""
        self.AMOUNT_COL = amount_col or self.AMOUNT_COL

        assert (
            self.TRANSACTION_COMPONENT_JOIN_COLS is not None
        ), "TRANSACTION_COMPONENT_JOIN_COLS must be defined in the subclass"
        assert (
            self.TRANSACTION_CURRENCY_COL is not None
        ), "TRANSACTION_CURRENCY_COL must be defined in the subclass"
        assert (
            self.AMOUNT_COL is not None
        ), "COMPONENT_AMOUNT must be defined in the subclass"

        super().__init__(self.AMOUNT_COL)

    @classmethod
    @transformation
    def add_new_columns(
        cls,
        transaction_component_df: DataFrame,
        transaction_df: DataFrame,
        exchange_rate_df: DataFrame,
    ) -> DataFrame:
        """Add new columns to the dataframe with the exchange rate applied."""
        conversor = cls()

        valid_exchange_rate_df = conversor._get_valid_exchange_rate(exchange_rate_df)
        transaction_with_exchange_rates_df = (
            conversor._get_exchange_rates_for_transactions(
                transaction_df, valid_exchange_rate_df
            )
        )
        transaction_components_with_converted_currency_df = (
            conversor._add_converted_currency_columns(
                transaction_component_df, transaction_with_exchange_rates_df
            )
        )
        return transaction_components_with_converted_currency_df

    @classmethod
    def add_new_columns_using_single_currency(
        cls,
        transaction_component_df: DataFrame,
        exchange_rate_df: DataFrame,
        currency: str,
        amount_col: str = None,
    ) -> DataFrame:
        """
        Add new columns to the dataframe with the exchange rate applied. Consider that all the amount has
        the same pre-defined currency.
        Args:
            transaction_component_df: DataFrame with the transaction components.
            exchange_rate_df: DataFrame with the exchange rates.
            currency: Currency to convert the amount.
            amount_col: Column with the amount to convert.

        Returns:
            DataFrame with the new columns with the converted and rounded currencies.

        """
        amount_col = amount_col or cls.AMOUNT_COL

        return CurrencyConversorBase.add_new_columns_using_single_currency(
            transaction_component_df, exchange_rate_df, currency, amount_col=amount_col
        )

    def _get_exchange_rates_for_transactions(
        self, transaction_df: DataFrame, pivoted_exchange_rate_df: DataFrame
    ) -> DataFrame:
        """Add the exchange rate columns to the transaction dataframe."""
        transaction_selected_df = transaction_df.select(
            *self.TRANSACTION_COMPONENT_JOIN_COLS, self.TRANSACTION_CURRENCY_COL
        )

        joined_df = self.join_with_exchange_rate(
            transaction_selected_df,
            pivoted_exchange_rate_df,
            self.TRANSACTION_CURRENCY_COL,
        )
        return joined_df.select(
            *self.TRANSACTION_COMPONENT_JOIN_COLS, *self.CURRENCIES_TO_CONVERT
        )

    def _add_converted_currency_columns(
        self, transaction_component_df: DataFrame, transaction_exchanges_df: DataFrame
    ) -> DataFrame:
        """Add the converted currency columns to the transaction component dataframe."""
        original_columns = transaction_component_df.columns
        required_columns = self.TRANSACTION_COMPONENT_JOIN_COLS + [self.AMOUNT_COL]
        assert set(required_columns) <= set(
            original_columns
        ), f"Columns {required_columns} not found in the dataframe."

        HAVE_MATCH_TRANSACTION_COL = "MatchTransaction"

        transaction_exchanges_selected_df = transaction_exchanges_df.select(
            *self.TRANSACTION_COMPONENT_JOIN_COLS,
            *self.CURRENCIES_TO_CONVERT,
            F.lit(True).alias(
                HAVE_MATCH_TRANSACTION_COL
            ),  # To be used to validate join success
        )

        joined_df = transaction_component_df.join(
            transaction_exchanges_selected_df,
            self.TRANSACTION_COMPONENT_JOIN_COLS,
            "left",
        )

        select_query = self._create_new_column_select_query()
        failed_matching_quarantine_col = self._quarantine_not_match_col(
            HAVE_MATCH_TRANSACTION_COL
        )
        output_df = joined_df.select(
            *original_columns, *select_query, failed_matching_quarantine_col
        )
        return output_df

    @staticmethod
    def _quarantine_not_match_col(have_match_col: str) -> Column:
        quarantine_col = const.CUSTOM_QUARANTINE_PREFIX + "MappingIsMissing"
        quarantine_value = F.when(
            F.col(have_match_col).isNull() | (~F.col(have_match_col)),
            F.lit("No Match between the Table and the Transaction"),
        ).otherwise(
            F.lit(None)
        )  # Null if the join was successful, so no quarantine needed.
        return quarantine_value.alias(quarantine_col)


class PolicyCurrencyConversor(EDFTransComponentCurrencyConversorBase):
    """Class to aggregate the functions to add new columns to the dataframe with the exchange rate applied for the Policy
    component."""

    TRANSACTION_COMPONENT_JOIN_COLS = [
        "KeyIdPolis",
        "KeyDekkingsNummer",
        "KeyFactuurnummer",
    ]
    TRANSACTION_CURRENCY_COL = "OriginalCurrencyCode"
    AMOUNT_COL = "TransactionComponentAmount"


class ClaimCurrencyConversor(EDFTransComponentCurrencyConversorBase):
    """Class to aggregate the functions to add new columns to the dataframe with the exchange rate applied for the Claim
    component."""

    TRANSACTION_COMPONENT_JOIN_COLS = [
        "KeyInternSchadenummer",
        "KeyDekkingsNummer",
        "KeySchadeBoekingsNummer",
    ]
    TRANSACTION_CURRENCY_COL = "TransactionCurrencyCode"
    AMOUNT_COL = "TransactionComponentAmount"


class SettlementCurrencyConversor(EDFTransComponentCurrencyConversorBase):
    """Class to aggregate the functions to add new columns to the dataframe with the exchange rate applied for the Settlement
    component."""

    TRANSACTION_COMPONENT_JOIN_COLS = [
        "KeyIdPolis",
        "KeyDekkingsNummer",
        "KeyFactuurNummer",
    ]
    TRANSACTION_CURRENCY_COL = "OriginalCurrencyCode"
    AMOUNT_COL = "TransactionComponentAmount"

    @classmethod
    def add_new_columns(
        cls,
        transaction_component_df: DataFrame,
        transaction_df: DataFrame,
        exchange_rate_df: DataFrame,
    ) -> DataFrame:
        """Add new columns to the dataframe with the exchange rate applied.
        Uniforming KeyFactuurnummer to KeyFactuurNummer before calling the parent method.
        """

        transaction_component_df = cls._conform_factuurnummer(transaction_component_df)
        transaction_df = cls._conform_factuurnummer(transaction_df)

        return super().add_new_columns(
            transaction_component_df, transaction_df, exchange_rate_df
        )

    @classmethod
    def _conform_factuurnummer(cls, df: DataFrame) -> DataFrame:
        """Conform the factuurnummer column to the same name."""
        df_columns = df.columns
        lowered_df_columns = {column.lower(): column for column in df_columns}
        for column in cls.TRANSACTION_COMPONENT_JOIN_COLS:
            if (
                column not in df_columns
            ) and column.lower() in lowered_df_columns.keys():
                df = df.withColumnRenamed(lowered_df_columns[column.lower()], column)
        return df


class AddCurrencyColumns:
    """Class to add currency columns to the dataframe with the exchange rate applied."""

    currency_amount_cols = None

    def __init__(self, currency_amount_cols: list[tuple[str, str]] = None):
        self.currency_amount_cols = currency_amount_cols or self.currency_amount_cols
        assert (
            self.currency_amount_cols is not None
        ), "currency_amount_cols must be defined in the subclass"

    @classmethod
    def add_currency_columns(
        cls, df: DataFrame, exchange_rate_df: DataFrame
    ) -> DataFrame:
        for currency_col, amount_col in cls.currency_amount_cols:
            df = conform_euro_currency(df, currency_col)
        return (
            CurrencyConversorBase.add_new_columns_using_currency_column_from_list_input(
                df, exchange_rate_df, cls.currency_amount_cols
            )
        )


class ClaimClaimCurrencyConversor(AddCurrencyColumns):
    """Class to add currency columns to the Claim_Claim dataframe."""

    currency_amount_cols = [("MaximumPotentialLossCurrency", "MaximumPotentialLoss")]


class PolicyExcessCurrencyConversor(AddCurrencyColumns):
    """Class to add currency columns to the Policy_Excess dataframe."""

    currency_amount_cols = [("ExcessCurrencyCode", "Excess")]


class PolicyAttachmentPointCurrencyConversor(AddCurrencyColumns):
    """Class to add currency columns to the Policy_Excess dataframe."""

    currency_amount_cols = [("AttachmentPointCurrencyCode", "AttachmentPoint")]


class PolicyLimitCurrencyConversor(AddCurrencyColumns):
    """Class to add currency columns to the Policy_Limit dataframe."""

    currency_amount_cols = [
        ("LimitCurrencyCode", "LimitAGG"),
        ("LimitCurrencyCode", "LimitAOC"),
    ]


class PolicyPolicyCurrencyConversor(AddCurrencyColumns):
    """Class to add currency columns to the Policy_Policy dataframe."""

    currency_amount_cols = [("AssuredAnnualTurnoverCurrency", "AssuredAnnualTurnover")]


class PolicySectionCurrencyConversor(AddCurrencyColumns):
    """Class to add currency columns to the Policy_Section dataframe."""

    currency_amount_cols = [("LimitCurrencyCode", "Deductible")]


class PolicySubLimitCurrencyConversor(AddCurrencyColumns):
    """Class to add currency columns to the Policy_SubLimit dataframe."""

    currency_amount_cols = [
        ("SubLimitCurrencyCode", "SubLimitAGG"),
        ("SubLimitCurrencyCode", "SubLimitAOC"),
    ]
