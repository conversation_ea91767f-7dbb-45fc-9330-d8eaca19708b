import pyspark.sql.functions as F
from mines2.core.constants import SOURCE_SYSTEM
from pyspark.sql import DataFrame


def calculate_limits(df: DataFrame) -> DataFrame:
    """Calculates the limits (AGG and AOC) for a given DataFrame.

    Args:
    - df: the DataFrame to calculate the limits for.

    Returns:
    - a new DataFrame with the calculated limits.
    """
    extra_columns = ["LimitCurrencyCode", SOURCE_SYSTEM]
    df = (
        df.groupBy("KeyIdPolis", "KeyDekkingsNummer", *extra_columns)
        .pivot("LimitBasisCode")
        .agg(F.first("Limit"))
        .withColumnsRenamed({"AGG": "LimitAGG", "AOC": "LimitAOC"})
    )
    return df


def handle_null_limits_spark(df: DataFrame, is_sublimit=False) -> DataFrame:
    """The objective of this transformation is to fix the null values in the LimitAGG and LimitAOC columns."""

    if is_sublimit:
        LimitAGG = "SubLimitAGG"
        LimitAOC = "SubLimitAOC"
    else:
        LimitAGG = "LimitAGG"
        LimitAOC = "LimitAOC"
    # Handle missing columns:
    assert (
        LimitAGG in df.columns or LimitAOC in df.columns
    ), f"Missing {LimitAGG} or {LimitAOC} columns"
    if LimitAGG not in df.columns:
        df = df.withColumn(LimitAGG, df[LimitAOC])
    if LimitAOC not in df.columns:
        df = df.withColumn(LimitAOC, df[LimitAGG])

    # Coalesce LimitAGG and LimitAOC:
    df = df.withColumn(LimitAOC, F.coalesce(df[LimitAOC], df[LimitAGG]))
    df = df.withColumn(LimitAGG, F.coalesce(df[LimitAGG], df[LimitAOC]))

    return df
