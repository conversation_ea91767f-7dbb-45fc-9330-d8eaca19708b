Columns:
  PolicyID:
    dataType: string
  ClaimsPolicyID:
    dataType: string
  KeyIdPolis:
    dataType: string
  PolicySequenceNumber:
    dataType: int
  AdministrationOfficeCode:
    dataType: string
  AssuredAddressArea:
    dataType: string
  AssuredAddressCity:
    dataType: string
  AssuredAddressStreet:
    dataType: string
    PII: true
  AssuredAnnualTurnover:
    dataType: float
  AssuredAnnualTurnoverCurrency:
    dataType: string
    conformCurrency: True
  AssuredAnnualTurnoverRounded:
    dataType: currency
  AssuredAnnualTurnoverGBP:
    dataType: double
  AssuredAnnualTurnoverEUR:
    dataType: double
  AssuredAnnualTurnoverUSD:
    dataType: double
  AssuredAnnualTurnoverCAD:
    dataType: double
  AssuredAnnualTurnoverRoundedGBP:
    dataType: currency
  AssuredAnnualTurnoverRoundedEUR:
    dataType: currency
  AssuredAnnualTurnoverRoundedUSD:
    dataType: currency
  AssuredAnnualTurnoverRoundedCAD:
    dataType: currency
  AssuredCode:
    dataType: string
  AssuredCountry:
    dataType: string
  AssuredFullName:
    dataType: string
    PII: true
  AssuredMainActivityCode:
    dataType: string
  AssuredMainActivityDescription:
    dataType: string
  AssuredNumberOfFullTimeEmployees:
    dataType: int
  AssuredPostcode:
    dataType: string
  AssuredProvince:
    dataType: string
  AssuredShortName:
    dataType: string
  AssuredState:
    dataType: string
  AssuredTerritory:
    dataType: string
  BrokerAddressArea:
    dataType: string
  BrokerAddressCity:
    dataType: string
  BrokerAddressStreet:
    dataType: string
  BrokerCode:
    dataType: string
  BrokerCodeID:
    dataType: string
  BrokerCountry:
    dataType: string
  BrokerFullName:
    dataType: string
    PII: true
  BrokerGroupCode:
    dataType: string
  BrokerGroupName:
    dataType: string
  BrokerPostcode:
    dataType: string
  BrokerProvince:
    dataType: string
  BrokerShortName:
    dataType: string
  ClaimsBasisCode:
    dataType: string
  ClaimsBasisDescription:
    dataType: string
  CoverholderCode:
    dataType: string
  CoverholderName:
    dataType: string
  CustomerClassification:
    dataType: string
  CustomerClassificationCode:
    dataType: string
  DistributionPartner:
    dataType: string
  DistributionType:
    dataType: string
  ExpiryDate:
    dataType: date
  IBCIndustryCode:
    dataType: string
  BusinessClassification:
    dataType: string
  InceptionDate:
    dataType: date
  InsurerEntityCode:
    dataType: string
  InsurerEntityDescription:
    dataType: string
    PII: true
  IPTLiable:
    dataType: string
  LapsedReason:
    dataType: string
  Layer:
    dataType: string
  OrderPercentage:
    dataType: float
  OutwardsFACIndicator:
    dataType: string
  PeerReview1:
    dataType: string
  PeerReview1Code:
    dataType: string
  PeerReview1Comment:
    dataType: string
  PeerReview1Date:
    dataType: date
  PeerReview2:
    dataType: string
  PeerReview2Code:
    dataType: string
  PeerReview2Comment:
    dataType: string
  PeerReview2Date:
    dataType: date
  PlacementType:
    dataType: string
  PolicyCode:
    dataType: string
  PolicyLastModifiedDate:
    dataType: date
  PolicyProductCode:
    dataType: string
  PolicyProductDescription:
    dataType: string
  PolicyReference:
    dataType: string
  PreviousPolicyReference:
    dataType: string
  PreviousSourceSystem:
    dataType: string
  PreviousSourceSystemDescription:
    dataType: string
  ProducingOfficeCode:
    dataType: string
  QuotationReference:
    dataType: string
  ReferralUnderwriter:
    dataType: string
  ReinsurancePolicyIndicator:
    dataType: string
  ReinsuranceReference:
    dataType: string
  ReinsuredAddressArea:
    dataType: string
  ReinsuredAddressCity:
    dataType: string
  ReinsuredAddressStreet:
    dataType: string
  ReinsuredAnnualTurnover:
    dataType: int
  ReinsuredAnnualTurnoverCurrency:
    dataType: string
  ReinsuredCode:
    dataType: string
  ReinsuredCountry:
    dataType: string
  ReinsuredFullName:
    dataType: string
  ReinsuredNumberOfFullTimeEmployees:
    dataType: int
  ReinsuredPostcode:
    dataType: string
  ReinsuredProvince:
    dataType: string
  ReinsuredShortName:
    dataType: string
  ReinsuredState:
    dataType: string
  ReinsuredTerritory:
    dataType: string
  RenewalPolicyIndicator:
    dataType: string
  RenewalPolicyReference:
    dataType: string
  RenewalSequenceNumber:
    dataType: int
  RiskCode:
    dataType: string
  StatusCode:
    dataType: string
  StatusDescription:
    dataType: string
  TacitRenewalIndicator:
    dataType: string
  TerrorismCode:
    dataType: string
  Timezone:
    dataType: string
  TradeCodeOrIndustry:
    dataType: string
  TrustFund:
    dataType: string
  UnderlyingLimit:
    dataType: string
  UnderlyingLimitCurrency:
    dataType: string
    conformCurrency: True
  UnderwriterCode:
    dataType: string
  UnderwriterCodeID:
    dataType: string
  UnderwriterName:
    dataType: string
  WrittenDate:
    dataType: date
  YearOfAccount:
    dataType: int
    RestrictMaxValue: year(add_months(current_date(), 3 * 12))
    RestrictMinValue: 1970
  ReUnderwritingExercise:
    dataType: bool
  IsCurrentFlag:
    dataType: bool
    calculatedColumn: True
  LatestPolicyFlag:
    dataType: bool
    calculatedColumn: True
  EarliestPolicyFlag:
    dataType: bool
    calculatedColumn: True
  LatestPolicyAssuredAnnualTurnoverFlag:
    dataType: bool
    calculatedColumn: True

primaryKey:
  - PolicyID
