select m1.*
        , t9.RenewalPolicyIndicator
        , t9.RenewalPolicyReference
        , t9.RenewalSequenceNumber
        , case when t10.AssuredMainActivityCode is null then '' else t10.AssuredMainActivityCode end as 'AssuredMainActivityCode'
        , case when t10.AssuredMainActivityDescription is null then '' else t10.AssuredMainActivityDescription end as 'AssuredMainActivityDescription'
        , case when t10.AssuredMainActivityCode is null then '' else t10.AssuredMainActivityCode end as 'TradeCodeOrIndustry'

from (

select distinct p.idpolis                                     as "KeyIdPolis"
, 'Rotterdam' 			              		      as "AdministrationOfficeCode"
, ''							      as "AssuredAddressArea"
, r.woonplaats                                                as "AssuredAddressCity"
, r.adres                                                     as "AssuredAddressStreet"
, case when ato.aantaleenheden is null then
                case when ato2.aantaleenheden is null then 0 else ato2.aantaleenheden end
        when ato.aantaleenheden = 0 then
                case when ato2.aantaleenheden is null then 0 else ato2.aantaleenheden end
        else ato.aantaleenheden end as "AssAnnTo"
, case when ato.aantaleenheden is null then
                case when ato2.aantaleenheden is null then '' else 'EURO' end
        when ato.aantaleenheden = 0 then
                case when ato2.aantaleenheden is null then '' else 'EURO' end
        else 'EURO' end as "AssAnnToCur"

, p.relatienummer																							as "AssuredCode"
, r.landcode																									as "AssuredCountry"
, replace(r.naam, '|', '[') + ' ' + replace(r.naam3, '|', '[') as "AssuredFullName"
, case when afte.aantaleenheden is null then 0 else afte.aantaleenheden end as "AssNrOfFTE"
, r.postcode                                                  as "AssuredPostCode"
, ''                                                          as "AssuredProvince"
, ''                                                          as "AssuredShortName"
, ''                                                          as "AssuredState"
, ''                                                          as "AssuredTerritory"
, ''                                                          as "BrokerAddressArea"
, t.woonplaats                                                as "BrokerAddressCity"
, t.adres                                                     as "BrokerAddressStreet"
, p.tussenpersoonnummer                                       as "BrokerCode"
, t.landcode                                                  as "BrokerCountry"
, replace(t.zoeksleutel, '|', '[')                            as "BrokerFullName"
, tv.rubriek1                                                 as "BrokerGroupCode"
, tvo.omschrijving                                            as "BrokerGroupName"
, t.postcode                                                  as "BrokerPostcode"
, ''                                                          as "BrokerProvince"
, ''                                                          as "BrokerShortName"
, 'CM'                                                        as "ClaimsBasisCode"
, 'CLAIMS MADE'                                               as "ClaimsBasisDescription"
, ''                                                          as "CoverholderCode"
, ''                                                          as "CoverholderName"
, tb.omschrijving                                             as "CustomerClassification"
, ab.bedrijfscode                                             as "CustomerClassificationCode"
, ''                                                          as "DistributionPartner"
, 'BROKER'                                                    as "DistributionType"
, case when p.expiratiedatum is not null and p.soortpolis <> 0 then p.expiratiedatum else p.contractvervaldatum end as "ExpiryDate"
, ''                                                          as "IBCIndustryCode"
, ifnull(p.histhoofdpremievervaldatum, p.ingangsdatum)        as "InceptionDate"
, case when p.maatschappijnummer = 'M001' then 'MIICL' else case when p.maatschappijnummer = 'M002' then 'MISE' else '' end end as "InsurerEntityCode"
, case when p.maatschappijnummer = 'M001' then 'MARKEL INTERNATIONAL INSURANCE COMPANY LIMITED' else
  case when p.maatschappijnummer = 'M002' then 'MARKEL INTERNATIONAL SOCIETAS EUROPAEA' else '' end end as "InsurerEntityDescription"
, case when p.mutatiereden >= 4200 and p.mutatiereden <= 4400 then ltrim(to_char(p.mutatiereden)) else '' end as "LapsedReason"
, case when p.productcode in (160, 162, 164, 166, 168, 170, 172, 174, 260, 360) then 'EXCEDENT' else 'PRIMARY' end as "Layer"
, ''                                                          as "OrderPercentage"
, 'N'                                                         as "OutwardsFACIndicator"
, p1.naam                                                     as "PeerReview1"
, p1.medewerkernummer                                         as "PeerReview1Code"
, ltrim(p1.tekst1) + ' ' + ltrim(p1.tekst2) + ' ' + ltrim(p1.tekst3) + ' ' + ltrim(p1.tekst4) + ' ' + ltrim(p1.tekst5) + ' '
  + ltrim(p1.tekst6) + ' ' + ltrim(p1.tekst7) + ' ' + ltrim(p1.tekst8) + ' ' + ltrim(p1.tekst9) + ' ' + ltrim(p1.tekst10) + ' '
  + ltrim(p1.tekst11) + ' ' + ltrim(p1.tekst12) + ' ' + ltrim(p1.tekst13) + ' ' + ltrim(p1.tekst14) + ' ' + ltrim(p1.tekst15) as "PeerReview1Comment"
, p1.aanmaakdatum                                             as "PeerReview1Date"
, case when p1.assur_recid = p2.assur_recid then '' else p2.naam end as "PeerReview2"
, case when p1.assur_recid = p2.assur_recid then null else p2.medewerkernummer end as "PeerReview2Code"
, case when p1.assur_recid = p2.assur_recid then '' else ltrim(p2.tekst1) + ' ' + ltrim(p2.tekst2) + ' ' + ltrim(p2.tekst3) + ' ' + ltrim(p2.tekst4) + ' ' + ltrim(p2.tekst5) + ' '
  + ltrim(p2.tekst6) + ' ' + ltrim(p2.tekst7) + ' ' + ltrim(p2.tekst8) + ' ' + ltrim(p2.tekst9) + ' ' + ltrim(p2.tekst10) + ' '
  + ltrim(p2.tekst11) + ' ' + ltrim(p2.tekst12) + ' ' + ltrim(p2.tekst13) + ' ' + ltrim(p2.tekst14) + ' ' + ltrim(p2.tekst15) end as "PeerReview2Comment"
, case when p1.assur_recid = p2.assur_recid then NULL else p2.aanmaakdatum end as "PeerReview2Date"
, case when p.productcode = 950 then 'BINDER' else 'OPEN MARKET' end as "PlacementType"
, p.internpolisnummer + '/' + ltrim(to_char(year(ifnull(p.histhoofdpremievervaldatum, p.ingangsdatum)))) as "PolicyCode"
, p.Mutatiedatum                                              as "PolicyLastModifiedDate"
, p.productcode                                               as "PolicyProductCode"
, tp.omschrijving                                             as "PolicyProductDescription"
, p.polisnummer                                               as "PolicyReference"
, case when substring(p.internpolisnummer,1,6) = 'MARKEL' and p.aanmaakdatum = to_date('5/31/2017') then p.polisnummer else '' end as "PreviousPolicyReference"
, case when substring(p.internpolisnummer,1,6) = 'MARKEL' and p.aanmaakdatum = to_date('5/31/2017') then 'SCS' else '' end as "PreviousSourceSystem"
, case when substring(p.internpolisnummer,1,6) = 'MARKEL' and p.aanmaakdatum = to_date('5/31/2017') then 'SCS' else '' end as "PreviousSourceSystemDescription"
, 'Rotterdam'                                                 as "ProducingOfficeCode"
, ''                                                          as "QuotationReference"
, p.aanbrengendproducent                                      as "ReferralUnderwriter"
, ''                                                          as "ReinsurancePolicyIndicator"
, ''                                                          as "ReinsuranceReference"
, ''                                                          as "ReinsuredAddressArea"
, ''                                                          as "ReinsuredAddressCity"
, ''                                                          as "ReinsuredAddressStreet"
, ''                                                          as "ReinsuredAnnualTurnover"
, ''                                                          as "ReinsuredAnnualTurnoverCurrency"
, ''                                                          as "ReinsuredCode"
, ''                                                          as "ReinsuredCountry"
, ''                                                          as "ReinsuredFullName"
, ''                                                          as "ReinsuredNumberOfFTEmployees"
, ''                                                          as "ReinsuredPostcode"
, ''                                                          as "ReinsuredProvince"
, ''                                                          as "ReinsuredShortName"
, ''                                                          as "ReinsuredState"
, ''                                                          as "ReinsuredTerritory"
, ''                                                          as "RiskCode"
, p.mutatiereden                                              as "StatusCode"
, tm.omschrijving                                             as "StatusDescription"
, case when p.soortpolis = 0 then 'Y' else 'N' end            as "TacitRenewalIndicator"
, 'NHT'                                                       as "TerrorismCode"
, 'The Netherlands'                                           as "Timezone"
, ''                                                          as "TrustFund"
, o.rubriek2                                                  as "UnderlyingLimit"
, 'EURO'                                                      as "UnderlyingLimitCurrency"
, p.producent                                                 as "UnderwriterCode"
, tmw.naam                                                    as "UnderwriterName"
, case when p.aanmaakdatum = to_date('2017/05/31') then ''
  else ltrim(to_char(year(p.creatiedatum))) + '-' + ltrim(to_char(month(p.creatiedatum)))
  + '-' + ltrim(to_char(dayofmonth(p.creatiedatum))) end      as "WrittenDate"
, year(ifnull(p.histhoofdpremievervaldatum, p.ingangsdatum))  as "YearOfAccount"
from pub.polisversie p
inner join pub.relatie r on r.relatienummer = p.relatienummer
left outer join pub.tussenpersoon t on t.bedrijftussenpersoonnummer = p.bedrijftussenpersoonnummer
left outer join pub.tussenpersoonvrij tv on tv.bedrijftussenpersoonnummer = p.bedrijftussenpersoonnummer
left outer join pub.tabelvrijerubriektabel tvo on tvo.soortvrijerubriek = 'T' and
                                                  tvo.volgnummerrubriek = 1 and
                                                  tvo.tabelcode = tv.rubriek1
left outer join pub.ab ab on ab.internpolisnummerobjectnummer = p.internpolisnummer + '/**********'
left outer join pub.tabelbedrijf tb on tb.bedrijfscode = ab.bedrijfscode
left outer join pub.tabelproduct tp on tp.soortverzekeringproductcode = p.soortverzekeringproductcode
left outer join pub.tabelmutatieredenpolis tm on tm.mutatiereden = p.mutatiereden
left outer join pub.objectvrij o on o.internpolisnummerobjectnummer = p.internpolisnummer + '/**********'
left outer join pub.tabelmedewerker tmw on tmw.bedrijfmedewerkernummer = p.bedrijfproducent
left outer join (select a.internpolisnummer, c.naam, b.medewerkernummer, b.aanmaakdatum,
								 b.tekst1, b.tekst2, b.tekst3, b.tekst4, b.tekst5, b.tekst6, b.tekst7, b.tekst8,
								 b.tekst9, b.tekst10, b.tekst11, b.tekst12, b.tekst13, b.tekst14, b.tekst15, a.assur_recid
								 from (select a.internpolisnummer, a.memocode, min(a.assur_recid) as assur_recid from pub.memopolisversie a group by a.internpolisnummer, a.memocode having a.memocode = 'PR') a
                                         left outer join pub.memopolisversie b on b.assur_recid = a.assur_recid
                                         left outer join pub.tabelmedewerker c on c.medewerkernummer = b.medewerkernummer) p1 on p1.internpolisnummer = p.internpolisnummer
left outer join (select a.internpolisnummer, c.naam, b.medewerkernummer, b.aanmaakdatum,
								 b.tekst1, b.tekst2, b.tekst3, b.tekst4, b.tekst5, b.tekst6, b.tekst7, b.tekst8,
								 b.tekst9, b.tekst10, b.tekst11, b.tekst12, b.tekst13, b.tekst14, b.tekst15, a.assur_recid
								 from (select a.internpolisnummer, a.memocode, max(a.assur_recid) as assur_recid from pub.memopolisversie a group by a.internpolisnummer, a.memocode having a.memocode = 'PR') a
                                         left outer join pub.memopolisversie b on b.assur_recid = a.assur_recid
                                         left outer join pub.tabelmedewerker c on c.medewerkernummer = b.medewerkernummer) p2 on p2.internpolisnummer = p.internpolisnummer

/* Treatment of Turnover starts here ************************************************************************************************/
left outer join (select distinct q.InternPolisnummer, min(b.internpolisnummerdekkingsnummer) as internpolisnummerdekkingsnummer
                 from dwh.pub.polisversie q
                 left outer join dwh.pub.dekking b
                 ON q.InternPolisnummer = b.InternPolisnummer
                 WHERE LEFT(LTRIM(TO_CHAR(q.Productcode)),1) = LEFT(LTRIM(TO_CHAR(b.Dekkingscode)),1) AND b.verzekerdezaakcode = 1
                 group by q.InternPolisnummer
                 ) at on at.internpolisnummer = p.internpolisnummer

left outer join (select distinct q.InternPolisnummer
                , min(b.dekkingscode) as dekkingscode
                , min(b.internpolisnummerdekkingsnummer) AS internpolisnummerdekkingsnummer
                from dwh.pub.polisversie q
                left outer join dwh.pub.dekking b
                ON q.InternPolisnummer = b.InternPolisnummer
                group by q.InternPolisnummer
                ) at2 ON p.InternPolisnummer = at2.internpolisnummer

left outer join pub.dekking ato on ato.internpolisnummerdekkingsnummer = at.internpolisnummerdekkingsnummer and ato.verzekerdezaakcode = 1
left outer join pub.dekking ato2 on ato2.internpolisnummerdekkingsnummer = at2.internpolisnummerdekkingsnummer and ato2.verzekerdezaakcode = 1

/* Treatment of Turnover end here **************************************************************************************************/

left outer join pub.dekking afte on afte.internpolisnummerdekkingsnummer = at.internpolisnummerdekkingsnummer and afte.verzekerdezaakcode = 2

union all

select distinct p.idpolis                                      as "KeyIdPolis"
, 'Rotterdam' 			              		       as "AdministrationOfficeCode"
, ''							       as "AssuredAddressArea"
, r.woonplaats                                                 as "AssuredAddressCity"
, r.adres                                                      as "AssuredAddressStreet"
, case when ato.aantaleenheden is null then
                case when ato2.aantaleenheden is null then 0 else ato2.aantaleenheden end
        when ato.aantaleenheden = 0 then
                case when ato2.aantaleenheden is null then 0 else ato2.aantaleenheden end
        else ato.aantaleenheden
        end as "AssAnnTo"
, case when ato.aantaleenheden is null then
                case when ato2.aantaleenheden is null then '' else 'EURO' end
        when ato.aantaleenheden = 0 then
                case when ato2.aantaleenheden is null then '' else 'EURO' end
        else 'EURO'
        end as "AssAnnToCur"

, p.relatienummer																							as "AssuredCode"
, r.landcode																									as "AssuredCountry"
, replace(r.naam, '|', '[') + ' ' + replace(r.naam3, '|', '[') as "AssuredFullName"
, case when afte.aantaleenheden is null then 0 else afte.aantaleenheden end as "AssNrOfFTE"
, r.postcode                                                  as "AssuredPostCode"
, ''                                                          as "AssuredProvince"
, ''                                                          as "AssuredShortName"
, ''                                                          as "AssuredState"
, ''                                                          as "AssuredTerritory"
, ''                                                          as "BrokerAddressArea"
, t.woonplaats                                                as "BrokerAddressCity"
, t.adres                                                     as "BrokerAddressStreet"
, p.tussenpersoonnummer                                       as "BrokerCode"
, t.landcode                                                  as "BrokerCountry"
, replace(t.zoeksleutel, '|', '[')                            as "BrokerFullName"
, tv.rubriek1                                                 as "BrokerGroupCode"
, tvo.omschrijving                                            as "BrokerGroupName"
, t.postcode                                                  as "BrokerPostcode"
, ''                                                          as "BrokerProvince"
, ''                                                          as "BrokerShortName"
, 'CM'                                                        as "ClaimsBasisCode"
, 'CLAIMS MADE'                                               as "ClaimsBasisDescription"
, ''                                                          as "CoverholderCode"
, ''                                                          as "CoverholderName"
, tb.omschrijving                                             as "CustomerClassification"
, ab.bedrijfscode                                             as "CustomerClassificationCode"
, ''                                                          as "DistributionPartner"
, 'BROKER'                                                    as "DistributionType"
, case when p.expiratiedatum is not null and p.soortpolis <> 0 then p.expiratiedatum else p.contractvervaldatum end as "ExpiryDate"
, ''                                                          as "IBCIndustryCode"
, ifnull(p.histhoofdpremievervaldatum, p.ingangsdatum)        as "InceptionDate"
, case when p.maatschappijnummer = 'M001' then 'MIICL' else case when p.maatschappijnummer = 'M002' then 'MISE' else '' end end as "InsurerEntityCode"
, case when p.maatschappijnummer = 'M001' then 'MARKEL INTERNATIONAL INSURANCE COMPANY LIMITED' else
  case when p.maatschappijnummer = 'M002' then 'MARKEL INTERNATIONAL SOCIETAS EUROPAEA' else '' end end as "InsurerEntityDescription"
, case when p.mutatiereden >= 4200 and p.mutatiereden <= 4400 then ltrim(to_char(p.mutatiereden)) else '' end as "LapsedReason"
, case when p.productcode in (160, 162, 164, 166, 168, 170, 172, 174, 260, 360) then 'EXCEDENT' else 'PRIMARY' end as "Layer"
, ''                                                          as "OrderPercentage"
, 'N'                                                         as "OutwardsFACIndicator"
, p1.naam                                                     as "PeerReview1"
, p1.medewerkernummer                                         as "PeerReview1Code"
, ltrim(p1.tekst1) + ' ' + ltrim(p1.tekst2) + ' ' + ltrim(p1.tekst3) + ' ' + ltrim(p1.tekst4) + ' ' + ltrim(p1.tekst5) + ' '
  + ltrim(p1.tekst6) + ' ' + ltrim(p1.tekst7) + ' ' + ltrim(p1.tekst8) + ' ' + ltrim(p1.tekst9) + ' ' + ltrim(p1.tekst10) + ' '
  + ltrim(p1.tekst11) + ' ' + ltrim(p1.tekst12) + ' ' + ltrim(p1.tekst13) + ' ' + ltrim(p1.tekst14) + ' ' + ltrim(p1.tekst15) as "PeerReview1Comment"
, p1.aanmaakdatum                                             as "PeerReview1Date"
, case when p1.assur_recid = p2.assur_recid then '' else p2.naam end as "PeerReview2"
, case when p1.assur_recid = p2.assur_recid then null else p2.medewerkernummer end as "PeerReview2Code"
, case when p1.assur_recid = p2.assur_recid then '' else ltrim(p2.tekst1) + ' ' + ltrim(p2.tekst2) + ' ' + ltrim(p2.tekst3) + ' ' + ltrim(p2.tekst4) + ' ' + ltrim(p2.tekst5) + ' '
  + ltrim(p2.tekst6) + ' ' + ltrim(p2.tekst7) + ' ' + ltrim(p2.tekst8) + ' ' + ltrim(p2.tekst9) + ' ' + ltrim(p2.tekst10) + ' '
  + ltrim(p2.tekst11) + ' ' + ltrim(p2.tekst12) + ' ' + ltrim(p2.tekst13) + ' ' + ltrim(p2.tekst14) + ' ' + ltrim(p2.tekst15) end as "PeerReview2Comment"
, case when p1.assur_recid = p2.assur_recid then NULL else p2.aanmaakdatum end as "PeerReview2Date"
, case when p.productcode = 950 then 'BINDER' else 'OPEN MARKET' end as "PlacementType"
, p.internpolisnummer + '/' + ltrim(to_char(year(ifnull(p.histhoofdpremievervaldatum, p.ingangsdatum)))) as "PolicyCode"
, p.Mutatiedatum                                              as "PolicyLastModifiedDate"
, p.productcode                                               as "PolicyProductCode"
, tp.omschrijving                                             as "PolicyProductDescription"
, p.polisnummer                                               as "PolicyReference"
, case when substring(p.internpolisnummer,1,6) = 'MARKEL' and p.aanmaakdatum = to_date('5/31/2017') then p.polisnummer else '' end as "PreviousPolicyReference"
, case when substring(p.internpolisnummer,1,6) = 'MARKEL' and p.aanmaakdatum = to_date('5/31/2017') then 'SCS' else '' end as "PreviousSourceSystem"
, case when substring(p.internpolisnummer,1,6) = 'MARKEL' and p.aanmaakdatum = to_date('5/31/2017') then 'SCS' else '' end as "PreviousSourceSystemDescription"
, 'Rotterdam'                                                 as "ProducingOfficeCode"
, ''                                                          as "QuotationReference"
, p.aanbrengendproducent                                      as "ReferralUnderwriter"
, ''                                                          as "ReinsurancePolicyIndicator"
, ''                                                          as "ReinsuranceReference"
, ''                                                          as "ReinsuredAddressArea"
, ''                                                          as "ReinsuredAddressCity"
, ''                                                          as "ReinsuredAddressStreet"
, ''                                                          as "ReinsuredAnnualTurnover"
, ''                                                          as "ReinsuredAnnualTurnoverCurrency"
, ''                                                          as "ReinsuredCode"
, ''                                                          as "ReinsuredCountry"
, ''                                                          as "ReinsuredFullName"
, ''                                                          as "ReinsuredNumberOfFTEmployees"
, ''                                                          as "ReinsuredPostcode"
, ''                                                          as "ReinsuredProvince"
, ''                                                          as "ReinsuredShortName"
, ''                                                          as "ReinsuredState"
, ''                                                          as "ReinsuredTerritory"
, ''                                                          as "RiskCode"
, p.mutatiereden                                              as "StatusCode"
, tm.omschrijving                                             as "StatusDescription"
, case when p.soortpolis = 0 then 'Y' else 'N' end            as "TacitRenewalIndicator"
, 'NHT'                                                       as "TerrorismCode"
, 'The Netherlands'                                           as "Timezone"
, ''                                                          as "TrustFund"
, o.rubriek2                                                  as "UnderlyingLimit"
, 'EURO'                                                      as "UnderlyingLimitCurrency"
, p.producent                                                 as "UnderwriterCode"
, tmw.naam                                                    as "UnderwriterName"
, case when p.aanmaakdatum = to_date('2017/05/31') then ''
  else ltrim(to_char(year(p.creatiedatum))) + '-' + ltrim(to_char(month(p.creatiedatum)))
  + '-' + ltrim(to_char(dayofmonth(p.creatiedatum))) end      as "WrittenDate"
, year(ifnull(p.histhoofdpremievervaldatum, p.ingangsdatum))  as "YearOfAccount"
from pub.histpolisversie p
inner join pub.relatie r on r.relatienummer = p.relatienummer
inner join pub.polisversie a on a.internpolisnummer = p.internpolisnummer
left outer join pub.tussenpersoon t on t.bedrijftussenpersoonnummer = p.bedrijftussenpersoonnummer
left outer join pub.tussenpersoonvrij tv on tv.bedrijftussenpersoonnummer = p.bedrijftussenpersoonnummer
left outer join pub.tabelvrijerubriektabel tvo on tvo.soortvrijerubriek = 'T' and
                                                  tvo.volgnummerrubriek = 1 and
                                                  tvo.tabelcode = tv.rubriek1
left outer join pub.histab ab on to_char(ab.idpolisobjectnummer) = lpad(ltrim(to_char(p.idpolis)),10,'0') + '/**********'
left outer join pub.tabelbedrijf tb on tb.bedrijfscode = ab.bedrijfscode
left outer join pub.tabelproduct tp on tp.soortverzekeringproductcode = p.soortverzekeringproductcode
left outer join pub.tabelmutatieredenpolis tm on tm.mutatiereden = p.mutatiereden
left outer join pub.histobjectvrij o on o.idpolisobjectnummer =  lpad(ltrim(to_char(p.idpolis)), 10, '0') + '/**********'
left outer join pub.tabelmedewerker tmw on tmw.bedrijfmedewerkernummer = p.bedrijfproducent
left outer join (select a.internpolisnummer, c.naam, b.medewerkernummer, b.aanmaakdatum,
								 b.tekst1, b.tekst2, b.tekst3, b.tekst4, b.tekst5, b.tekst6, b.tekst7, b.tekst8,
								 b.tekst9, b.tekst10, b.tekst11, b.tekst12, b.tekst13, b.tekst14, b.tekst15, a.assur_recid
								 from (select a.internpolisnummer, a.memocode, min(a.assur_recid) as assur_recid from pub.memopolisversie a group by a.internpolisnummer, a.memocode having a.memocode = 'PR') a
                                         left outer join pub.memopolisversie b on b.assur_recid = a.assur_recid
                                         left outer join pub.tabelmedewerker c on c.medewerkernummer = b.medewerkernummer) p1 on p1.internpolisnummer = p.internpolisnummer
left outer join (select a.internpolisnummer, c.naam, b.medewerkernummer, b.aanmaakdatum,
								 b.tekst1, b.tekst2, b.tekst3, b.tekst4, b.tekst5, b.tekst6, b.tekst7, b.tekst8,
								 b.tekst9, b.tekst10, b.tekst11, b.tekst12, b.tekst13, b.tekst14, b.tekst15, a.assur_recid
								 from (select a.internpolisnummer, a.memocode, max(a.assur_recid) as assur_recid from pub.memopolisversie a group by a.internpolisnummer, a.memocode having a.memocode = 'PR') a
                                         left outer join pub.memopolisversie b on b.assur_recid = a.assur_recid
                                         left outer join pub.tabelmedewerker c on c.medewerkernummer = b.medewerkernummer) p2 on p2.internpolisnummer = p.internpolisnummer

/* Treatment of Turnover starts here ************************************************************************************************/
left outer join (select distinct q.IdPolis, min(b.idpolisdekkingsnummer) as idpolisdekkingsnummer
                 from dwh.pub.histpolisversie q
                 left outer join dwh.pub.histdekking b
                 ON q.IdPolis = b.IdPolis
                 WHERE LEFT(LTRIM(TO_CHAR(q.Productcode)),1) = LEFT(LTRIM(TO_CHAR(b.Dekkingscode)),1) AND b.verzekerdezaakcode = 1
                 group by q.IdPolis) at on at.idpolis = p.idpolis

left outer join (select top 10 q.IdPolis, min(b.dekkingscode) as dekkingscode, min(b.idpolisdekkingsnummer) AS idpolisdekkingsnummer
                 from dwh.pub.histpolisversie q
                 left outer join dwh.pub.histdekking b
                 ON q.IdPolis = b.IdPolis
                 group by q.IdPolis
                ) at2 ON p.idpolis = at2.idpolis

left outer join pub.histdekking ato on ato.idpolisdekkingsnummer = at.idpolisdekkingsnummer and ato.verzekerdezaakcode = 1
left outer join pub.histdekking ato2 on ato2.idpolisdekkingsnummer = at2.idpolisdekkingsnummer and ato2.verzekerdezaakcode = 1
/* Treatment of Turnover ends here **************************************************************************************************/


left outer join pub.histdekking afte on afte.idpolisdekkingsnummer = at.idpolisdekkingsnummer and afte.verzekerdezaakcode = 2

where p.internschadenummer = 0

/* add additional section for policy version related to claims
         - note the change from inner join to left join for relatie and polisversie tables
         - also criteria statement has changed to p.internschadenummer <> 0
*/
union all

select distinct p.idpolis                                      as "KeyIdPolis"
, 'Rotterdam' 			              		       as "AdministrationOfficeCode"
, ''							       as "AssuredAddressArea"
, r.woonplaats                                                 as "AssuredAddressCity"
, r.adres                                                      as "AssuredAddressStreet"
, case when ato.aantaleenheden is null then
                case when ato2.aantaleenheden is null then 0 else ato2.aantaleenheden end
        when ato.aantaleenheden = 0 then
                case when ato2.aantaleenheden is null then 0 else ato2.aantaleenheden end
        else ato.aantaleenheden
        end as "AssAnnTo"
, case when ato.aantaleenheden is null then
                case when ato2.aantaleenheden is null then '' else 'EURO' end
        when ato.aantaleenheden = 0 then
                case when ato2.aantaleenheden is null then '' else 'EURO' end
        else 'EURO'
        end as "AssAnnToCur"

, p.relatienummer																							as "AssuredCode"
, r.landcode																									as "AssuredCountry"
, replace(r.naam, '|', '[') + ' ' + replace(r.naam3, '|', '[') as "AssuredFullName"
, case when afte.aantaleenheden is null then 0 else afte.aantaleenheden end as "AssNrOfFTE"
, r.postcode                                                  as "AssuredPostCode"
, ''                                                          as "AssuredProvince"
, ''                                                          as "AssuredShortName"
, ''                                                          as "AssuredState"
, ''                                                          as "AssuredTerritory"
, ''                                                          as "BrokerAddressArea"
, t.woonplaats                                                as "BrokerAddressCity"
, t.adres                                                     as "BrokerAddressStreet"
, p.tussenpersoonnummer                                       as "BrokerCode"
, t.landcode                                                  as "BrokerCountry"
, replace(t.zoeksleutel, '|', '[')                            as "BrokerFullName"
, tv.rubriek1                                                 as "BrokerGroupCode"
, tvo.omschrijving                                            as "BrokerGroupName"
, t.postcode                                                  as "BrokerPostcode"
, ''                                                          as "BrokerProvince"
, ''                                                          as "BrokerShortName"
, 'CM'                                                        as "ClaimsBasisCode"
, 'CLAIMS MADE'                                               as "ClaimsBasisDescription"
, ''                                                          as "CoverholderCode"
, ''                                                          as "CoverholderName"
, tb.omschrijving                                             as "CustomerClassification"
, ab.bedrijfscode                                             as "CustomerClassificationCode"
, ''                                                          as "DistributionPartner"
, 'BROKER'                                                    as "DistributionType"
, case when p.expiratiedatum is not null and p.soortpolis <> 0 then p.expiratiedatum else p.contractvervaldatum end as "ExpiryDate"
, ''                                                          as "IBCIndustryCode"
, ifnull(p.histhoofdpremievervaldatum, p.ingangsdatum)        as "InceptionDate"
, case when p.maatschappijnummer = 'M001' then 'MIICL' else case when p.maatschappijnummer = 'M002' then 'MISE' else '' end end as "InsurerEntityCode"
, case when p.maatschappijnummer = 'M001' then 'MARKEL INTERNATIONAL INSURANCE COMPANY LIMITED' else
  case when p.maatschappijnummer = 'M002' then 'MARKEL INTERNATIONAL SOCIETAS EUROPAEA' else '' end end as "InsurerEntityDescription"
, case when p.mutatiereden >= 4200 and p.mutatiereden <= 4400 then ltrim(to_char(p.mutatiereden)) else '' end as "LapsedReason"
, case when p.productcode in (160, 162, 164, 166, 168, 170, 172, 174, 260, 360) then 'EXCEDENT' else 'PRIMARY' end as "Layer"
, ''                                                          as "OrderPercentage"
, 'N'                                                         as "OutwardsFACIndicator"
, p1.naam                                                     as "PeerReview1"
, p1.medewerkernummer                                         as "PeerReview1Code"
, ltrim(p1.tekst1) + ' ' + ltrim(p1.tekst2) + ' ' + ltrim(p1.tekst3) + ' ' + ltrim(p1.tekst4) + ' ' + ltrim(p1.tekst5) + ' '
  + ltrim(p1.tekst6) + ' ' + ltrim(p1.tekst7) + ' ' + ltrim(p1.tekst8) + ' ' + ltrim(p1.tekst9) + ' ' + ltrim(p1.tekst10) + ' '
  + ltrim(p1.tekst11) + ' ' + ltrim(p1.tekst12) + ' ' + ltrim(p1.tekst13) + ' ' + ltrim(p1.tekst14) + ' ' + ltrim(p1.tekst15) as "PeerReview1Comment"
, p1.aanmaakdatum                                             as "PeerReview1Date"
, case when p1.assur_recid = p2.assur_recid then '' else p2.naam end as "PeerReview2"
, case when p1.assur_recid = p2.assur_recid then null else p2.medewerkernummer end as "PeerReview2Code"
, case when p1.assur_recid = p2.assur_recid then '' else ltrim(p2.tekst1) + ' ' + ltrim(p2.tekst2) + ' ' + ltrim(p2.tekst3) + ' ' + ltrim(p2.tekst4) + ' ' + ltrim(p2.tekst5) + ' '
  + ltrim(p2.tekst6) + ' ' + ltrim(p2.tekst7) + ' ' + ltrim(p2.tekst8) + ' ' + ltrim(p2.tekst9) + ' ' + ltrim(p2.tekst10) + ' '
  + ltrim(p2.tekst11) + ' ' + ltrim(p2.tekst12) + ' ' + ltrim(p2.tekst13) + ' ' + ltrim(p2.tekst14) + ' ' + ltrim(p2.tekst15) end as "PeerReview2Comment"
, case when p1.assur_recid = p2.assur_recid then NULL else p2.aanmaakdatum end as "PeerReview2Date"
, case when p.productcode = 950 then 'BINDER' else 'OPEN MARKET' end as "PlacementType"
, p.internpolisnummer + '/' + ltrim(to_char(year(ifnull(p.histhoofdpremievervaldatum, p.ingangsdatum)))) as "PolicyCode"
, p.Mutatiedatum                                              as "PolicyLastModifiedDate"
, p.productcode                                               as "PolicyProductCode"
, tp.omschrijving                                             as "PolicyProductDescription"
, p.polisnummer                                               as "PolicyReference"
, case when substring(p.internpolisnummer,1,6) = 'MARKEL' and p.aanmaakdatum = to_date('5/31/2017') then p.polisnummer else '' end as "PreviousPolicyReference"
, case when substring(p.internpolisnummer,1,6) = 'MARKEL' and p.aanmaakdatum = to_date('5/31/2017') then 'SCS' else '' end as "PreviousSourceSystem"
, case when substring(p.internpolisnummer,1,6) = 'MARKEL' and p.aanmaakdatum = to_date('5/31/2017') then 'SCS' else '' end as "PreviousSourceSystemDescription"
, 'Rotterdam'                                                 as "ProducingOfficeCode"
, ''                                                          as "QuotationReference"
, p.aanbrengendproducent                                      as "ReferralUnderwriter"
, ''                                                          as "ReinsurancePolicyIndicator"
, ''                                                          as "ReinsuranceReference"
, ''                                                          as "ReinsuredAddressArea"
, ''                                                          as "ReinsuredAddressCity"
, ''                                                          as "ReinsuredAddressStreet"
, ''                                                          as "ReinsuredAnnualTurnover"
, ''                                                          as "ReinsuredAnnualTurnoverCurrency"
, ''                                                          as "ReinsuredCode"
, ''                                                          as "ReinsuredCountry"
, ''                                                          as "ReinsuredFullName"
, ''                                                          as "ReinsuredNumberOfFTEmployees"
, ''                                                          as "ReinsuredPostcode"
, ''                                                          as "ReinsuredProvince"
, ''                                                          as "ReinsuredShortName"
, ''                                                          as "ReinsuredState"
, ''                                                          as "ReinsuredTerritory"
, ''                                                          as "RiskCode"
, p.mutatiereden                                              as "StatusCode"
, tm.omschrijving                                             as "StatusDescription"
, case when p.soortpolis = 0 then 'Y' else 'N' end            as "TacitRenewalIndicator"
, 'NHT'                                                       as "TerrorismCode"
, 'The Netherlands'                                           as "Timezone"
, ''                                                          as "TrustFund"
, o.rubriek2                                                  as "UnderlyingLimit"
, 'EURO'                                                      as "UnderlyingLimitCurrency"
, p.producent                                                 as "UnderwriterCode"
, tmw.naam                                                    as "UnderwriterName"
, case when p.aanmaakdatum = to_date('2017/05/31') then ''
  else ltrim(to_char(year(p.creatiedatum))) + '-' + ltrim(to_char(month(p.creatiedatum)))
  + '-' + ltrim(to_char(dayofmonth(p.creatiedatum))) end      as "WrittenDate"
, year(ifnull(p.histhoofdpremievervaldatum, p.ingangsdatum))  as "YearOfAccount"
from pub.histpolisversie p
left join pub.relatie r on r.relatienummer = p.relatienummer
left join pub.polisversie a on a.internpolisnummer = p.internpolisnummer
left outer join pub.tussenpersoon t on t.bedrijftussenpersoonnummer = p.bedrijftussenpersoonnummer
left outer join pub.tussenpersoonvrij tv on tv.bedrijftussenpersoonnummer = p.bedrijftussenpersoonnummer
left outer join pub.tabelvrijerubriektabel tvo on tvo.soortvrijerubriek = 'T' and
                                                  tvo.volgnummerrubriek = 1 and
                                                  tvo.tabelcode = tv.rubriek1
left outer join pub.histab ab on to_char(ab.idpolisobjectnummer) = lpad(ltrim(to_char(p.idpolis)),10,'0') + '/**********'
left outer join pub.tabelbedrijf tb on tb.bedrijfscode = ab.bedrijfscode
left outer join pub.tabelproduct tp on tp.soortverzekeringproductcode = p.soortverzekeringproductcode
left outer join pub.tabelmutatieredenpolis tm on tm.mutatiereden = p.mutatiereden
left outer join pub.histobjectvrij o on o.idpolisobjectnummer =  lpad(ltrim(to_char(p.idpolis)), 10, '0') + '/**********'
left outer join pub.tabelmedewerker tmw on tmw.bedrijfmedewerkernummer = p.bedrijfproducent
left outer join (select a.internpolisnummer, c.naam, b.medewerkernummer, b.aanmaakdatum,
								 b.tekst1, b.tekst2, b.tekst3, b.tekst4, b.tekst5, b.tekst6, b.tekst7, b.tekst8,
								 b.tekst9, b.tekst10, b.tekst11, b.tekst12, b.tekst13, b.tekst14, b.tekst15, a.assur_recid
								 from (select a.internpolisnummer, a.memocode, min(a.assur_recid) as assur_recid from pub.memopolisversie a group by a.internpolisnummer, a.memocode having a.memocode = 'PR') a
                                         left outer join pub.memopolisversie b on b.assur_recid = a.assur_recid
                                         left outer join pub.tabelmedewerker c on c.medewerkernummer = b.medewerkernummer) p1 on p1.internpolisnummer = p.internpolisnummer
left outer join (select a.internpolisnummer, c.naam, b.medewerkernummer, b.aanmaakdatum,
								 b.tekst1, b.tekst2, b.tekst3, b.tekst4, b.tekst5, b.tekst6, b.tekst7, b.tekst8,
								 b.tekst9, b.tekst10, b.tekst11, b.tekst12, b.tekst13, b.tekst14, b.tekst15, a.assur_recid
								 from (select a.internpolisnummer, a.memocode, max(a.assur_recid) as assur_recid from pub.memopolisversie a group by a.internpolisnummer, a.memocode having a.memocode = 'PR') a
                                         left outer join pub.memopolisversie b on b.assur_recid = a.assur_recid
                                         left outer join pub.tabelmedewerker c on c.medewerkernummer = b.medewerkernummer) p2 on p2.internpolisnummer = p.internpolisnummer

/* Treatment of Turnover starts here ************************************************************************************************/
left outer join (select distinct q.IdPolis, min(b.idpolisdekkingsnummer) as idpolisdekkingsnummer
                 from dwh.pub.histpolisversie q
                 left outer join dwh.pub.histdekking b
                 ON q.IdPolis = b.IdPolis
                 WHERE LEFT(LTRIM(TO_CHAR(q.Productcode)),1) = LEFT(LTRIM(TO_CHAR(b.Dekkingscode)),1) AND b.verzekerdezaakcode = 1
                 group by q.IdPolis) at on at.idpolis = p.idpolis

left outer join (select top 10 q.IdPolis, min(b.dekkingscode) as dekkingscode, min(b.idpolisdekkingsnummer) AS idpolisdekkingsnummer
                 from dwh.pub.histpolisversie q
                 left outer join dwh.pub.histdekking b
                 ON q.IdPolis = b.IdPolis
                 group by q.IdPolis
                ) at2 ON p.idpolis = at2.idpolis

left outer join pub.histdekking ato on ato.idpolisdekkingsnummer = at.idpolisdekkingsnummer and ato.verzekerdezaakcode = 1
left outer join pub.histdekking ato2 on ato2.idpolisdekkingsnummer = at2.idpolisdekkingsnummer and ato2.verzekerdezaakcode = 1
/* Treatment of Turnover ends here **************************************************************************************************/


left outer join pub.histdekking afte on afte.idpolisdekkingsnummer = at.idpolisdekkingsnummer and afte.verzekerdezaakcode = 2

where p.internschadenummer <> 0


) m1

/* New treatment of RenewalPolicyIndicator, Reference and Sequence number starts here */
left outer join (
        select  T3.InternPolisnummer, T3.yoa
                , T3.internpolisnummer + '/' + ltrim(to_char(T3.yoa)) AS 'PolicyCode'
                , T3.IdPolis, T4.mutatiereden
                , case when substr(ltrim(to_char(T4.mutatiereden)),1,1) = '1'
                        Then 'New'
                        Else case when T5.RenewalCheck = 'New' then 'New' else 'Renewal' end
                  END As RenewalPolicyIndicator
                , case when substr(ltrim(to_char(T4.mutatiereden)),1,1) = '1'
                        Then '0'
                        Else case when T5.RenewalCheck = 'New' then '0' else T3.internpolisnummer + '/' + ltrim(to_char(T3.yoa-1)) end
                  END As RenewalPolicyReference
                , case when t8.max_idpolis is NULL then '0' else ltrim(to_char(t8.max_idpolis)) end as 'RenewalSequenceNumber'
        from ((select  T1.*, T2.idpolis from (
                        select  a.internpolisnummer
                                , year(ifnull(a.histhoofdpremievervaldatum, a.ingangsdatum)) as yoa
                                , min(a.mutatiedatum) as mutatiedatum

                        from (
                                select pv.internpolisnummer, 0 as internschadenummer, pv.polisnummer, pv.idpolis, pv.mutatiereden, pv.histhoofdpremievervaldatum, pv.ingangsdatum, pv.mutatiedatum from pub.polisversie pv
                                union all
                                select hpv.internpolisnummer, hpv.internschadenummer, hpv.polisnummer, hpv.idpolis, hpv.mutatiereden, hpv.histhoofdpremievervaldatum, hpv.ingangsdatum, hpv.mutatiedatum from pub.histpolisversie hpv
                        ) a
                        where a.internschadenummer = 0
                        group by a.internpolisnummer, year(ifnull(a.histhoofdpremievervaldatum, a.ingangsdatum))) T1

                left outer join (
                        select  a.internpolisnummer
                                , year(ifnull(a.histhoofdpremievervaldatum, a.ingangsdatum)) as yoa
                                , a.mutatiedatum
                                , min(a.idpolis) as idpolis
                        from (
                                select pv.internpolisnummer, 0 as internschadenummer, pv.polisnummer, pv.idpolis, pv.mutatiereden, pv.histhoofdpremievervaldatum, pv.ingangsdatum, pv.mutatiedatum from pub.polisversie pv
                                union all
                                select hpv.internpolisnummer, hpv.internschadenummer, hpv.polisnummer, hpv.idpolis, hpv.mutatiereden, hpv.histhoofdpremievervaldatum, hpv.ingangsdatum, hpv.mutatiedatum from pub.histpolisversie hpv
                        ) a
                        where a.internschadenummer = 0
                        group by a.internpolisnummer, year(ifnull(a.histhoofdpremievervaldatum, a.ingangsdatum)), a.mutatiedatum
                ) T2
                ON T1.internpolisnummer = T2.internpolisnummer
                AND T1.yoa = T2.yoa
                AND T1.mutatiedatum = T2.mutatiedatum)
        ) T3

        left outer join (
                select  a.internpolisnummer
                        , year(ifnull(a.histhoofdpremievervaldatum, a.ingangsdatum)) as yoa
                        , a.idpolis
                        , a.mutatiereden
                from (
                        select pv.internpolisnummer, 0 as internschadenummer, pv.idpolis, pv.mutatiereden, pv.histhoofdpremievervaldatum
                                , pv.ingangsdatum, pv.mutatiereden from pub.polisversie pv
                        union all
                        select hpv.internpolisnummer, hpv.internschadenummer, hpv.idpolis, hpv.mutatiereden, hpv.histhoofdpremievervaldatum
                                , hpv.ingangsdatum, hpv.mutatiereden
                        from pub.histpolisversie hpv
                        where hpv.internschadenummer = 0
                ) a
        ) T4
        ON T3.IdPolis = T4.IdPolis

        left outer join (
                select  k4.internpolisnummer, k4.yoa
                        , case when sum(k4.count_pols) is not null then 'Renewal' else 'New' end as 'RenewalCheck'
                from (
                        select k2.internpolisnummer, k2.yoa, k3.count_pols
                        from (
                                select pv.internpolisnummer, year(ifnull(pv.histhoofdpremievervaldatum, pv.ingangsdatum)) as yoa from pub.polisversie pv
                                union all
                                select hpv.internpolisnummer, year(ifnull(hpv.histhoofdpremievervaldatum, hpv.ingangsdatum)) as yoa from pub.histpolisversie hpv
                                where hpv.internschadenummer = 0
                        ) k2
                        left outer join (
                                select k1.internpolisnummer, k1.yoa, count(k1.internpolisnummer) as count_pols
                                from (
                                        select pv.internpolisnummer, year(ifnull(pv.histhoofdpremievervaldatum, pv.ingangsdatum)) as yoa from pub.polisversie pv
                                        union all
                                        select hpv.internpolisnummer, year(ifnull(hpv.histhoofdpremievervaldatum, hpv.ingangsdatum)) as yoa from pub.histpolisversie hpv
                                        where hpv.internschadenummer = 0
                                ) k1
                                group by k1.internpolisnummer, k1.yoa
                        ) k3
                        ON k2.internpolisnummer = k3.internpolisnummer
                        and k2.yoa > k3.yoa
                ) k4
                group by k4.internpolisnummer, k4.yoa
        ) T5
        ON T3.InternPolisnummer = T5.Internpolisnummer
        AND T3.YOA = T5.YOA

        LEFT OUTER JOIN (
                select  to_char(t6.internpolisnummer) as internpolisnummer
                        , t6.yoa, t7.max_idpolis
                from (
                        select  a5.internpolisnummer
                                , year(ifnull(a5.histhoofdpremievervaldatum, a5.ingangsdatum)) as yoa
                                , max(a5.mutatiedatum) as max_mutatiedatum
                        from (
                                select pv.internpolisnummer, 0 as internschadenummer, pv.histhoofdpremievervaldatum, pv.ingangsdatum, pv.mutatiedatum from pub.polisversie pv
                                union all
                                select hpv.internpolisnummer, hpv.internschadenummer, hpv.histhoofdpremievervaldatum, hpv.ingangsdatum, hpv.mutatiedatum
                                from pub.histpolisversie hpv
                                where hpv.internschadenummer = 0
                        ) a5
                        group by a5.internpolisnummer, year(ifnull(a5.histhoofdpremievervaldatum, a5.ingangsdatum))
                ) t6
                left outer join (
                        select  a6.internpolisnummer
                                , year(ifnull(a6.histhoofdpremievervaldatum, a6.ingangsdatum)) as yoa
                                , a6.mutatiedatum as eff_md
                                , max(idpolis) as max_idpolis
                        from (
                                select pv.internpolisnummer, 0 as internschadenummer, pv.idpolis, pv.histhoofdpremievervaldatum, pv.ingangsdatum, pv.mutatiedatum from pub.polisversie pv
                                union all
                                select hpv.internpolisnummer, hpv.internschadenummer, hpv.idpolis, hpv.histhoofdpremievervaldatum, hpv.ingangsdatum, hpv.mutatiedatum
                                from pub.histpolisversie hpv
                                where hpv.internschadenummer = 0
                        ) a6
                        group by a6.internpolisnummer, year(ifnull(a6.histhoofdpremievervaldatum, a6.ingangsdatum)), a6.mutatiedatum
                ) t7
                ON (t6.internpolisnummer = t7.internpolisnummer
                AND t6.yoa = t7.yoa
                AND t6.max_mutatiedatum = t7.eff_md)
        ) t8
        ON T3.InternPolisnummer = T8.Internpolisnummer
        AND T3.yoa = T8.yoa + 1
) t9
ON m1.PolicyCode = t9.PolicyCode

/* New treatment of MainActivityCode starts here */
LEFT OUTER JOIN (

        SELECT DISTINCT ex1.*
        , CASE WHEN ex2.omschrijving IS NULL THEN ''
                ELSE ex2.omschrijving
          END AS 'AssuredMainActivityDescription'

FROM (

SELECT DISTINCT
    /*
    This part of the query innerjoins the polisnummer information (in the second part) to
    idpolisinformation
    in order to find th tradecode corresponding to the maximum idpolis per polisnumer (with
    minimum dekkingsnummer)
    */
    gld.internpolisnummer ,
    --gld.polisnummer,
    --gld.productcode ,
    --gld.idpolis ,
    --gld.dekkingsnummer ,
    --gld.tradecode ,
    --smd.max_tradecode ,
    --smd.min_tradecode , --this will be empty if there is at list one empty value over all idpolis

    CASE WHEN gld.tradecode = ' ' THEN
        CASE
                WHEN smd.max_tradecode = '-2146826246' THEN ''
                WHEN smd.max_tradecode = '' THEN ''
                WHEN smd.max_tradecode IS NULL THEN ''
                ELSE UPPER(smd.max_tradecode)
        END
    ELSE
        CASE
                WHEN gld.tradecode = '-2146826246' THEN ''
                WHEN gld.tradecode = '' THEN ''
                WHEN gld.tradecode IS NULL THEN ''
                ELSE UPPER(gld.tradecode)
        END
    END AS AssuredMainActivityCode --a shortcut to replace empty tradecodevalues
FROM
  (
        /* This part picks the minimum dekking corresponding to each idpolis level */
        SELECT
            grp.internpolisnummer,
            grp.max_mutatiedatum AS mutatiedatum,
            grp.max_idpolis      AS idpolis,
            MAX(grp.tradecode)   AS max_tradecode,
            MIN(grp.tradecode)   AS min_tradecode
        FROM
            (
                SELECT
                    filt_d.internpolisnummer ,
                    filt_d.productcode ,
                    filt_d.idpolis ,
                    filt_d.mutatiedatum ,
                    filt_d.min_dekkingsnummer,
                    upn.max_mutatiedatum,
                    upi.max_idpolis,
                    t.tradecode
                FROM
                    (   /*This part of the query picks the minimum dekkingsnummer  associated
                    with each polis version */
                        SELECT
                            t.internpolisnummer ,
                            t.productcode ,
                            t.idpolis ,
                            t.mutatiedatum ,
                            MIN(t.dekkingsnummer) AS min_dekkingsnummer
                        FROM
                            (
                                /* This part of the query pulls  all relevant data at the most
                                granular regarding(dekking) information  of both current and historical policy
                                versions */
                                SELECT
                                    hpv.internpolisnummer ,
                                    hpv.productcode ,
                                    hdk.idpolis ,
                                    hdk.dekkingsnummer ,
                                    hdk.rubriek1                                      AS tradecode ,
                                    ifnull(hpv.histhoofdpremievervaldatum, hpv.ingangsdatum) AS
                                    doa ,
                                    hpv.mutatiedatum
                                FROM  pub.histpolisversie hpv
                                LEFT OUTER JOIN pub.histdekkingvrij hdk ON hdk.idpolis = hpv.idpolis
                                WHERE hpv.internschadenummer =0

                                UNION ALL

                                SELECT
                                    pv.internpolisnummer ,
                                    pv.productcode ,
                                    pv.idpolis ,
                                    dk.dekkingsnummer ,
                                    dk.rubriek1                                       AS tradecode ,
                                    ifnull(pv.histhoofdpremievervaldatum, pv.ingangsdatum) AS doa
                                    ,
                                    pv.mutatiedatum
                                FROM  pub.polisversie pv
                                LEFT OUTER JOIN  pub.dekkingvrij dk ON dk.internpolisnummer = pv.internpolisnummer
                                )t
                        GROUP BY
                            t.internpolisnummer ,
                            t.productcode ,
                            t.idpolis ,
                            t.mutatiedatum
                            )filt_d

                LEFT JOIN

                    (   /* This part of the query picks the maximum mutation date corresponding
                    to a policy */
                        SELECT
                            pil.internpolisnummer,
                            MAX(pil.mutatiedatum) AS max_mutatiedatum
                        FROM
                            (   /*All polisvrsies info */
                                SELECT
                                    hpv.internpolisnummer ,
                                    hpv.idpolis ,
                                    hpv.mutatiedatum
                                FROM pub.histpolisversie hpv
                                WHERE hpv.internschadenummer =0

                                UNION ALL

                                SELECT
                                    pv.internpolisnummer ,
                                    pv.idpolis ,
                                    pv.mutatiedatum
                                FROM pub.polisversie pv )pil
                        GROUP BY pil.internpolisnummer
                        )upn ON  filt_d.internpolisnummer = upn.internpolisnummer
                INNER JOIN
                    (   /*This part selects the maximum id_polis corresponding to each mutation date */
                        SELECT
                            pil.internpolisnummer,
                            pil.mutatiedatum,
                            MAX(pil.idpolis) AS max_idpolis
                        FROM
                            (   SELECT
                                    hpv.internpolisnummer ,
                                    hpv.idpolis ,
                                    hpv.mutatiedatum
                                FROM pub.histpolisversie hpv
                                WHERE hpv.internschadenummer =0

                                UNION ALL

                                SELECT
                                    pv.internpolisnummer ,
                                    pv.idpolis ,
                                    pv.mutatiedatum
                                FROM  pub.polisversie pv
                                )pil
                        GROUP BY
                            pil.internpolisnummer,
                            pil.mutatiedatum
                            ) upi  ON upi.internpolisnummer = upn.internpolisnummer AND upi.mutatiedatum = upn.max_mutatiedatum
                LEFT JOIN
                    (
                        /* This part of the query pulls  all relevant data at the most granular
                        regarding coverage
                        (dekking) information  of both current and historical policy versions */
                        SELECT
                            hpv.internpolisnummer ,
                            hpv.productcode ,
                            hdk.idpolis ,
                            hdk.dekkingsnummer ,
                            hdk.rubriek1                                             AS tradecode ,
                            ifnull(hpv.histhoofdpremievervaldatum, hpv.ingangsdatum) AS doa ,
                            hpv.mutatiedatum
                        FROM pub.histpolisversie hpv
                        LEFT OUTER JOIN pub.histdekkingvrij hdk  ON  hdk.idpolis = hpv.idpolis
                        WHERE  hpv.internschadenummer =0

                        UNION ALL

                        SELECT
                            pv.internpolisnummer ,
                            pv.productcode ,
                            pv.idpolis ,
                            dk.dekkingsnummer ,
                            dk.rubriek1                                            AS tradecode ,
                            ifnull(pv.histhoofdpremievervaldatum, pv.ingangsdatum) AS doa ,
                            pv.mutatiedatum
                        FROM  pub.polisversie pv
                        LEFT OUTER JOIN  pub.dekkingvrij dk ON dk.internpolisnummer = pv.internpolisnummer
                        )t  ON   filt_d.idpolis = t.idpolis  AND filt_d.min_dekkingsnummer = t.dekkingsnummer )grp
        GROUP BY
            grp.internpolisnummer,
            grp.max_mutatiedatum ,
            grp.max_idpolis
     )smd

LEFT JOIN


    (
        /* This part of the query looks up the tradecode corresponding to the minimum
        dekkingsnummer
        for each polisid */
        SELECT
            filt_d.internpolisnummer ,
            filt_d.polisnummer,
            filt_d.productcode ,
            filt_d.idpolis ,
            filt_d.mutatiedatum ,
            all_d.tradecode ,
            filt_d.dekkingsnummer
        FROM
            (
                /* This part picks the minimum dekking corresponding to each idpolis level */
                SELECT
                    t.internpolisnummer ,
                    t.polisnummer,
                    t.productcode ,
                    t.idpolis ,
                    t.mutatiedatum ,
                    MIN(t.dekkingsnummer) AS dekkingsnummer
                FROM
                    (
                        /* This part of the query pulls  all relevant data at the most granular
                        regarding coverage
                        (dekking) information  of both current and historical policy versions */
                        SELECT
                            hpv.internpolisnummer ,
                            hpv.polisnummer,
                            hpv.productcode ,
                            hdk.idpolis ,
                            hdk.dekkingsnummer ,
                            hdk.rubriek1                                             AS tradecode ,
                            ifnull(hpv.histhoofdpremievervaldatum, hpv.ingangsdatum) AS doa ,
                            hpv.mutatiedatum
                        FROM pub.histpolisversie hpv
                        LEFT OUTER JOIN  pub.histdekkingvrij hdk
                        ON hdk.idpolis = hpv.idpolis
                        WHERE  hpv.internschadenummer =0

                        UNION ALL

                        SELECT
                            pv.internpolisnummer ,
                            pv.polisnummer,
                            pv.productcode ,
                            pv.idpolis ,
                            dk.dekkingsnummer ,
                            dk.rubriek1                                            AS tradecode ,
                            ifnull(pv.histhoofdpremievervaldatum, pv.ingangsdatum) AS doa ,
                            pv.mutatiedatum
                        FROM  pub.polisversie pv
                        LEFT OUTER JOIN  pub.dekkingvrij dk ON dk.internpolisnummer = pv.internpolisnummer
                        )t
                GROUP BY
                    t.internpolisnummer ,
                    t.polisnummer,
                    t.productcode ,
                    t.idpolis ,
                    t.mutatiedatum ) filt_d
        LEFT JOIN
            (
                /* This part of the query pulls  all relevant data at the most granular regarding
                coverage
                (dekking) information  of both current and historical policy versions */
                SELECT
                    hpv.internpolisnummer ,
                    hpv.polisnummer,
                    hpv.productcode ,
                    hdk.idpolis ,
                    hdk.dekkingsnummer ,
                    hdk.rubriek1                                             AS tradecode ,
                    ifnull(hpv.histhoofdpremievervaldatum, hpv.ingangsdatum) AS doa ,
                    hpv.mutatiedatum
                FROM pub.histpolisversie hpv
                LEFT OUTER JOIN pub.histdekkingvrij hdk ON  hdk.idpolis = hpv.idpolis
                WHERE hpv.internschadenummer =0

                UNION ALL

                SELECT
                    pv.internpolisnummer ,
                    pv.polisnummer,
                    pv.productcode ,
                    pv.idpolis ,
                    dk.dekkingsnummer ,
                    dk.rubriek1                                            AS tradecode ,
                    ifnull(pv.histhoofdpremievervaldatum, pv.ingangsdatum) AS doa ,
                    pv.mutatiedatum
                FROM  pub.polisversie pv
                LEFT OUTER JOIN  pub.dekkingvrij dk ON  dk.internpolisnummer = pv.internpolisnummer
                ) all_d
                ON filt_d.idpolis = all_d.idpolis AND filt_d.dekkingsnummer = all_d.dekkingsnummer
                WHERE all_d.tradecode <> ''
        ) gld ON gld.internpolisnummer = smd.internpolisnummer  AND gld.idpolis = smd.idpolis
) ex1

LEFT OUTER JOIN (

        SELECT DISTINCT tc.code
        , CASE
                WHEN tc.code = 'NHC' THEN 'PI - Real estate broker LMV/Vastgoedpro'
                WHEN tc.code = 'NHE' THEN 'PI - Real estate broker RE/MAX'
                WHEN tc.code = 'NHO' THEN 'PI - DPO / FG'
                WHEN tc.code = 'NIV' THEN 'Energy bio-energy'
                WHEN tc.code = 'NJW' THEN 'PI - accountant audit non audit and consultancy pensions'
                WHEN tc.code = 'NKM' THEN 'Industry equipment instruments and other'
                WHEN tc.code = 'NKS' THEN 'Industry rubber ceramic concrete stone'
                ELSE td.omschrijving
          END AS omschrijving

        FROM (

                Select DISTINCT
                        ta.code
                FROM pub.tabelalgemeen ta
                WHERE ta.tabelcode LIKE '%MARKELTRADECODE%'
                ) tc

                LEFT OUTER JOIN (

                        Select DISTINCT
                                ta.code, LTRIM(RTRIM(REPLACE(REPLACE(REPLACE(REPLACE(ta.omschrijving,',',''),'(',''),')',''),'  ',' '))) AS omschrijving
                        FROM pub.tabelalgemeen ta
                        WHERE ta.tabelcode LIKE '%MARKELTRADECODE%'

                ) td
                ON tc.code = td.code
                ORDER BY tc.code

) ex2
ON ex1.AssuredMainActivityCode = ex2.code

) t10
ON LEFT(m1.PolicyCode,LENGTH(m1.PolicyCode)-5) = t10.InternPolisnummer  /*Have to join on InternPolisnummer without the YOA as Polisnummer is not unique*/
