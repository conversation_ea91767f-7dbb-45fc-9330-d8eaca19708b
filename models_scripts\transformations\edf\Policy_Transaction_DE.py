import pyspark.sql.functions as F
from pyspark.sql import DataFrame

from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.add_columns import (
    AddUSGAAPDateColumn,
    get_german_country_using_policy_table,
)


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["Transaction_main"]
    policy_df = df_dict["Policy_Policy"]
    gaap_df = df_dict["Reference_DatesGAAP"]

    main_df = main_df.withColumn("TransactionDate", F.to_date(F.col("TransactionDate")))
    output_df = get_german_country_using_policy_table(main_df, policy_df)
    output_df = AddUSGAAPDateColumn(output_df, gaap_df, policy_df).add_usgaap_date()

    return output_df
