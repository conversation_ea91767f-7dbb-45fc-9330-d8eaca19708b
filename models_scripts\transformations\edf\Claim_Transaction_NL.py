from pyspark.sql import DataFrame

from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.misc import (
    unify_keyDekkingsNummer_with_claim_section,
)


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    """
    Custom transformation for Claim_Transaction_NL.
    """
    transaction_df = df_dict["Transaction_main"]
    section_df = df_dict["Claim_Section"]

    result_df = unify_keyDekkingsNummer_with_claim_section(
        transaction_df, section_df, logger=kwargs["logger"]
    )
    return result_df
