from pyspark.sql import DataFrame

from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.currency import (
    PolicyExcessCurrencyConversor,
)


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["Excess_main"]
    exchange_rate_df = df_dict["Excess_exchange_rate"]

    output_df = PolicyExcessCurrencyConversor.add_currency_columns(
        main_df, exchange_rate_df
    )

    return output_df
