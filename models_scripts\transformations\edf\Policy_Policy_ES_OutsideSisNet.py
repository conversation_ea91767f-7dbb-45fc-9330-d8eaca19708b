from pyspark.sql import DataFrame

from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.add_columns import add_spanish_country
from models_scripts.transformations.edf.common.currency import (
    PolicyPolicyCurrencyConversor,
)


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["Policy_main"]
    exchange_rate_df = df_dict["Policy_exchange_rate"]

    main_df = add_spanish_country(main_df)
    main_df = PolicyPolicyCurrencyConversor.add_currency_columns(
        main_df, exchange_rate_df
    )

    return main_df
