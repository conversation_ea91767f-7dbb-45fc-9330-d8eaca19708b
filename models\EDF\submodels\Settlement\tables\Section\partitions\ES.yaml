Country: ES
existCustomTransformation: 'True'

dataSource:
- name: main
  type: SourceSisnetES
  parameters:
    sqlFileName: Section.sql
    querySourceType: SQL_FILE

- name: policy
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Policy
    Table: Policy
    Partitions:
      - ES
      - ES_historic

ColumnSpecs:
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  PolicySectionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  KeyReserving:
    NotInSource: True
