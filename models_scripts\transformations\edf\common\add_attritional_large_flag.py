import pyspark.sql.functions as F
from pyspark.sql import Column, DataFrame, Window

from models_scripts.transformations.common.traits import transformation


class AttritionalLargeFlagCalculator:
    """Add AttritionalLargeFlag column to claim_claim_df. This column is calculated based on the following formula:
    AttritionalLargeFlag = "Large" if CumulativeTransactionAmount >= AttritionalLargeThreshold else "Attritional"

    Args:
        claim_claim_df (DataFrame): Claim_Claim DataFrame. Main DataFrame.
        claim_section_df (DataFrame): Claim_Section DataFrame. Will be used as a bridge between Claim and PolicySection.
        claim_transaction_df (DataFrame): Claim_Transaction DataFrame. Will be used to get TransactionDate.
        claim_transaction_component_df (DataFrame): Claim_TransactionComponent DataFrame. Will be used to get CumulativeTransactionAmount.
        policy_section_df (DataFrame): Policy_Section DataFrame. Will be used as a bridge between Claim and ReservingClass.
        reservingclass_df (DataFrame): ReservingClass DataFrame. Will be used to get AttritionalLargeThreshold.
        partition (str): Source partition. Will be used to filter all the DataFrames to the partition Source.
    """

    MAX_COMULATIVE_AMOUNT_COL = "MaxCumulativeAmount"

    def __init__(
        self,
        claim_claim_df: DataFrame,
        claim_section_df: DataFrame,
        claim_transaction_df: DataFrame,
        claim_transaction_component_df: DataFrame,
        policy_section_df: DataFrame,
        reservingclass_df: DataFrame,
        partition: str,
    ) -> None:
        self.claim_claim_df = claim_claim_df
        self.claim_section_df = claim_section_df
        self.claim_transaction_df = claim_transaction_df
        self.claim_transaction_component_df = claim_transaction_component_df
        self.policy_section_df = policy_section_df
        self.reservingclass_df = reservingclass_df
        self.partition = partition

        self.original_columns = claim_claim_df.columns

    def _filter_partition(self) -> None:
        partition_filter = F.col("Partition") == self.partition
        """Filter dataframes to the partition Source"""
        self.claim_section_df = self.claim_section_df.filter(partition_filter)
        self.claim_transaction_df = self.claim_transaction_df.filter(partition_filter)
        self.claim_transaction_component_df = (
            self.claim_transaction_component_df.filter(partition_filter)
        )
        self.policy_section_df = self.policy_section_df.filter(partition_filter)
        self.reservingclass_df = self.reservingclass_df.filter(partition_filter)

    @staticmethod
    def _cumulative_amount_col() -> Column:
        return F.sum("TransactionComponentAmount").over(
            Window.partitionBy("KeyInternSchadenummer").orderBy("TransactionDate")
        )

    @transformation
    def _get_max_acumulative_amount_per_claim(self) -> DataFrame:
        """Get a DataFrame with the maximum cumulative amount per claim."""
        connection_cols = [
            "KeyInternSchadenummer",
            "KeyDekkingsNummer",
            "KeySchadeBoekingsNummer",
        ]
        transaction_df = self.claim_transaction_df.select(
            *connection_cols, "TransactionDate"
        )
        component_df = self.claim_transaction_component_df.select(
            *connection_cols, "TransactionComponentAmount"
        )

        components_with_date_df = transaction_df.join(
            component_df, connection_cols, "left"
        )

        component_cumulative_df = components_with_date_df.withColumn(
            "CumulativeAmount", self._cumulative_amount_col()
        )
        maximum_amount_per_claim_df = component_cumulative_df.groupBy(
            "KeyInternSchadenummer"
        ).agg(F.max("CumulativeAmount").alias(self.MAX_COMULATIVE_AMOUNT_COL))

        return maximum_amount_per_claim_df

    @transformation
    def _get_threshold_per_claim(self) -> DataFrame:
        """Get the attritional/Large threshold per claim."""

        claim_section_df = self.claim_section_df.select(
            "KeyInternSchadenummer", "KeyIdPolis", "KeyDekkingsNummer"
        )
        policy_section_df = self.policy_section_df.select(
            "KeyReserving", "KeyIdPolis", "KeyDekkingsNummer"
        )
        reservingclass_df = self.reservingclass_df.select(
            "KeyReserving", "AttritionalLargeThreshold"
        )

        joined_df = claim_section_df.join(
            policy_section_df,
            ["KeyIdPolis", "KeyDekkingsNummer"],
            "left",
        ).join(reservingclass_df, ["KeyReserving"], "left")

        threshold_per_claim_df = joined_df.select(
            "KeyInternSchadenummer", "AttritionalLargeThreshold"
        ).distinct()

        return threshold_per_claim_df

    @staticmethod
    def _validate_threshold(joined_df: DataFrame):
        corrupted_threshold_df = (
            joined_df.drop_duplicates(["KeyInternSchadenummer"])
            .groupBy("KeyInternSchadenummer", "AttritionalLargeThreshold")
            .count()
            .filter("count > 1")
        )
        assert corrupted_threshold_df.isEmpty(), (
            f"AttritionalLargeThreshold is not unique for each product!."
            f"The following products have more than one AttritionalLargeThreshold: {corrupted_threshold_df.collect()}"
        )

    @transformation
    def _claim_with_cumulative_and_threshold(
        self, cumulative_df: DataFrame, threshold_df: DataFrame
    ) -> DataFrame:
        """Join the claim_claim_df with the cumulative_df and threshold_df"""
        joined_df = self.claim_claim_df.join(
            cumulative_df, ["KeyInternSchadenummer"], "left"
        ).join(threshold_df, ["KeyInternSchadenummer"], "left")
        return joined_df

    @transformation
    def _add_attritional_large_flag(self, joined_df: DataFrame) -> DataFrame:
        # noinspection PyTypeChecker
        attritional_large_flag_col = F.when(
            F.col(self.MAX_COMULATIVE_AMOUNT_COL) >= F.col("AttritionalLargeThreshold"),
            F.lit("Large"),
        ).otherwise("Attritional")
        return joined_df.withColumn("AttritionalLargeFlag", attritional_large_flag_col)

    def _remove_unnecessary_columns(self, df: DataFrame) -> DataFrame:
        return df.select(self.original_columns + ["AttritionalLargeFlag"])

    @transformation
    def add_flag(self) -> DataFrame:
        """Add AttritionalLargeFlag column to claim_claim_df.

        This column is calculated based on the following formula:
        AttritionalLargeFlag = "Large" if CumulativeTransactionAmount >= AttritionalLargeThreshold else "Attritional"
        """
        self._filter_partition()
        cumulative_df = self._get_max_acumulative_amount_per_claim()
        threshold_df = self._get_threshold_per_claim()

        claim_df = self._claim_with_cumulative_and_threshold(
            cumulative_df, threshold_df
        )
        enhanced_claim_df = self._add_attritional_large_flag(claim_df)
        output_df = self._remove_unnecessary_columns(enhanced_claim_df)

        return output_df
