/* QUERY POLICY_POLICY MANUAL DEF */

SELECT DISTINCT
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.KeyIdPolis END AS 'KeyIdPolis',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.AdministrationOfficeCode END AS 'AdministrationOfficeCode',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.AssuredAddressArea END AS 'AssuredAddressArea',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.AssuredAddressCity END AS 'AssuredAddressCity',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.AssuredAddressStreet END AS 'AssuredAddressStreet',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.AssuredAnnualTurnover END AS 'AssuredAnnualTurnover',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.AssuredAnnualTurnoverCurrency END AS 'AssuredAnnualTurnoverCurrency',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.AssuredCode END AS 'AssuredCode',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.AssuredCountry END AS 'AssuredCountry',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.AssuredFullName END AS 'AssuredFullName',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.AssuredMainActivityCode END AS 'AssuredMainActivityCode',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.AssuredMainActivityDescription END AS 'AssuredMainActivityDescription',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.AssuredNumberOfFullTimeEmployees END AS 'AssuredNumberOfFullTimeEmployees',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.AssuredPostCode END AS 'AssuredPostCode',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.AssuredProvince END AS 'AssuredProvince',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.AssuredShortName END AS 'AssuredShortName',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.AssuredState END AS 'AssuredState',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.AssuredTerritory END AS 'AssuredTerritory',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.BrokerAddressArea END AS 'BrokerAddressArea',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.BrokerAddressCity END AS 'BrokerAddressCity',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.BrokerAddressStreet END AS 'BrokerAddressStreet',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.BrokerCode END AS 'BrokerCode',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.BrokerCountry END AS 'BrokerCountry',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.BrokerFullName END AS 'BrokerFullName',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.BrokerGroupCode END AS 'BrokerGroupCode',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.BrokerGroupName END AS 'BrokerGroupName',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.BrokerPostcode END AS 'BrokerPostcode',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.BrokerProvince END AS 'BrokerProvince',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.BrokerShortName END AS 'BrokerShortName',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.ClaimsBasisCode END AS 'ClaimsBasisCode',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.ClaimsBasisDescription END AS 'ClaimsBasisDescription',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.CoverholderCode END AS 'CoverholderCode',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.CoverholderName END AS 'CoverholderName',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.CustomerClassification END AS 'CustomerClassification',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.CustomerClassificationCode END AS 'CustomerClassificationCode',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.DistributionPartner END AS 'DistributionPartner',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.DistributionType END AS 'DistributionType',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.ExpiryDate END AS 'ExpiryDate',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.IBCIndustryCode END AS 'IBCIndustryCode',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.InceptionDate END AS 'InceptionDate',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.InsurerEntityCode END AS 'InsurerEntityCode',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.InsurerEntityDescription END AS 'InsurerEntityDescription',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.LapsedReason END AS 'LapsedReason',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.Layer END AS 'Layer',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.OrderPercentage END AS 'OrderPercentage',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.OutwardsFACIndicator END AS 'OutwardsFACIndicator',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.PeerReview1 END AS 'PeerReview1',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.PeerReview1Code END AS 'PeerReview1Code',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.PeerReview1Comment END AS 'PeerReview1Comment',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.PeerReview1Date END AS 'PeerReview1Date',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.PeerReview2 END AS 'PeerReview2',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.PeerReview2Code END AS 'PeerReview2Code',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.PeerReview2Comment END AS 'PeerReview2Comment',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.PeerReview2Date END AS 'PeerReview2Date',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.PlacementType END AS 'PlacementType',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.PolicyCode END AS 'PolicyCode',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.PolicyLastModifiedDate END AS 'PolicyLastModifiedDate',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.PolicyProductCode END AS 'PolicyProductCode',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.PolicyProductDescription END AS 'PolicyProductDescription',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.PolicyReference END AS 'PolicyReference',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.PreviousPolicyReference END AS 'PreviousPolicyReference',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.PreviousSourceSystem END AS 'PreviousSourceSystem',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.PreviousSourceSystemDescription END AS 'PreviousSourceSystemDescription',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.ProducingOfficeCode END AS 'ProducingOfficeCode',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.QuotationReference END AS 'QuotationReference',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.ReferralUnderwriter END AS 'ReferralUnderwriter',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.ReinsurancePolicyIndicator END AS 'ReinsurancePolicyIndicator',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.ReinsuranceReference END AS 'ReinsuranceReference',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.ReinsuredAddressArea END AS 'ReinsuredAddressArea',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.ReinsuredAddressCity END AS 'ReinsuredAddressCity',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.ReinsuredAddressStreet END AS 'ReinsuredAddressStreet',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.ReinsuredAnnualTurnover END AS 'ReinsuredAnnualTurnover',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.ReinsuredAnnualTurnoverCurrency END AS 'ReinsuredAnnualTurnoverCurrency',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.ReinsuredCode END AS 'ReinsuredCode',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.ReinsuredCountry END AS 'ReinsuredCountry',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.ReinsuredFullName END AS 'ReinsuredFullName',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.ReinsuredNumberOfFullTimeEmployees END AS 'ReinsuredNumberOfFullTimeEmployees',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.ReinsuredPostcode END AS 'ReinsuredPostcode',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.ReinsuredProvince END AS 'ReinsuredProvince',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.ReinsuredShortName END AS 'ReinsuredShortName',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.ReinsuredState END AS 'ReinsuredState',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.ReinsuredTerritory END AS 'ReinsuredTerritory',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.RenewalPolicyIndicator END AS 'RenewalPolicyIndicator',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.RenewalPolicyReference END AS 'RenewalPolicyReference',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.RenewalSequenceNumber END AS 'RenewalSequenceNumber',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.RiskCode END AS 'RiskCode',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.StatusCode END AS 'StatusCode',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.StatusDescription END AS 'StatusDescription',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.TacitRenewalIndicator END AS 'TacitRenewalIndicator',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.TerrorismCode END AS 'TerrorismCode',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.Timezone END AS 'Timezone',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.TradeCodeOrIndustry END AS 'TradeCodeOrIndustry',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.TrustFund END AS 'TrustFund',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.UnderlyingLimit END AS 'UnderlyingLimit',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.UnderlyingLimitCurrency END AS 'UnderlyingLimitCurrency',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.UnderwriterCode END AS 'UnderwriterCode',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.UnderwriterCodeID END AS 'UnderwriterCodeID',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.UnderwriterName END AS 'UnderwriterName',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.WrittenDate END AS 'WrittenDate',
    CASE WHEN B.Certificate_number is not null THEN '' ELSE A.YearOfAccount END AS 'YearOfAccount',
    A.PAIS

FROM ( /* MANUAL DATA */
    SELECT DISTINCT
        'manual' as 'partition',
        REPLACE(CASE WHEN A.NPOLICY = '' THEN TRIM(A.POLIORIG) ELSE TRIM(A.NPOLICY) END,' ','') Certificate_number,
        CASE
            WHEN (CASE WHEN A.NPOLICY = '' THEN A.POLIORIG ELSE A.NPOLICY END) IS NOT NULL THEN
                CONCAT(
                    A.CODPROD
                    ,' '
                    ,(SELECT MIN(CAST(Z.YEAREFEC AS float)) FROM NTJDWHMRK..MEDLMAPS Z WHERE B.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK)
                    ,'/'
                    ,CASE WHEN A.NPOLICY = '' THEN A.POLIORIG ELSE A.NPOLICY END
                    ,'_'
                    ,(CAST(B.YEAREFEC AS float) - (SELECT MIN(CAST(Z.YEAREFEC AS float)) FROM NTJDWHMRK..MEDLMAPS Z WHERE B.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK) + 1)
                )
            WHEN (CASE WHEN A.NPOLICY = '' THEN A.POLIORIG ELSE A.NPOLICY END) IS NULL THEN
                (
                    SELECT DISTINCT POLICODE
                    FROM NTJDWHMRK..MEDFPOLI X
                    WHERE REPLACE(CASE WHEN A.NPOLICY = '' THEN TRIM(A.POLIORIG) ELSE TRIM(A.NPOLICY) END,' ','') = (LEFT(X.POLIREFE, CHARINDEX('_',X.POLIREFE)-1))
                    AND B.YEAREFEC = X.YEAOFACC
                )
        END AS 'KeyIdPolis',
        CASE WHEN A.OFICINA = 'Madrid' THEN 'MDR01' ELSE 'BRC01' END AS 'AdministrationOfficeCode',
        'NONE' AS 'AssuredAddressArea',
        'NONE' AS 'AssuredAddressCity',
        'NONE' AS 'AssuredAddressStreet',
        0     AS 'AssuredAnnualTurnover',
        'EURO' AS 'AssuredAnnualTurnoverCurrency',
        'NONE' AS 'AssuredCode',
        CASE WHEN PAIS = 'Portugal' THEN 'PORT' WHEN PAIS = 'EspaÃ±a' THEN 'ESPA' ELSE PAIS END AS 'AssuredCountry',
        PAIS,
        B.TOMADOR AS 'AssuredFullName',
        CASE WHEN SECREGRU = '2. Financial Services Professional Risks' THEN 'GRCP03'
              WHEN SECREGRU = '3. Miscellaneous Professional Risks' THEN 'GRCP01'
              WHEN SECREGRU = '4. Professions Professional Risks' THEN 'GRCP02'
              WHEN SECREGRU = '1. Construction Professional Risks' THEN 'GRCP01'
        ELSE '' END AS 'AssuredMainActivityCode',
        'NONE' AS 'AssuredMainActivityDescription',
        'NONE' AS 'AssuredNumberOfFullTimeEmployees',
        '' AS 'AssuredPostCode',
        '' AS 'AssuredProvince',
        B.TOMADOR AS 'AssuredShortName',
        'None' AS 'AssuredState',
        A.TERRITORIALITY AS 'AssuredTerritory',
        'NONE' AS 'BrokerAddressArea',
        'NONE' AS 'BrokerAddressCity',
        'NONE' AS 'BrokerAddressStreet',
        B.CODIMEDI AS 'BrokerCode',
        'NONE' AS 'BrokerCountry',
        B.NOMBMEDI AS 'BrokerFullName',
        'NONE' AS 'BrokerGroupCode',
        'NONE' AS 'BrokerGroupName',
        'NONE' AS 'BrokerPostcode',
        'NONE' AS 'BrokerProvince',
        'NONE' AS 'BrokerShortName',
        'NONE' AS 'ClaimsBasisCode',
        'NONE' AS 'ClaimsBasisDescription',
        'NONE' AS 'CoverholderCode',
        'NONE' AS 'CoverholderName',
        'NONE' AS 'CustomerClassification',
        'NONE' AS 'CustomerClassificationCode',
        'NONE' AS 'DistributionPartner',
        'NONE' AS 'DistributionType',
        (SELECT MAX (Z.FECHVENC) FROM NTJDWHMRK..MEDLMAPS Z WHERE B.YEAREFEC = Z.YEAREFEC AND B.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK) AS 'ExpiryDate',
        'NONE' AS 'IBCIndustryCode',
        (SELECT MIN (Z.FECHEFEC) FROM NTJDWHMRK..MEDLMAPS Z WHERE B.YEAREFEC = Z.YEAREFEC AND B.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK) AS 'InceptionDate',
        'NONE' AS 'InsurerEntityCode',
        'NONE' AS 'InsurerEntityDescription',
        'NONE' AS 'LapsedReason',
        'NONE' AS 'Layer',
        'NONE' AS 'OrderPercentage',
        'NONE' AS 'OutwardsFACIndicator',
        'NONE' AS 'PeerReview1',
        'NONE' AS 'PeerReview1Code',
        'NONE' AS 'PeerReview1Comment',
        'NONE' AS 'PeerReview1Date',
        'NONE' AS 'PeerReview2',
        'NONE' AS 'PeerReview2Code',
        'NONE' AS 'PeerReview2Comment',
        'NONE' AS 'PeerReview2Date',
        'OM'   AS 'PlacementType',
        CASE
            WHEN A.NPOLICY = '' THEN CONCAT(
                A.POLIORIG,'/',(CAST(B.YEAREFEC AS float) - (SELECT MIN(CAST(Z.YEAREFEC AS float)) FROM NTJDWHMRK..MEDLMAPS Z WHERE B.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK) + 1)
            )
            ELSE CONCAT(
                A.NPOLICY,'/',(CAST(B.YEAREFEC AS float) - (SELECT MIN(CAST(Z.YEAREFEC AS float)) FROM NTJDWHMRK..MEDLMAPS Z WHERE B.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK) + 1)
            )
        END AS 'PolicyCode',
        '' AS 'PolicyLastModifiedDate',
        A.CODPROD AS 'PolicyProductCode',
        '' AS 'PolicyProductDescription',
        CASE
            WHEN A.NPOLICY = '' THEN CONCAT(
                A.POLIORIG,'_',(CAST(B.YEAREFEC AS float) - (SELECT MIN(CAST(Z.YEAREFEC AS float)) FROM NTJDWHMRK..MEDLMAPS Z WHERE B.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK) + 1)
            )
            ELSE CONCAT(
                A.NPOLICY,'_',(CAST(B.YEAREFEC AS float) - (SELECT MIN(CAST(Z.YEAREFEC AS float)) FROM NTJDWHMRK..MEDLMAPS Z WHERE B.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK) + 1)
            )
        END AS 'PolicyReference',
        '-' AS 'PreviousPolicyReference',
        'SISnet' AS 'PreviousSourceSystem',
        'SISnet' AS 'PreviousSourceSystemDescription',
        '' AS 'ProducingOfficeCode',
        'None' AS 'QuotationReference',
        'None' AS 'ReferralUnderwriter',
        'None' AS 'ReinsurancePolicyIndicator',
        'None' AS 'ReinsuranceReference',
        'None' AS 'ReinsuredAddressArea',
        'None' AS 'ReinsuredAddressCity',
        'None' AS 'ReinsuredAddressStreet',
        '' AS 'ReinsuredAnnualTurnover',
        'None' AS 'ReinsuredAnnualTurnoverCurrency',
        'None' AS 'ReinsuredCode',
        'None' AS 'ReinsuredCountry',
        'None' AS 'ReinsuredFullName',
        'None' AS 'ReinsuredNumberOfFullTimeEmployees',
        'None' AS 'ReinsuredPostcode',
        'None' AS 'ReinsuredProvince',
        'None' AS 'ReinsuredShortName',
        'None' AS 'ReinsuredState',
        'None' AS 'ReinsuredTerritory',
        'None' AS 'RenewalPolicyIndicator',
        '' AS 'RenewalPolicyReference',
        CAST(B.YEAREFEC AS float) - (SELECT MIN(CAST(Z.YEAREFEC AS float)) FROM NTJDWHMRK..MEDLMAPS Z WHERE B.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK) + 1  AS 'RenewalSequenceNumber',
        'None' AS 'RiskCode',
        '' AS 'StatusCode',
        '' AS 'StatusDescription',
        '' AS 'TacitRenewalIndicator',
        '' AS 'TerrorismCode',
        '' AS 'Timezone',
        'None' AS 'TradeCodeOrIndustry',
        'None' AS 'TrustFund',
        'None' AS 'UnderlyingLimit',
        'None' AS 'UnderlyingLimitCurrency',
        '' AS 'UnderwriterCode',
        'None' AS 'UnderwriterCodeID',
        '' AS 'UnderwriterName',
        '' AS 'WrittenDate',
        B.YEAREFEC AS 'YearOfAccount'
    FROM NTJDWHMRK..MEDLMAPO A, NTJDWHMRK..MEDLMAPS B
    WHERE A.ID_MEDLMAPO = B.ID_MEDLMAPO_FK
) A

LEFT JOIN ( /* SISNET DATA */
    SELECT DISTINCT
        'SISnet' as 'partition',
        TRIM(LEFT(A.POLIREFE, CHARINDEX('_',A.POLIREFE) - 1)) Certificate_number,
        A.POLICODE AS 'KeyIdPolis',
        A.ADMOFFCO AS 'AdministrationOfficeCode',
        A.ASSADDAR AS 'AssuredAddressArea',
        A.ASSADDCI AS 'AssuredAddressCity',
        A.ASSADDST AS 'AssuredAddressStreet',
        A.ASSANNTU AS 'AssuredAnnualTurnover',
        A.ASANTUCU AS 'AssuredAnnualTurnoverCurrency',
        A.ASSUCODE AS 'AssuredCode',
        A.ASSUCOUN AS 'AssuredCountry',
        A.ASSFULNA AS 'AssuredFullName',
        A.ASMAACCO AS 'AssuredMainActivityCode',
        'None' AS 'AssuredMainActivityDescription',
        A.ASNFUTEM AS 'AssuredNumberOfFullTimeEmployees',
        A.ASSPOSCO AS 'AssuredPostCode',
        A.ASSUPROV AS 'AssuredProvince',
        A.ASSSHONA AS 'AssuredShortName',
        'None' AS 'AssuredState',
        A.ASSUTERR AS 'AssuredTerritory',
        A.BROADDAR AS 'BrokerAddressArea',
        A.BROADDCI AS 'BrokerAddressCity',
        A.BROADDST AS 'BrokerAddressStreet',
        A.BROKCODE AS 'BrokerCode',
        'None' AS 'BrokerCodeID',
        A.BROKCOUN AS 'BrokerCountry',
        A.BROFULNA AS 'BrokerFullName',
        A.BROGROCO AS 'BrokerGroupCode',
        A.BROGRONA AS 'BrokerGroupName',
        A.BROPOSCO AS 'BrokerPostcode',
        A.BROKPROV AS 'BrokerProvince',
        'None' AS 'BrokerShortName',
        'None' AS 'ClaimsBasisCode',
        'None' AS 'ClaimsBasisDescription',
        'None' AS 'CoverholderCode',
        'None' AS 'CoverholderName',
        A.CUSTCLAS AS 'CustomerClassification',
        A.CUSCLACO AS 'CustomerClassificationCode',
        'None' AS 'DistributionPartner',
        'None' AS 'DistributionType',
        A.EXPIDATE AS 'ExpiryDate',
        'None' AS 'IBCIndustryCode',
        A.INCEDATE AS 'InceptionDate',
        'None' AS 'InsurerEntityCode',
        'None' AS 'InsurerEntityDescription',
        'None' AS 'LapsedReason',
        'None' AS 'Layer',
        '' AS 'OrderPercentage',
        'None' AS 'OutwardsFACIndicator',
        'None' AS 'PeerReview1',
        'None' AS 'PeerReview1Code',
        'None' AS 'PeerReview1Comment',
        '' AS 'PeerReview1Date',
        'None' AS 'PeerReview2',
        'None' AS 'PeerReview2Code',
        'None' AS 'PeerReview2Comment',
        '' AS 'PeerReview2Date',
        A.PACETYPE AS 'PlacementType',
        A.POLICODE AS 'PolicyCode',
        A.POLAMODA AS 'PolicyLastModifiedDate',
        A.POLPROCO AS 'PolicyProductCode',
        A.POLPRODE AS 'PolicyProductDescription',
        A.POLIREFE AS 'PolicyReference',
        '-' AS 'PreviousPolicyReference',
        'SISnet' AS 'PreviousSourceSystem',
        'SISnet' AS 'PreviousSourceSystemDescription',
        A.PROOFFCO AS 'ProducingOfficeCode',
        'None' AS 'QuotationReference',
        'None' AS 'ReferralUnderwriter',
        'None' AS 'ReinsurancePolicyIndicator',
        'None' AS 'ReinsuranceReference',
        'None' AS 'ReinsuredAddressArea',
        'None' AS 'ReinsuredAddressCity',
        'None' AS 'ReinsuredAddressStreet',
        '' AS 'ReinsuredAnnualTurnover',
        'None' AS 'ReinsuredAnnualTurnoverCurrency',
        'None' AS 'ReinsuredCode',
        'None' AS 'ReinsuredCountry',
        'None' AS 'ReinsuredFullName',
        'None' AS 'ReinsuredNumberOfFullTimeEmployees',
        'None' AS 'ReinsuredPostcode',
        'None' AS 'ReinsuredProvince',
        'None' AS 'ReinsuredShortName',
        'None' AS 'ReinsuredState',
        'None' AS 'ReinsuredTerritory',
        'None' AS 'RenewalPolicyIndicator',
        A.RENPOLRE AS 'RenewalPolicyReference',
        A.RENSEQNU AS 'RenewalSequenceNumber',
        'None' AS 'RiskCode',
        A.STATCODE AS 'StatusCode',
        A.STATDESC AS 'StatusDescription',
        A.TACRENIN AS 'TacitRenewalIndicator',
        A.TERRCODE AS 'TerrorismCode',
        A.TIMEZONE AS 'Timezone',
        'None' AS 'TradeCodeOrIndustry',
        'None' AS 'TrustFund',
        'None' AS 'UnderlyingLimit',
        'None' AS 'UnderlyingLimitCurrency',
        A.UNDECODE AS 'UnderwriterCode',
        'None' AS 'UnderwriterCodeID',
        A.UNDENAME AS 'UnderwriterName',
        A.WRITDATE AS 'WrittenDate',
        A.YEAOFACC AS 'YearOfAccount'
    FROM NTJDWHMRK..MEDFPOLI A
    WHERE A.ID_MEDFPOLI IN (
        SELECT MAX(Z.ID_MEDFPOLI)
        FROM NTJDWHMRK..MEDFPOLI Z
        WHERE A.POLICODE = Z.POLICODE
            AND Z.POLAMODA = (
                SELECT MAX(B.POLAMODA)
                FROM NTJDWHMRK..MEDFPOLI B
                WHERE
                    B.FECHALTA >= CONVERT(datetime,'01/01/2000',103)
                    AND Z.POLICODE = B.POLICODE
            )
    )

) B ON TRIM(A.Certificate_number) = TRIM(B.Certificate_number) and CAST(A.YearOfAccount AS float) = CAST(B.YearOfAccount AS float)

WHERE CASE WHEN B.Certificate_number is not null THEN '' ELSE A.KeyIdPolis END <> ''
