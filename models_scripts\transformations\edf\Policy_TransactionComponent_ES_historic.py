from pyspark.sql import DataFrame

from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.currency import PolicyCurrencyConversor


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["TransactionComponent_main"]
    transaction_df = df_dict["Policy_Transaction"]
    exchange_rate_df = df_dict["TransactionComponent_exchange_rate"]

    component_with_converted_currencies_df = PolicyCurrencyConversor.add_new_columns(
        main_df, transaction_df, exchange_rate_df
    )

    return component_with_converted_currencies_df
