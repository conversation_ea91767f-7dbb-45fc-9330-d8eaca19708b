from datetime import date

import pytest
from mines2.core.extensions.misc import assert_dataframe_equality

from models_scripts.transformations.edf.common.currency import (
    ClaimCurrencyConversor,
    EDFTransComponentCurrencyConversorBase,
    PolicyCurrencyConversor,
    SettlementCurrencyConversor,
    conform_euro_currency,
)


def test_conform_euro_currency(spark):
    """Test conform_euro_currency function."""
    input_df = spark.createDataFrame(
        [
            ("1", "EURO", "AGG", 1000),
            ("2", None, "AOC", 2000),
            ("3", "", "AOC", 3000),
            ("4", "EUR", "AGG", 3000),
            ("5", "USD", "AOC", 4000),
        ],
        [
            "KeyIdPolis",
            "LimitCurrencyCode",
            "LimitBasisCode",
            "Limit",
        ],
    )
    expected_df = spark.createDataFrame(
        [
            ("1", "EUR", "AGG", 1000),
            ("2", "EUR", "AOC", 2000),
            ("3", "EUR", "AOC", 3000),
            ("4", "EUR", "AGG", 3000),
            ("5", "USD", "AOC", 4000),
        ],
        [
            "KeyIdPolis",
            "LimitCurrencyCode",
            "LimitBasisCode",
            "Limit",
        ],
    )
    output_df = conform_euro_currency(input_df, "LimitCurrencyCode")
    assert_dataframe_equality(output_df, expected_df)


class TestCurrencyConversor:
    class DummyCurrencyConversor(EDFTransComponentCurrencyConversorBase):
        TRANSACTION_COMPONENT_JOIN_COLS = ["id1", "id2"]
        TRANSACTION_CURRENCY_COL = "Currency"
        AMOUNT_COL = "Amount"

    class InvalidDummyCurrencyConversor(EDFTransComponentCurrencyConversorBase):
        """Dummy class to test invalid CurrencyConversorBase subclass.
        The class is invalid because it does not define all required attributes."""

        TRANSACTION_CURRENCY_COL = "Currency"
        AMOUNT_COL = "Amount"

    def test_exchange_rates_for_transactions_returns_correct_format(self, spark):
        transaction_data = [
            ("KeyDekkingsNummer1", "KeyFactuurnummer1", "USD"),
            ("KeyDekkingsNummer2", "KeyFactuurnummer2", "EUR"),
        ]
        transaction_df = spark.createDataFrame(
            transaction_data,
            self.DummyCurrencyConversor.TRANSACTION_COMPONENT_JOIN_COLS
            + [self.DummyCurrencyConversor.TRANSACTION_CURRENCY_COL],
        )

        exchange_rate_data = [
            ("USD", 0.86, 0.75, 1.2, 1.3),
            ("EUR", 1.16, 1.33, 0.92, 0.76),
        ]
        exchange_rate_df = spark.createDataFrame(
            exchange_rate_data,
            ["FromCcy"] + self.DummyCurrencyConversor.CURRENCIES_TO_CONVERT,
        )

        result_df = self.DummyCurrencyConversor()._get_exchange_rates_for_transactions(
            transaction_df, exchange_rate_df
        )

        expected_data = [
            ("KeyDekkingsNummer1", "KeyFactuurnummer1", 0.86, 0.75, 1.2, 1.3),
            ("KeyDekkingsNummer2", "KeyFactuurnummer2", 1.16, 1.33, 0.92, 0.76),
        ]
        expected_df = spark.createDataFrame(
            expected_data,
            self.DummyCurrencyConversor.TRANSACTION_COMPONENT_JOIN_COLS
            + self.DummyCurrencyConversor.CURRENCIES_TO_CONVERT,
        )

        assert_dataframe_equality(result_df, expected_df)

    def test_exchange_rates_for_transactions_handles_missing_rates(self, spark):
        transaction_data = [
            ("KeyDekkingsNummer1", "KeyFactuurnummer1", "USD"),
            ("KeyDekkingsNummer2", "KeyFactuurnummer2", "JPY"),
        ]
        transaction_df = spark.createDataFrame(
            transaction_data,
            self.DummyCurrencyConversor.TRANSACTION_COMPONENT_JOIN_COLS
            + [self.DummyCurrencyConversor.TRANSACTION_CURRENCY_COL],
        )

        exchange_rate_data = [("USD", 0.86, 0.75, 1.2, 1.3)]
        exchange_rate_df = spark.createDataFrame(
            exchange_rate_data,
            ["FromCcy"] + self.DummyCurrencyConversor.CURRENCIES_TO_CONVERT,
        )

        result_df = self.DummyCurrencyConversor()._get_exchange_rates_for_transactions(
            transaction_df, exchange_rate_df
        )

        expected_data = [
            ("KeyDekkingsNummer1", "KeyFactuurnummer1", 0.86, 0.75, 1.2, 1.3),
            ("KeyDekkingsNummer2", "KeyFactuurnummer2", None, None, None, None),
        ]
        expected_df = spark.createDataFrame(
            expected_data,
            self.DummyCurrencyConversor.TRANSACTION_COMPONENT_JOIN_COLS
            + self.DummyCurrencyConversor.CURRENCIES_TO_CONVERT,
        )

        assert_dataframe_equality(result_df, expected_df)

    def test_add_converted_currency_columns_returns_correct(self, spark):
        transaction_component_df = spark.createDataFrame(
            [
                ("KeyIdPolis1", "KeyDekkingsNummer1", 100.0),
                ("KeyIdPolis2", "KeyDekkingsNummer2", 200.0),
                ("KeyIdPolis3", "KeyDekkingsNummer3", 300.5555),
                ("KeyIdPolis4", "KeyDekkingsNummer4", 400.0),
            ],
            ["id1", "id2", "Amount"],
        )

        transaction_exchanges_df = spark.createDataFrame(
            [
                ("KeyIdPolis1", "KeyDekkingsNummer1", 0.86, 0.75, 1.2, 1.3),
                ("KeyIdPolis2", "KeyDekkingsNummer2", 1.16, 1.33, 0.92, 0.76),
                ("KeyIdPolis3", "KeyDekkingsNummer3", 1.16, 1.33, 0.92, 0.76),
            ],
            ["id1", "id2", "GBP", "EUR", "USD", "CAD"],
        )

        result_df = self.DummyCurrencyConversor()._add_converted_currency_columns(
            transaction_component_df, transaction_exchanges_df
        )

        expected_df = spark.createDataFrame(
            [
                (
                    "KeyIdPolis1",
                    "KeyDekkingsNummer1",
                    100.0,
                    100.0,
                    116.27906976744187,
                    116.28,
                    133.33333333333334,
                    133.33,
                    83.33333333333334,
                    83.33,
                    76.92307692307692,
                    76.92,
                    None,
                ),
                (
                    "KeyIdPolis2",
                    "KeyDekkingsNummer2",
                    200.0,
                    200.0,
                    172.41379310344828,
                    172.41,
                    150.37593984962405,
                    150.38,
                    217.39130434782606,
                    217.39,
                    263.1578947368421,
                    263.16,
                    None,
                ),
                (
                    "KeyIdPolis3",
                    "KeyDekkingsNummer3",
                    300.5555,
                    300.56,
                    259.0995689655173,
                    259.1,
                    225.9815789473684,
                    225.98,
                    326.6907608695652,
                    326.69,
                    395.46776315789475,
                    395.47,
                    None,
                ),
                (
                    "KeyIdPolis4",
                    "KeyDekkingsNummer4",
                    400.0,
                    400.0,
                    None,
                    None,
                    None,
                    None,
                    None,
                    None,
                    None,
                    None,
                    "No Match between the Table and the Transaction",
                ),
            ],
            [
                "id1",
                "id2",
                "Amount",
                "AmountRounded",
                "AmountGBP",
                "AmountRoundedGBP",
                "AmountEUR",
                "AmountRoundedEUR",
                "AmountUSD",
                "AmountRoundedUSD",
                "AmountCAD",
                "AmountRoundedCAD",
                "__MINES__TMP__quarantine_MappingIsMissing",
            ],
        )

        assert_dataframe_equality(result_df, expected_df)

    def test_add_converted_without_transaction(self, spark):
        transaction_component_df = spark.createDataFrame(
            [
                ("KeyIdPolis1", "KeyDekkingsNummer1", 100.0),
                ("KeyIdPolis2", "KeyDekkingsNummer2", 200.0),
                ("KeyIdPolis3", "KeyDekkingsNummer3", 300.5555),
            ],
            ["id1", "id2", "Amount"],
        )

        exchange_rate_df = spark.createDataFrame(
            [
                ("USD", "GBP", "Closing", 0.8, date(2021, 1, 1)),
                ("USD", "EUR", "Closing", 0.75, date(2021, 1, 1)),
                ("USD", "USD", "Closing", 1.2, date(2021, 1, 1)),
                ("USD", "CAD", "Closing", 1.3, date(2021, 1, 1)),
                # Below is the exchange rate that we expect to be ignored
                ("CAD", "GBP", "Closing", 1.16, date(2022, 1, 1)),
                ("CAD", "EUR", "Closing", 1.33, date(2022, 1, 1)),
                ("CAD", "USD", "Closing", 0.92, date(2022, 1, 1)),
                ("CAD", "CAD", "Closing", 0.76, date(2022, 1, 1)),
            ],
            ["FromCcy", "ToCcY", "RateType", "FXRate", "RateDate"],
        )
        result_df = self.DummyCurrencyConversor.add_new_columns_using_single_currency(
            transaction_component_df, exchange_rate_df, "USD"
        )

        expected_df = spark.createDataFrame(
            [
                (
                    "KeyIdPolis1",
                    "KeyDekkingsNummer1",
                    100.0,
                    100.0,
                    125.0,
                    125.0,
                    133.33333333333334,
                    133.33,
                    83.33333333333334,
                    83.33,
                    76.92307692307692,
                    76.92,
                ),
                (
                    "KeyIdPolis2",
                    "KeyDekkingsNummer2",
                    200.0,
                    200.0,
                    250.0,
                    250.0,
                    266.6666666666667,
                    266.67,
                    166.66666666666669,
                    166.67,
                    153.84615384615384,
                    153.85,
                ),
                (
                    "KeyIdPolis3",
                    "KeyDekkingsNummer3",
                    300.5555,
                    300.56,
                    375.694375,
                    375.69,
                    400.74066666666664,
                    400.74,
                    250.46291666666667,
                    250.46,
                    231.19653846153844,
                    231.2,
                ),
            ],
            [
                "id1",
                "id2",
                "Amount",
                "AmountRounded",
                "AmountGBP",
                "AmountRoundedGBP",
                "AmountEUR",
                "AmountRoundedEUR",
                "AmountUSD",
                "AmountRoundedUSD",
                "AmountCAD",
                "AmountRoundedCAD",
            ],
        )

        assert_dataframe_equality(result_df, expected_df)

    def test_invalid_subclass_raises_error(self):
        with pytest.raises(AssertionError):
            self.InvalidDummyCurrencyConversor()


def test_policy_currency_conversor_valid():
    """check if the __init__ tests pass"""
    PolicyCurrencyConversor()


def test_claim_currency_conversor_valid():
    """check if the __init__ tests pass"""
    ClaimCurrencyConversor()


def test_settlement_currency_conversor_valid():
    """check if the __init__ tests pass"""
    SettlementCurrencyConversor()
