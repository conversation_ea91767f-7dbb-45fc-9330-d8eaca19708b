Country: ES
existCustomTransformation: 'False'
Incremental: 'True'
dataSource:
- name: main
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Policy
    Table: Policy
ColumnSpecs:
  AS_AT_DATE:
    mapType: calculated
    func:
      name: add_days
      args:
        column: CreatedDateTime
        days: -1
  PolicyStatusCode:
    sourceName: StatusCode
  PolicyStatusDescription:
    sourceName: StatusDescription
