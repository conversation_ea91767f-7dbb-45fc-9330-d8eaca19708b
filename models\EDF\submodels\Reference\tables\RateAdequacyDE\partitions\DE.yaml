Country: DE
existCustomTransformation: 'True'

dataSource:
- name: main
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Silver
    Table: RateAdequacyDE

- name: sev_vertrag
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: dev_europeandatalake_01
      prod: prod_europeandatalake_01
    schema: de_direct
    table: winsurespike_sev_vertrag

- name: sev_police
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: dev_europeandatalake_01
      prod: prod_europeandatalake_01
    schema: de_direct
    table: winsurespike_sev_police

ColumnSpecs:
  POLICY_NR:
    locale: en_US.utf8
  CONTRACT_NR:
    locale: en_US.utf8
  RISK_NR:
    locale: en_US.utf8
  YEAROFACCOUNT:
    locale: en_US.utf8
  PRM_NO_FEES:
    locale: en_US.utf8
  RISK_DISCOUNT_OR_SURCHARGE:
    locale: en_US.utf8
  RATE_SURCHARGE:
    locale: en_US.utf8
  ADMIN_COST:
    locale: en_US.utf8
  PRM_ADJ:
    locale: en_US.utf8
  final_net_prem:
    locale: en_US.utf8
  final_tech_prem:
    locale: en_US.utf8
  tech_ratio:
    locale: en_US.utf8
