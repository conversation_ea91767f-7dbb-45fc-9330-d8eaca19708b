Country: NL
existCustomTransformation: 'True'
dataSource:
- name: main
  type: SourceProgressNL
  parameters:
    sqlFileName: Query ClaimTransactionComponentArray.sql
    querySourceType: SQL_FILE

- name: exchange_rate
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: test_mintdatalake_02
      uat: test_mintdatalake_02
      prod: prod_mintdatalake_02
    schema: rdm_dbo
    table: mint_mapping_roe
    sourceSystem: MINT_RAW_DATALAKE
- name: section
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Claim
    Table: Section
- name:  transaction
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Claim
    Table: Transaction
ColumnSpecs:
  ClaimsID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: KeyInternSchadenummer
        sep: ':'
  ClaimsSectionTransID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: KeyInternSchadenummer
        - source: COLUMN
          name: KeyDekkingsNummer
        - source: COLUMN
          name: KeySchadeBoekingsNummer
        sep: ':'
  KeyInternSchadenummer:
    locale: en_US.utf8
  KeyIdPolis:
    locale: en_US.utf8
  KeyDekkingsNummer:
    locale: en_US.utf8
  KeySchadeBoekingsNummer:
    locale: en_US.utf8
  TransactionComponentAmount:
    locale: en_US.utf8
  TransactionComponentAmountGBP:
    NotInSource: True
  TransactionComponentAmountEUR:
    NotInSource: True
  TransactionComponentAmountUSD:
    NotInSource: True
  TransactionComponentAmountCAD:
    NotInSource: True
  TransactionComponentAmountRounded:
    NotInSource: True
  TransactionComponentAmountRoundedGBP:
    NotInSource: True
  TransactionComponentAmountRoundedEUR:
    NotInSource: True
  TransactionComponentAmountRoundedUSD:
    NotInSource: True
  TransactionComponentAmountRoundedCAD:
    NotInSource: True
  TransactionComponentTypeCode:
    locale: en_US.utf8
  TransactionComponentTypeDescription:
    locale: en_US.utf8
    sourceName: TransComponentTypeDescription

