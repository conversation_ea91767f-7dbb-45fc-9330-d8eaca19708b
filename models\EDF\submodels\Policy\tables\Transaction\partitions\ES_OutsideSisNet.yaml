Country: custom
existCustomTransformation: "True"

dataSource:
- name: main
  type: SourceSisnetES
  parameters:
    sqlFileName: 'Policy Transaction ES Manual Business.sql'
    querySourceType: SQL_FILE
    selectColumnsFromSchema: False
    enforceSourceSchemaOnStandard: 'False'

- name: policy
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Policy
    Table: Policy
    Partitions:
      - ES
      - ES_historic
      - ES_OutsideSisNet

- name: dates_gaap
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Reference
    Table: DatesGAAP
    Partitions:
      - UN

ColumnSpecs:
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  PolicySectionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  PolicyTransactionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        - source: COLUMN
          name: KeyFactuurnummer
        sep: ':'
  TransactionDate:
    dateTimeFormat: ISO
  USGAAP_Date:
    NotInSource: True
