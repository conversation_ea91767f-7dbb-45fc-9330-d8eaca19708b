import pyspark.sql.functions as F
import pyspark.sql.types as T
from pyspark.sql import DataFrame

from models_scripts.transformations.common.traits import business_logic


def fill_key_reserving(main_df: DataFrame, policy_df: DataFrame) -> DataFrame:
    original_cols = main_df.columns

    df = main_df.join(
        policy_df.select(
            F.col("KeyIdPolis"),
            F.col("PolicyProductCode").cast(T.StringType()),
            F.col("AssuredMainActivityCode").cast(T.StringType()),
        ),
        "KeyIdPolis",
        how="left",
    )

    df = df.withColumn(
        "KeyReserving",
        F.when(
            F.col("PolicyProductCode") == "RC02",
            F.concat_ws(
                ":",
                F.lit("ES"),
                F.col("PolicyProductCode"),
                F.coalesce(F.col("AssuredMainActivityCode"), F.lit("")),
            ),
        ).otherwise(
            F.concat_ws(
                ":", F.lit("ES"), F.coalesce(F.col("PolicyProductCode"), F.lit(""))
            )
        ),
    )
    if "KeyReserving" not in original_cols:
        original_cols.append("KeyReserving")
    return df.select(*original_cols)


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["Section_main"]
    policy_df = df_dict["Policy_Policy"]

    output_df = fill_key_reserving(main_df, policy_df)

    return output_df
