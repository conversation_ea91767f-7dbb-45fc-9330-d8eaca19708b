import pyspark.sql.functions as F
from pyspark.sql import DataFrame

from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.add_columns import AddUSGAAPDateColumn


def create_transaction_columns(df: DataFrame) -> DataFrame:
    """Generate the Policy.Transaction columns."""
    transaction_df = df.select(
        "KeyIdPolis",
        "KeyDekkingsNummer",
        "KeyFactuurnummer",
        F.lit("EUR").alias("OriginalCurrencyCode"),
        F.lit(1).alias("RateOfExchange"),
        F.lit("EUR").alias("SettlementCurrencyCode"),
        "TransactionDate",
        F.col("KeyFactuurnummer").alias("TransactionReference"),
        F.lit("NEW").alias("TransactionTypeCode"),
        F.lit("NEW").alias("TransactionTypeDescription"),
    )
    return transaction_df


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["Transaction_main"]
    policy_df = df_dict["Policy_Policy"]
    gaap_df = df_dict["Reference_DatesGAAP"]

    main_df = create_transaction_columns(main_df)
    output_df = AddUSGAAPDateColumn(main_df, gaap_df, policy_df).add_usgaap_date()

    return output_df
