Country: ES
existCustomTransformation: 'True'

dataSource:
- name: main
  type: DatabricksCatalogSQLQuery
  parameters:
    sourceSystem: SCS
    catalogName:
      mintdatalake:
        dev: test_mintdatalake_02
        uat: test_mintdatalake_02
        prod: prod_mintdatalake_02
    sqlFileName: transaction.sql

- name: dates_gaap
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Reference
    Table: DatesGAAP
    Partitions:
      - UN

- name: policy
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Policy
    Table: Policy

ColumnSpecs:
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  PolicySectionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  PolicyTransactionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        - source: COLUMN
          name: KeyFactuurnummer
        sep: ':'
  TransactionDate:
    dateTimeFormat: dd/MM/yyyy
  RateOfExchange:
    locale: en_US.utf8
  TransactionSequenceNumber:
    locale: en_US.utf8
  USGAAP_Date:
    NotInSource: True
