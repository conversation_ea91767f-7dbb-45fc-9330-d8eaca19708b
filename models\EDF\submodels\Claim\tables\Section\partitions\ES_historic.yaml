Country: ES
existCustomTransformation: 'False'

dataSource:
- name: main
  type: SourceCSV
  parameters:
    fileName: EDF ClaimArraySection.csv
    Separator: ','
    Encoding: UTF-8
    sourceSystem: SCS

ColumnSpecs:
  ClaimsID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyInternSchadenummer
        sep: ':'
  ClaimsSectionPolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  ClaimsSectionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyInternSchadenummer
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
