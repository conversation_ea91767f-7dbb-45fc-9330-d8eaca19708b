SELECT DISTINCT
    'SISnet' AS Source,
    B.POLICODE AS 'KeyIdPolis',
    C.SECTREFE AS 'KeyDekkingsNummer',
    A<PERSON>ID_MEDFPOTR AS 'KeyFactuurnummer',
    <PERSON><PERSON><PERSON>RCOADI<PERSON> AS 'TransactionComponentAdditionsDeductionsIndicator',
    <PERSON><PERSON>RACO<PERSON> AS 'TransactionComponentAmount',
    D<PERSON>TRCOTY<PERSON> AS 'TransactionComponentTypeCode',
    D<PERSON>TRCOTYDE AS 'TransactionComponentTypeDescription',
    D<PERSON>TRANCOMP AS 'TransactionComponentPercentage',
    D<PERSON>TRACOMTE AS 'TransactionComponentTerritory'
FROM
    NTJDWHMRK..MEDFPOTR A,
    NTJDWHMRK..MEDFPOLI B,
    NTJDWHMRK..MEDFPOSE C,
    NTJDWHMRK..MEDFPOTC D
WHERE
    B.ID_MEDFPOLI = C.ID_MEDFPOLI_FK
    AND C.ID_MEDFPOSE = A.ID_MEDFPOSE_FK
    AND A.ID_MEDFPOTR = D.ID_MEDFPOTR_FK
    AND B.FECHALTA >= CONVERT(datetime,'01/01/2000',103)
