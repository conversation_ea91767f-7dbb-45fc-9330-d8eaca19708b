import pyspark.sql.functions as F
from pyspark.sql import DataFrame

from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.backward_populate_records import (
    COL_AMMOUNT,
    COL_AMMOUNT_NEW,
    COL_COD,
    COL_DK,
    COL_FAC,
    COL_ID,
    NEW_TRANSACTION_ID,
    clean_white_spaces,
    conform_to_string,
)
from models_scripts.transformations.edf.common.currency import PolicyCurrencyConversor


def add_new_components(main_df: DataFrame, new_components_df: DataFrame) -> DataFrame:
    """Add new records to the main table. Generated by the R script"""

    main_df = conform_to_string(main_df, (COL_ID, COL_DK, COL_COD, COL_FAC))
    new_components_df = conform_to_string(new_components_df, (COL_ID, COL_DK, COL_COD))

    main_df = clean_white_spaces(main_df)
    new_components_df = clean_white_spaces(new_components_df)

    new_components_df = new_components_df.select(
        COL_ID,
        COL_DK,
        COL_COD,
        F.col(COL_AMMOUNT).alias(COL_AMMOUNT_NEW),
    )

    joined_df = main_df.join(
        new_components_df, on=[COL_ID, COL_DK, COL_COD], how="outer"
    )
    joined_df = (
        joined_df.withColumn(
            COL_AMMOUNT, F.coalesce(F.col(COL_AMMOUNT_NEW), F.col(COL_AMMOUNT))
        )
        .withColumn(COL_FAC, F.coalesce(F.col(COL_FAC), F.lit(NEW_TRANSACTION_ID)))
        .drop(COL_AMMOUNT_NEW)
    )

    return joined_df


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["TransactionComponent_main"]
    new_components_df = df_dict["Support_NewTransactionComponents"]
    exchange_rate_df = df_dict["TransactionComponent_exchange_rate"]

    output_df = add_new_components(main_df, new_components_df)
    output_with_converted_currencies_df = (
        PolicyCurrencyConversor.add_new_columns_using_single_currency(
            output_df, exchange_rate_df, "EUR"
        )
    )

    return output_with_converted_currencies_df
