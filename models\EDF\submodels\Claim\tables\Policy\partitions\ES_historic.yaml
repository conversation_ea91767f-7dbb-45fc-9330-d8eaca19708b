Country: ES
existCustomTransformation: 'True'

dataSource:
- name: main
  type: SourceCSV
  parameters:
    fileName: EDF ClaimArrayPolicy.csv
    Separator: ','
    Encoding: UTF-8
    sourceSystem: SCS
- name: policy
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Policy
    Table: Policy
    Partitions:
      - ES
      - ES_historic

ColumnSpecs:
  ClaimsPolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  ClaimsID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyInternSchadenummer
        sep: ':'
  PolicyYearOfAccount:
    NotInSource: True
