# Use the official Ubuntu image as a base
FROM ubuntu:22.04

# Avoid interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Install necessary packages
RUN apt-get update && \
    apt-get install -y \
    curl \
    jq \
    python3 \
    python3-pip \
    python3-venv \
    git \
    unzip \
    vim \
    zsh \
    docker.io \
    fonts-powerline

# Install Poetry
ENV POETRY_VERSION=1.8.3
RUN curl -sSL https://install.python-poetry.org | POETRY_HOME=/etc/poetry python3 -
ENV PATH="/etc/poetry/bin:$PATH"

# Install Databricks CLI
RUN curl -fsSL https://raw.githubusercontent.com/databricks/setup-cli/main/install.sh | sh

# Set up the working directory
WORKDIR /workspaces

CMD ["sleep", "infinity"]
