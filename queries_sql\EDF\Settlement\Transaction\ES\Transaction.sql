SELECT
    A.POLICODE AS 'KeyIdPolis',
    B<PERSON>SECTREFE AS 'KeyDekkingsNummer',
    <PERSON><PERSON>TRANREFE AS 'KeyFactuurnummer',
    <PERSON><PERSON>UR<PERSON> AS 'OriginalCurrencyCode',
    <PERSON><PERSON>RATEOFEX AS 'RateOfExchange',
    <PERSON><PERSON> AS 'SettlementCurrencyCode',
    <PERSON><PERSON> AS 'TransactionDate',
    <PERSON><PERSON>TRANR<PERSON> AS 'TransactionReference',
    C<PERSON>TRATYPCO AS 'TransactionTypeCode',
    <PERSON><PERSON>TRATYPDE AS 'TransactionTypeDescription',
    C<PERSON>TRASEQNU AS 'TransactionSequenceNumber',
    '' AS 'PreviousSourceSystem',
    '' AS 'PreviousSourceSystemDescription',
    '' AS 'PreviousTransactionReference'
FROM
    NTJDWHMRK..MEDFRECI A,
    NTJDWHMRK..MEDFRESE B,
    NTJDWHMRK..MEDFRETR C
WHERE
    A.ID_MEDFRECI = B.ID_MEDFRECI_FK
    AND B.ID_MEDFRESE = C.ID_MEDFRESE_FK
    AND C.TRASDATE >= CONVERT(datetime,'01/01/2000',103)
