import pytest
from mines2.core.extensions.misc import assert_dataframe_equality


def test_unify_keyDekkingsNummer_with_claim_section(spark):
    from pyspark.sql import Row
    from pyspark.sql.types import IntegerType, StringType, StructField, StructType

    from models_scripts.transformations.edf.common.misc import (
        unify_keyDekkingsNummer_with_claim_section,
    )

    df = spark.createDataFrame(
        [
            Row(ID=1, KeyInternSchadenummer=1, KeyDekkingsNummer=1),
            Row(ID=2, KeyInternSchadenummer=2, KeyDekkingsNummer=2),
            Row(ID=3, KeyInternSchadenummer=3, KeyDekkingsNummer=3),
            Row(ID=4, KeyInternSchadenummer=1, KeyDekkingsNummer=11),
            Row(ID=5, KeyInternSchadenummer=2, KeyDekkingsNummer=22),
            Row(ID=6, KeyInternSchadenummer=3, KeyDekkingsNummer=33),
        ]
    )
    section_df = spark.createDataFrame(
        [
            Row(KeyInternSchadenummer=1, KeyDekkingsNummer=11),
            Row(KeyInternSchadenummer=2, KeyDekkingsNummer=22),
            Row(KeyInternSchadenummer=3, KeyDekkingsNummer=33),
        ]
    )

    result_df = unify_keyDekkingsNummer_with_claim_section(df, section_df)

    expected_df = spark.createDataFrame(
        [
            Row(
                ID=1,
                KeyInternSchadenummer=1,
                KeyDekkingsNummer=11,
                MINES_MarkedRecord="unify_keyDekkingsNummer_with_claim_section",
            ),
            Row(
                ID=2,
                KeyInternSchadenummer=2,
                KeyDekkingsNummer=22,
                MINES_MarkedRecord="unify_keyDekkingsNummer_with_claim_section",
            ),
            Row(
                ID=3,
                KeyInternSchadenummer=3,
                KeyDekkingsNummer=33,
                MINES_MarkedRecord="unify_keyDekkingsNummer_with_claim_section",
            ),
            Row(
                ID=4,
                KeyInternSchadenummer=1,
                KeyDekkingsNummer=11,
                MINES_MarkedRecord=None,
            ),
            Row(
                ID=5,
                KeyInternSchadenummer=2,
                KeyDekkingsNummer=22,
                MINES_MarkedRecord=None,
            ),
            Row(
                ID=6,
                KeyInternSchadenummer=3,
                KeyDekkingsNummer=33,
                MINES_MarkedRecord=None,
            ),
        ]
    )

    assert_dataframe_equality(result_df, expected_df)
