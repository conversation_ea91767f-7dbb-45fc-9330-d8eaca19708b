Columns:
  ClaimsSectionPolicyID:
    dataType: string
  PolicyID:
    dataType: string
  PolicySectionID:
    dataType: string
  KeyIdPolis:
    dataType: string
  KeyDekkingsNummer:
    dataType: string
  CostBasisCode:
    dataType: string
  CostBasisDescription:
    dataType: string
  CoverageType:
    dataType: string
  Deductible:
    dataType: float
  DeductibleRounded:
    dataType: currency
  DeductibleGBP:
    dataType: double
  DeductibleEUR:
    dataType: double
  DeductibleUSD:
    dataType: double
  DeductibleCAD:
    dataType: double
  DeductibleRoundedGBP:
    dataType: currency
  DeductibleRoundedEUR:
    dataType: currency
  DeductibleRoundedUSD:
    dataType: currency
  DeductibleRoundedCAD:
    dataType: currency
  EstSignedDown:
    dataType: float
  IBCCoverageCode:
    dataType: string
  InsurerCarrierCode:
    dataType: string
  InsurerCarrierDescription:
    dataType: string
  InsurerCarrierPercentage:
    dataType: float
  Jurisdiction:
    dataType: string
  NoClaimsBonus:
    dataType: string
  OperatingTerritory:
    dataType: string
  PremiumBasisCode:
    dataType: string
  ProfitCommission:
    dataType: float
  RateOnExposure:
    dataType: string
  SectionEffectiveFromDate:
    dataType: date
  SectionEffectiveToDate:
    dataType: date
  SectionProductCode:
    dataType: string
  SectionProductDescription:
    dataType: string
  SectionReference:
    dataType: string
  SectionStatusCode:
    dataType: string
  SignedLine:
    dataType: float
  SignedOrder:
    dataType: float
  SubLimitsIndicator:
    dataType: string
  TerritorialScope:
    dataType: string
  WrittenLine:
    dataType: float
  WrittenOrder:
    dataType: float
  KeyReserving:
    dataType: string
    restrictNull: True

primaryKey:
  - PolicySectionID
