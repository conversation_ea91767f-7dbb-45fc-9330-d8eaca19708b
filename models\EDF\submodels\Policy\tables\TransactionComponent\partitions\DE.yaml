Country: custom
existCustomTransformation: 'True'

dataSource:
- name: main
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: dev_europeandatalake_01
      uat: uat_europeandatalake_01
      prod: prod_europeandatalake_01
    schema: DE_Direct
    table: Policy_PolicyArrayTransactionComponent
    sourceSystem: SPARK_EDF_EXPORT

- name: exchange_rate
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: test_mintdatalake_02
      uat: test_mintdatalake_02
      prod: prod_mintdatalake_02
    schema: rdm_dbo
    table: mint_mapping_roe
    sourceSystem: MINT_RAW_DATALAKE

- name:  transaction
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Policy
    Table: Transaction

- name: policy
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Policy
    Table: Policy

ColumnSpecs:
  TransactionComponentAmount:
    locale: en_US.utf8
  TransactionComponentPercentage:
    locale: en_US.utf8
  TransactionComponentAdditionsDeductionsIndicator:
    sourceName: TransactionComponentAdditionsDeductionsindicator
  PolicyTransComponentID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: DE
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        - source: COLUMN
          name: KeyFactuurnummer
        - source: COLUMN
          name: TransactionComponentTypeCode
        sep: ':'
  PolicyTransactionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: DE
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        - source: COLUMN
          name: KeyFactuurnummer
        sep: ':'
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: DE
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  TransactionComponentAmountGBP:
    NotInSource: True
  TransactionComponentAmountEUR:
    NotInSource: True
  TransactionComponentAmountUSD:
    NotInSource: True
  TransactionComponentAmountCAD:
    NotInSource: True
  TransactionComponentAmountRounded:
    NotInSource: True
  TransactionComponentAmountRoundedGBP:
    NotInSource: True
  TransactionComponentAmountRoundedEUR:
    NotInSource: True
  TransactionComponentAmountRoundedUSD:
    NotInSource: True
  TransactionComponentAmountRoundedCAD:
    NotInSource: True
