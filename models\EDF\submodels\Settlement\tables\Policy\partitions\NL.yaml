Country: NL
existCustomTransformation: 'True'
dataSource:
- name: main
  type: SourceProgressNL
  parameters:
    sqlFileName: Query SettlementArrayPolicy.sql
    querySourceType: SQL_FILE
ColumnSpecs:
  KeyIdPolis:
    locale: en_US.utf8
  PolicyCode:
    locale: en_US.utf8
  PolicyReference:
    locale: en_US.utf8
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'