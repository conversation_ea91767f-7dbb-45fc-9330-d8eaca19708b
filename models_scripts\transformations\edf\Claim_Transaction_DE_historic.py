import pyspark.sql.functions as F
from pyspark.sql import DataFrame

from models_scripts.transformations.common.misc import enforce_nulls_type
from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.DE_historic_openviva.get_openviva_boekings_nummer import (
    get_openviva_boekings_nummer,
)
from models_scripts.transformations.edf.DE_historic_openviva.get_openviva_transaction_sequence_number import (
    get_openviva_transaction_sequence_number,
)


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    # Load tables
    input_df = df_dict["Transaction_main"]

    # add TransactionSequenceNumber
    output_w_transsec_df = get_openviva_transaction_sequence_number(
        input_df, "yyyyMMddHHmmss"
    )

    # add KeySchadeBoekingsNummer
    output_w_boeking_nummer_df = get_openviva_boekings_nummer(output_w_transsec_df)

    # create TransactionReference from KeySchadeBoekingsNummer
    output_w_trans_ref_df = output_w_boeking_nummer_df.withColumn(
        "TransactionReference", F.col("KeySchadeBoekingsNummer")
    )

    # Clean TransactionDate and TransactionAuthenticationDate columns
    output_clean_transdate = output_w_trans_ref_df.withColumn(
        "TransactionDate", F.to_date(F.col("TransactionDate"), "yyyy-MM-dd")
    )
    output_clean_transauthdate = output_clean_transdate.withColumn(
        "TransactionAuthorisationDate",
        F.to_date(F.col("TransactionAuthorisationDate"), "yyyy-MM-dd"),
    )

    output_df = enforce_nulls_type(output_clean_transauthdate)

    return output_df
