resources:
  jobs:
    model_ingestion_job_EDF:
      name: model_ingestion_job [EDF]

      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 14400  # 4h
      timeout_seconds: 18000  # 5h
      max_concurrent_runs: 4

      tasks:
        # DE, DE_historic partitions
        - task_key: orchestration_DE_AND_historic  # Solve Dependencies
          notebook_task:
            notebook_path: ../../src/generic_runner_caller/generic_runner_caller.ipynb
            base_parameters:
              module_name: orchestration.runner
              root_path: ${var.mines_root_path}
              args: '["DE,DE_historic", "All", "{{job.run_id}}_DE_AND_historic", "{{job.start_time.[iso_datetime]}}", "EDF"]'
              model_name: EDF
          job_cluster_key: generic_job_cluster
          libraries:
            - pypi:
                package: mines2

        - task_key: bronze_DE_AND_historic
          depends_on:
            - task_key: orchestration_DE_AND_historic
          notebook_task:
            notebook_path: ../../src/generic_runner_caller/generic_runner_caller.ipynb
            base_parameters:
              module_name: bronze.runner
              root_path: ${var.mines_root_path}
              args: '["EDF", "/mnt/system/${var.mines_root_path}/adf_runs/{{job.run_id}}_DE_AND_historic/tables.json", "{{job.run_id}}_DE_AND_historic"]'
              model_name: EDF
          job_cluster_key: generic_job_cluster
          max_retries: 1
          min_retry_interval_millis: 10000 # 10 seconds
          retry_on_timeout: true
          timeout_seconds: 5400  # 1h30min
          libraries:
            - jar: /Volumes/${var.edl_catalog_name}/default/scripts/libraries/mssql-jdbc-12.6.1.jre8.jar
            - jar: /Volumes/${var.edl_catalog_name}/default/scripts/libraries/ojdbc8-*********.09.jar
            - pypi:
                package: mines2

        - task_key: singlepartition_DE_AND_historic
          depends_on:
            - task_key: bronze_DE_AND_historic
          notebook_task:
            notebook_path: ../../src/generic_runner_caller/generic_runner_caller.ipynb
            base_parameters:
              module_name: singlepartition.runner
              root_path: ${var.mines_root_path}
              import_models_transformations: "True"
              args: '["EDF", "{{job.start_time.[iso_datetime]}}", "/mnt/system/${var.mines_root_path}/adf_runs/{{job.run_id}}_DE_AND_historic/tables_by_phase.json", "{{job.run_id}}_DE_AND_historic", "0.5", "{}"]'
              model_name: EDF
          job_cluster_key: generic_job_cluster
          max_retries: 1
          min_retry_interval_millis: 10000 # 10 seconds
          retry_on_timeout: true
          timeout_seconds: 3600  # 1h
          libraries:
            - pypi:
                package: mines2

        # ES, ES_historic, ES_OutsideSisNet partitions
        - task_key: orchestration_ES_AND_historic_AND_OutsideSisNet  # Solve Dependencies
          notebook_task:
            notebook_path: ../../src/generic_runner_caller/generic_runner_caller.ipynb
            base_parameters:
              module_name: orchestration.runner
              root_path: ${var.mines_root_path}
              args: '["ES,ES_historic,ES_OutsideSisNet", "All", "{{job.run_id}}_ES_AND_historic_AND_OutsideSisNet", "{{job.start_time.[iso_datetime]}}", "EDF"]'
              model_name: EDF
          job_cluster_key: generic_job_cluster
          libraries:
            - pypi:
                package: mines2

        - task_key: bronze_ES_AND_historic_AND_OutsideSisNet
          depends_on:
            - task_key: orchestration_ES_AND_historic_AND_OutsideSisNet
          notebook_task:
            notebook_path: ../../src/generic_runner_caller/generic_runner_caller.ipynb
            base_parameters:
              module_name: bronze.runner
              root_path: ${var.mines_root_path}
              args: '["EDF", "/mnt/system/${var.mines_root_path}/adf_runs/{{job.run_id}}_ES_AND_historic_AND_OutsideSisNet/tables.json", "{{job.run_id}}_ES_AND_historic_AND_OutsideSisNet"]'
              model_name: EDF
          job_cluster_key: generic_job_cluster
          max_retries: 1
          min_retry_interval_millis: 10000 # 10 seconds
          retry_on_timeout: true
          timeout_seconds: 3600  # 1h
          libraries:
            - jar: /Volumes/${var.edl_catalog_name}/default/scripts/libraries/mssql-jdbc-12.6.1.jre8.jar
            - pypi:
                package: mines2

        - task_key: singlepartition_ES_AND_historic_AND_OutsideSisNet
          depends_on:
            - task_key: bronze_ES_AND_historic_AND_OutsideSisNet
          notebook_task:
            notebook_path: ../../src/generic_runner_caller/generic_runner_caller.ipynb
            base_parameters:
              module_name: singlepartition.runner
              root_path: ${var.mines_root_path}
              import_models_transformations: "True"
              args: '["EDF", "{{job.start_time.[iso_datetime]}}", "/mnt/system/${var.mines_root_path}/adf_runs/{{job.run_id}}_ES_AND_historic_AND_OutsideSisNet/tables_by_phase.json", "{{job.run_id}}_ES_AND_historic_AND_OutsideSisNet", "0.5", "{}"]'
              model_name: EDF
          job_cluster_key: driver_demanding_job_cluster
          max_retries: 1
          min_retry_interval_millis: 10000 # 10 seconds
          retry_on_timeout: true
          timeout_seconds: 9000  # 2h30m
          libraries:
            - pypi:
                package: mines2

        # FR partition
        - task_key: orchestration_FR  # Solve Dependencies
          notebook_task:
            notebook_path: ../../src/generic_runner_caller/generic_runner_caller.ipynb
            base_parameters:
              module_name: orchestration.runner
              root_path: ${var.mines_root_path}
              args: '["FR", "All", "{{job.run_id}}_FR", "{{job.start_time.[iso_datetime]}}", "EDF"]'
              model_name: EDF
          job_cluster_key: generic_job_cluster
          libraries:
            - pypi:
                package: mines2

        - task_key: bronze_FR
          depends_on:
            - task_key: orchestration_FR
          notebook_task:
            notebook_path: ../../src/generic_runner_caller/generic_runner_caller.ipynb
            base_parameters:
              module_name: bronze.runner
              root_path: ${var.mines_root_path}
              args: '["EDF", "/mnt/system/${var.mines_root_path}/adf_runs/{{job.run_id}}_FR/tables.json", "{{job.run_id}}_FR"]'
              model_name: EDF
          job_cluster_key: generic_job_cluster
          max_retries: 1
          min_retry_interval_millis: 10000 # 10 seconds
          retry_on_timeout: true
          timeout_seconds: 1800  # 30 minutes
          libraries:
            - pypi:
                package: mines2

        - task_key: singlepartition_FR
          depends_on:
            - task_key: bronze_FR
          notebook_task:
            notebook_path: ../../src/generic_runner_caller/generic_runner_caller.ipynb
            base_parameters:
              module_name: singlepartition.runner
              root_path: ${var.mines_root_path}
              import_models_transformations: "True"
              args: '["EDF", "{{job.start_time.[iso_datetime]}}", "/mnt/system/${var.mines_root_path}/adf_runs/{{job.run_id}}_FR/tables_by_phase.json", "{{job.run_id}}_FR", "0.5", "{}"]'
              model_name: EDF
          job_cluster_key: generic_job_cluster
          max_retries: 1
          min_retry_interval_millis: 10000 # 10 seconds
          retry_on_timeout: true
          timeout_seconds: 1800  # 30 minutes
          libraries:
            - pypi:
                package: mines2

        # NL partition
        - task_key: orchestration_NL  # Solve Dependencies
          notebook_task:
            notebook_path: ../../src/generic_runner_caller/generic_runner_caller.ipynb
            base_parameters:
              module_name: orchestration.runner
              root_path: ${var.mines_root_path}
              args: '["NL", "All", "{{job.run_id}}_NL", "{{job.start_time.[iso_datetime]}}", "EDF"]'
              model_name: EDF
          job_cluster_key: generic_job_cluster
          libraries:
            - pypi:
                package: mines2

        - task_key: bronze_NL
          depends_on:
            - task_key: orchestration_NL
          notebook_task:
            notebook_path: ../../src/generic_runner_caller/generic_runner_caller.ipynb
            base_parameters:
              module_name: bronze.runner
              root_path: ${var.mines_root_path}
              args: '["EDF", "/mnt/system/${var.mines_root_path}/adf_runs/{{job.run_id}}_NL/tables.json", "{{job.run_id}}_NL"]'
              model_name: EDF
          job_cluster_key: generic_job_cluster
          max_retries: 1
          min_retry_interval_millis: 10000 # 10 seconds
          retry_on_timeout: true
          timeout_seconds: 5400  # 1h30min
          libraries:
            - jar: /Volumes/${var.edl_catalog_name}/default/scripts/libraries/openedge.jar
            - pypi:
                package: mines2

        - task_key: singlepartition_NL
          depends_on:
            - task_key: bronze_NL
          notebook_task:
            notebook_path: ../../src/generic_runner_caller/generic_runner_caller.ipynb
            base_parameters:
              module_name: singlepartition.runner
              root_path: ${var.mines_root_path}
              import_models_transformations: "True"
              args: '["EDF", "{{job.start_time.[iso_datetime]}}", "/mnt/system/${var.mines_root_path}/adf_runs/{{job.run_id}}_NL/tables_by_phase.json", "{{job.run_id}}_NL", "0.5", "{}"]'
              model_name: EDF
          job_cluster_key: driver_demanding_job_cluster
          max_retries: 1
          min_retry_interval_millis: 10000 # 10 seconds
          retry_on_timeout: true
          timeout_seconds: 7200  # 2h
          libraries:
            - pypi:
                package: mines2

        # UN partition
        - task_key: orchestration_UN  # Solve Dependencies
          notebook_task:
            notebook_path: ../../src/generic_runner_caller/generic_runner_caller.ipynb
            base_parameters:
              module_name: orchestration.runner
              root_path: ${var.mines_root_path}
              args: '["UN", "All", "{{job.run_id}}_UN", "{{job.start_time.[iso_datetime]}}", "EDF"]'
              model_name: EDF
          job_cluster_key: generic_job_cluster
          libraries:
            - pypi:
                package: mines2

        - task_key: bronze_UN
          depends_on:
            - task_key: orchestration_UN
          notebook_task:
            notebook_path: ../../src/generic_runner_caller/generic_runner_caller.ipynb
            base_parameters:
              module_name: bronze.runner
              root_path: ${var.mines_root_path}
              args: '["EDF", "/mnt/system/${var.mines_root_path}/adf_runs/{{job.run_id}}_UN/tables.json", "{{job.run_id}}_UN"]'
              model_name: EDF
          job_cluster_key: generic_job_cluster
          max_retries: 1
          min_retry_interval_millis: 10000 # 10 seconds
          retry_on_timeout: true
          timeout_seconds: 1800  # 30 minutes
          libraries:
            - jar: /Volumes/${var.edl_catalog_name}/default/scripts/libraries/mssql-jdbc-12.6.1.jre8.jar
            - jar: /Volumes/${var.edl_catalog_name}/default/scripts/libraries/ojdbc8-*********.09.jar
            - pypi:
                package: mines2

        - task_key: singlepartition_UN
          depends_on:
            - task_key: bronze_UN
          notebook_task:
            notebook_path: ../../src/generic_runner_caller/generic_runner_caller.ipynb
            base_parameters:
              module_name: singlepartition.runner
              root_path: ${var.mines_root_path}
              import_models_transformations: "True"
              args: '["EDF", "{{job.start_time.[iso_datetime]}}", "/mnt/system/${var.mines_root_path}/adf_runs/{{job.run_id}}_UN/tables_by_phase.json", "{{job.run_id}}_UN", "0.5", "{}"]'
              model_name: EDF
          job_cluster_key: generic_job_cluster
          max_retries: 1
          min_retry_interval_millis: 10000 # 10 seconds
          retry_on_timeout: true
          timeout_seconds: 1800  # 30 minutes
          libraries:
            - pypi:
                package: mines2

        # All partitions
        - task_key: orchestration_All  # Solve Dependencies
          depends_on:
            - task_key: singlepartition_DE_AND_historic
            - task_key: singlepartition_ES_AND_historic_AND_OutsideSisNet
            - task_key: singlepartition_FR
            - task_key: singlepartition_NL
            - task_key: singlepartition_UN
          run_if: AT_LEAST_ONE_SUCCESS
          notebook_task:
            notebook_path: ../../src/generic_runner_caller/generic_runner_caller.ipynb
            base_parameters:
              module_name: orchestration.runner
              root_path: ${var.mines_root_path}
              args: '["DE,DE_historic,ES,ES_historic,ES_OutsideSisNet,FR,NL,UN", "All", "{{job.run_id}}", "{{job.start_time.[iso_datetime]}}", "EDF"]'
              model_name: EDF
          job_cluster_key: generic_job_cluster
          libraries:
            - pypi:
                package: mines2

        - task_key: crosspartition
          depends_on:
            - task_key: orchestration_All
          notebook_task:
            notebook_path: ../../src/generic_runner_caller/generic_runner_caller.ipynb
            base_parameters:
              module_name: crosspartitions.runner
              root_path: ${var.mines_root_path}
              args: '["{{job.start_time.[iso_datetime]}}", "/mnt/system/${var.mines_root_path}/adf_runs/{{job.run_id}}/tables.json", "DE,DE_historic,ES,ES_historic,ES_OutsideSisNet,FR,NL,UN", "EDF", "{{job.run_id}}"]'
              model_name: EDF
          job_cluster_key: generic_job_cluster2
          max_retries: 1
          min_retry_interval_millis: 10000 # 10 seconds
          retry_on_timeout: true
          timeout_seconds: 4800  # 1h20m
          libraries:
            - pypi:
                package: mines2

        - task_key: crosstable
          depends_on:
            - task_key: crosspartition
          notebook_task:
            notebook_path: ../../src/generic_runner_caller/generic_runner_caller.ipynb
            base_parameters:
              module_name: cross_table.runner
              root_path: ${var.mines_root_path}
              args: '["EDF", "{{job.run_id}}", "/mnt/system/${var.mines_root_path}/adf_runs/{{job.run_id}}/"]'
              model_name: EDF
          job_cluster_key: generic_job_cluster2
          max_retries: 1
          min_retry_interval_millis: 10000 # 10 seconds
          retry_on_timeout: true
          timeout_seconds: 4800  # 1h20m
          libraries:
            - pypi:
                package: mines2

        - task_key: published  # Model validation
          depends_on:
            - task_key: crosstable
          notebook_task:
            notebook_path: ../../src/generic_runner_caller/generic_runner_caller.ipynb
            base_parameters:
              module_name: published.runner
              root_path: ${var.mines_root_path}
              args: '["EDF", "0.5", "{{job.run_id}}"]'
              model_name: EDF
          job_cluster_key: generic_job_cluster2
          libraries:
            - pypi:
                package: mines2

        - task_key: run_monitor_status
          depends_on:
            - task_key: published
          run_job_task:
            job_id: ${resources.jobs.monitor_status_job_EDF.id}
      
      job_clusters:
        - job_cluster_key: generic_job_cluster
          new_cluster:
          # Order of parameters determined by the order in the databricks API documentation
            autoscale:
              min_workers: 2
              max_workers: 8
            cluster_name: ""
            spark_version: ${var.spark_version}
            spark_conf:
              spark.databricks.delta.preview.enabled: "true"
              spark.databricks.delta.schema.autoMerge.enabled: "true"
              spark.databricks.delta.rowLevelConcurrencyPreview: "true"
            azure_attributes:
              first_on_demand: 1
              availability: ON_DEMAND_AZURE
              spot_bid_max_price: -1
            node_type_id: Standard_E4ds_v4
            driver_node_type_id: Standard_E4d_v4
            custom_tags:
              Project: ${bundle.target}_${bundle.name}
            cluster_log_conf:
              dbfs:
                destination: dbfs:/cluster-logs
            init_scripts:
              - workspace:
                  destination: ${var.jfrog_pip_artifacts_path}
            spark_env_vars:
              MINES_ENVIRONMENT: ${var.environment_code}
              ARTIFACTORY_USER: "{{secrets/Key-vault-secret/markel-jfrog-user}}"
              ARTIFACTORY_TOKEN: "{{secrets/Key-vault-secret/markel-jfrog-api-token}}"
            enable_elastic_disk: true
            data_security_mode: SINGLE_USER
            runtime_engine: PHOTON

        - job_cluster_key: generic_job_cluster2
          new_cluster:
          # Order of parameters determined by the order in the databricks API documentation
            autoscale:
              min_workers: 2
              max_workers: 8
            cluster_name: ""
            spark_version: ${var.spark_version}
            spark_conf:
              spark.databricks.delta.preview.enabled: "true"
              spark.databricks.delta.schema.autoMerge.enabled: "true"
              spark.databricks.delta.rowLevelConcurrencyPreview: "true"
            azure_attributes:
              first_on_demand: 1
              availability: ON_DEMAND_AZURE
              spot_bid_max_price: -1
            node_type_id: Standard_E4d_v4
            driver_node_type_id: Standard_DS5_v2
            custom_tags:
              Project: ${bundle.target}_${bundle.name}
            cluster_log_conf:
              dbfs:
                destination: dbfs:/cluster-logs
            init_scripts:
              - workspace:
                  destination: ${var.jfrog_pip_artifacts_path}
            spark_env_vars:
              MINES_ENVIRONMENT: ${var.environment_code}
              ARTIFACTORY_USER: "{{secrets/Key-vault-secret/markel-jfrog-user}}"
              ARTIFACTORY_TOKEN: "{{secrets/Key-vault-secret/markel-jfrog-api-token}}"
            enable_elastic_disk: true
            data_security_mode: SINGLE_USER
            runtime_engine: PHOTON

        - job_cluster_key: driver_demanding_job_cluster
          new_cluster:
          # Order of parameters determined by the order in the databricks API documentation
            autoscale:
              min_workers: 2
              max_workers: 8
            cluster_name: ""
            spark_version: ${var.spark_version}
            spark_conf:
              spark.databricks.delta.preview.enabled: "true"
              spark.databricks.delta.schema.autoMerge.enabled: "true"
              spark.databricks.delta.rowLevelConcurrencyPreview: "true"
            azure_attributes:
              first_on_demand: 1
              availability: ON_DEMAND_AZURE
              spot_bid_max_price: -1
            node_type_id: Standard_E4ds_v4
            driver_node_type_id: Standard_D16ads_v5
            custom_tags:
              Project: ${bundle.target}_${bundle.name}
            cluster_log_conf:
              dbfs:
                destination: dbfs:/cluster-logs
            init_scripts:
              - workspace:
                  destination: ${var.jfrog_pip_artifacts_path}
            spark_env_vars:
              MINES_ENVIRONMENT: ${var.environment_code}
              ARTIFACTORY_USER: "{{secrets/Key-vault-secret/markel-jfrog-user}}"
              ARTIFACTORY_TOKEN: "{{secrets/Key-vault-secret/markel-jfrog-api-token}}"
            enable_elastic_disk: true
            data_security_mode: SINGLE_USER
            runtime_engine: PHOTON
