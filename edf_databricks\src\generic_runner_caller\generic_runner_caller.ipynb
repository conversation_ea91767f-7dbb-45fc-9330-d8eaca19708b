{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "8330470c-c60e-4e25-b897-26e7d7d8b77c", "showTitle": true, "title": "Import statements"}}, "outputs": [], "source": ["import importlib\n", "import json\n", "import os\n", "import sys\n", "from subprocess import STDOUT, CalledProcessError, check_output\n", "\n", "import mines2"]}, {"cell_type": "code", "execution_count": null, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "81acfddc-3ec7-4080-a646-3a8bb498d512", "showTitle": true, "title": "Load inputs"}}, "outputs": [], "source": ["# Widgets definition\n", "dbutils.widgets.text(\"module_name\", \"\", \"module name, e.g. silver.runner.\")\n", "dbutils.widgets.text(\"root_path\", \"\", \"RootPath of the pipeline.\")\n", "dbutils.widgets.text(\"import_models_transformations\", \"False\", \"If needs the models transformations to be imported.\")\n", "dbutils.widgets.text(\"args\", \"[]\", \"Extra Parameters to pass to runner.\")  # Before migration will be the args instead of kwargs\n", "dbutils.widgets.text(\"model_name\", \"\", \"model name\")\n", "\n", "# Load into variables\n", "module_name = dbutils.widgets.get(\"module_name\") or None\n", "root_path = dbutils.widgets.get(\"root_path\") or None\n", "import_models_transformations = dbutils.widgets.get(\"import_models_transformations\").lower() == \"true\"\n", "args = json.loads(dbutils.widgets.get(\"args\"))\n", "args = [str(x) for x in args]\n", "model_name = dbutils.widgets.get(\"model_name\") or None\n", "\n", "\n", "# Validate that all variables are set\n", "\n", "assert module_name is not None, \"Variable Not Set\"\n", "assert root_path is not None, \"Variable Not Set\"\n", "assert import_models_transformations is not None, \"Variable Not Set\"\n", "assert args is not None, \"Variable Not Set\"\n", "assert model_name is not None, \"Variable Not Set\"\n", "\n", "# Print variables\n", "print(f\"{module_name=}\")\n", "print(f\"{root_path=}\")\n", "print(f\"{import_models_transformations=}\")\n", "print(f\"{args=}\")\n", "print(f\"{model_name=}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "5471c251-7c7d-4a62-992c-37fdb61acdec", "showTitle": true, "title": "Functions"}}, "outputs": [], "source": ["def install(path: str):\n", "    print(\"Installing Library from Path: \", path)\n", "    try:\n", "        output = check_output(\n", "            [sys.executable, \"-m\", \"pip\", \"install\", path], stderr=STDOUT\n", "        )\n", "    except CalledProcessError as exc:\n", "        print(exc.output)\n", "        raise\n", "\n", "\n", "def get_dbutils():\n", "    if \"DATABRICKS_RUNTIME_VERSION\" in os.environ:\n", "        from databricks.sdk.runtime import dbutils\n", "\n", "        return dbutils\n", "    else:\n", "        from databricks.sdk import WorkspaceClient\n", "\n", "        return WorkspaceClient().dbutils\n", "\n", "\n", "def get_environment() -> str:\n", "    \"\"\"Get Environment from databricks.\"\"\"\n", "    ENVIRONMENT_DEV = \"DEV\"\n", "    ENVIRONMENT_UAT = \"UAT\"\n", "    ENVIRONMENT_PROD = \"PROD\"\n", "    assert os.environ[\"MINES_ENVIRONMENT\"] in (\n", "        ENVIRONMENT_DEV,\n", "        ENVIRONMENT_UAT,\n", "        ENVIRONMENT_PROD,\n", "    ), \"Must be a Known Environment\"\n", "\n", "    return os.environ[\"MINES_ENVIRONMENT\"].lower()\n", "\n", "\n", "def get_catalog() -> str:\n", "    \"\"\"Return the catalog name based on the current environment.\"\"\"\n", "    catalog_ = f\"{get_environment()}_europeandatalake_01\"\n", "    return catalog_\n", "\n", "\n", "def _get_wheel_path(path: str) -> str:\n", "    \"\"\"Get the wheel file from a path.\"\"\"\n", "    wheel_paths = []\n", "    dbutils = get_dbutils()\n", "    for file in dbutils.fs.ls(path):\n", "        if file.name.endswith(\".whl\"):\n", "            wheel_paths.append(os.path.join(path, file.name))\n", "    assert len(wheel_paths) == 1, f\"{wheel_paths=} must be 1!\"\n", "    wheel_path = wheel_paths[0]\n", "\n", "    return wheel_path\n", "\n", "\n", "def install_models_scripts(catalog_: str, path_root: str) -> None:\n", "    \"\"\"Install custom transformation.\"\"\"\n", "    models_scripts_path = os.path.join(\n", "        \"/Volumes\", catalog_, \"default\", \"models\", path_root, \"package\", model_name\n", "    )\n", "    wheel_path = _get_wheel_path(models_scripts_path)\n", "    install(wheel_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "f2184b75-efde-4d52-afbc-e4a39c0d00f1", "showTitle": true, "title": "Main"}}, "outputs": [], "source": ["catalog = get_catalog()\n", "\n", "if import_models_transformations:\n", "    try:\n", "        import models_scripts.transformations as transformations\n", "    except:\n", "        install_models_scripts(catalog, root_path)\n", "\n", "module = importlib.import_module(f\"mines2.{module_name}\")\n", "argv = [module_name, root_path, *args]\n", "print(f\" Running module {module_name=} with args {' '.join(argv)}\")\n", "module.entrypoint(argv)"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"dashboards": [], "language": "python", "notebookMetadata": {"pythonIndentUnit": 4, "widgetLayout": []}, "notebookName": "Generic Runner <PERSON>er", "widgets": {"args": {"currentValue": "[\"EDF\",\"10394\"]", "nuid": "29097ae0-6104-414a-bf76-6fa85398156f", "widgetInfo": {"defaultValue": "[]", "label": "Extra Parameters to pass to runner.", "name": "args", "options": {"autoCreated": null, "validationRegex": null, "widgetType": "text"}, "widgetType": "text"}}, "import_models_transformations": {"currentValue": "False", "nuid": "3de0754b-4e17-4d83-bf82-85f5a9b4d86a", "widgetInfo": {"defaultValue": "False", "label": "If needs the models transformations to be imported.", "name": "import_models_transformations", "options": {"autoCreated": null, "validationRegex": null, "widgetType": "text"}, "widgetType": "text"}}, "kwargs": {"currentValue": "[\"EDF\",\"10394\"]", "nuid": "5f0b2a43-78cb-4083-a1cd-930a7487002b", "widgetInfo": {"defaultValue": "[]", "label": "Extra Parameters to pass to runner.", "name": "kwargs", "options": {"autoCreated": null, "validationRegex": null, "widgetType": "text"}, "widgetType": "text"}}, "module_name": {"currentValue": "monitor.runner", "nuid": "d241c372-159e-4a28-b2a9-61d5920511e1", "widgetInfo": {"defaultValue": "", "label": "module name, e.g. silver.runner.", "name": "module_name", "options": {"autoCreated": null, "validationRegex": null, "widgetType": "text"}, "widgetType": "text"}}, "root_path": {"currentValue": "main", "nuid": "f611d371-6cfe-48db-9dd3-10ba26e01111", "widgetInfo": {"defaultValue": "", "label": "RootPath of the pipeline.", "name": "root_path", "options": {"autoCreated": null, "validationRegex": null, "widgetType": "text"}, "widgetType": "text"}}}}, "kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 0}