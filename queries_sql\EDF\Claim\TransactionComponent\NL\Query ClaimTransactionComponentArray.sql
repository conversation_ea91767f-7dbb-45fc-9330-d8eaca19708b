select sb.internschadenummer                                          as "KeyInternSchadenummer"
, h.idpolis                                                           as "KeyIdPolis"
, ltrim(to_char(sb.dekkingsnummer))                                   as "KeyDekkingsNummer"
, ltrim(to_char(sb.uitkeringsnummer))                                 as "KeySchadeBoekingsNummer"
, sb.bedrag                                                           as "TransactionComponentAmount"
, 'P-' + ltrim(to_char(sb.soortreserve)) + '-' + ltrim(to_char(sb.statistiekcode)) as "TransactionComponentTypeCode"
, 'Paid amount - ' + case when ts.omschrijving is null then '' else ltrim(ts.omschrijving) end as "TransComponentTypeDescription"
from pub.schadeboeking sb
inner join pub.schade s on s.bedrijfinternschadenummer = sb.bedrijfinternschadenummer
inner join pub.histpolisversie h on h.bedrijfinternschadenummer = s.bedrijfinternschadenummer
left outer join pub.tabelstatistiek ts on ts.soortverzekeringstatistiekcode = sb.soortverzekeringstatistiekcode
left outer join (
select a.bedrijfinternschadenummer, max(a.boek<PERSON>) as boekdatum
from pub.schadeboeking a
  group by a.bedrijfinternschadenummer) b
  on b.bedrijfinternschadenummer = sb.bedrijfinternschadenummer
left outer join (
select x.bedrijfinternschadenummer, max(x.boekdatum) as boekdatum
from pub.schadereserve x
  group by x.bedrijfinternschadenummer) m
  on m.bedrijfinternschadenummer = s.bedrijfinternschadenummer
union all
select sb.internschadenummer                                          as "KeyInternSchadenummer"
, h.idpolis                                                           as "KeyIdPolis"
,ltrim(to_char(sb.dekkingsnummer))                                       as "KeyDekkingsNummer"
, sb.schadereserveguid                                                as "KeySchadeBoekingsNummer"
, sb.bedrag                                                           as "TransactionComponentAmount"
, 'O-' + ltrim(to_char(sb.soortreserve)) + '-0'                       as "TransactionComponentTypeCode"
, 'Outstanding Amount - ' + sb.OmschrijvingSoortReserve               as "TransComponentTypeDescription"
from pub.schadereserve sb
inner join pub.schade s on s.bedrijfinternschadenummer = sb.bedrijfinternschadenummer
inner join pub.histpolisversie h on h.bedrijfinternschadenummer = s.bedrijfinternschadenummer
left outer join (
select a.bedrijfinternschadenummer, max(a.boekdatum) as boekdatum
from pub.schadeboeking a
  group by a.bedrijfinternschadenummer) b
  on b.bedrijfinternschadenummer = s.bedrijfinternschadenummer
left outer join (
select x.bedrijfinternschadenummer, max(x.boekdatum) as boekdatum
from pub.schadereserve x
  group by x.bedrijfinternschadenummer) m
  on m.bedrijfinternschadenummer = s.bedrijfinternschadenummer

