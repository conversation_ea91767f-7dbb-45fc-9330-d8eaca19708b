SELECT DISTINCT
    A.CLAIREFE AS 'KeyInternSchadenummer',
    B.POLICODE AS 'KeyIdPolis',
    C.SECTREFE AS 'KeyDekkingsNummer',
    C.SECTPROD AS 'SectionProductCode',
    C.SECTREFE AS 'SectionReference'
FROM
    NTJDWHMRK..MEDFSINI A,
    NTJDWHMRK..MEDFSIPO B,
    NTJDWHMRK..MEDFSISE C
WHERE
    A.ID_MEDFSINI = B.ID_MEDFSINI_FK AND
    B.ID_MEDFSIPO = C.ID_MEDFSIPO_FK AND
    A.CLAMODDA >= CONVERT(datetime,'01/01/2000',103)
