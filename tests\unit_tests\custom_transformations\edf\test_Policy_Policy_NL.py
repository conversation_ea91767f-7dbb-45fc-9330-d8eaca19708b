from datetime import date

from mines2.core.extensions.misc import assert_dataframe_equality
from pyspark.sql import Row

import models_scripts.transformations.edf.Policy_Policy_NL as transform


def test_fix_renewal_values_nl(spark):
    main_df = spark.createDataFrame(
        [
            Row(PolicyID=0, RenewalPolicyIndicator="New  "),
            Row(PolicyID=1, RenewalPolicyIndicator="New"),
            Row(PolicyID=2, RenewalPolicyIndicator="Renew"),
            Row(PolicyID=3, RenewalPolicyIndicator=None),
        ]
    )
    expected_df = spark.createDataFrame(
        [
            Row(PolicyID=0, RenewalPolicyIndicator="new"),
            Row(PolicyID=1, RenewalPolicyIndicator="new"),
            Row(PolicyID=2, RenewalPolicyIndicator="renew"),
            Row(PolicyID=3, RenewalPolicyIndicator="renew"),
        ]
    )
    output_df = transform.fix_renewal_values_nl(main_df)
    assert_dataframe_equality(output_df, expected_df)


def test_policysequencenumber_nl(spark):
    main_df = spark.createDataFrame(
        [
            Row(
                PolicyReference=1,
                KeyIdPolis="10",
                PolicyLastModifiedDate=date(2018, 1, 1),
            ),
            Row(
                PolicyReference=1,
                KeyIdPolis="20",
                PolicyLastModifiedDate=date(2018, 1, 1),
            ),
            Row(
                PolicyReference=1,
                KeyIdPolis="40",
                PolicyLastModifiedDate=date(2020, 1, 1),
            ),
            Row(
                PolicyReference=1,
                KeyIdPolis="30",
                PolicyLastModifiedDate=date(2021, 1, 1),
            ),
            Row(
                PolicyReference=2,
                KeyIdPolis="9",
                PolicyLastModifiedDate=date(2020, 1, 1),
            ),
            Row(
                PolicyReference=2,
                KeyIdPolis="80",
                PolicyLastModifiedDate=date(2020, 1, 1),
            ),
            Row(
                PolicyReference=2,
                KeyIdPolis="85",
                PolicyLastModifiedDate=date(2020, 1, 1),
            ),
            Row(
                PolicyReference=2,
                KeyIdPolis="100",
                PolicyLastModifiedDate=date(2020, 1, 1),
            ),
        ]
    )
    expected_df = spark.createDataFrame(
        [
            Row(
                PolicyReference=1,
                KeyIdPolis="10",
                PolicyLastModifiedDate=date(2018, 1, 1),
                PolicySequenceNumber=1,
            ),
            Row(
                PolicyReference=1,
                KeyIdPolis="20",
                PolicyLastModifiedDate=date(2018, 1, 1),
                PolicySequenceNumber=2,
            ),
            Row(
                PolicyReference=1,
                KeyIdPolis="40",
                PolicyLastModifiedDate=date(2020, 1, 1),
                PolicySequenceNumber=3,
            ),
            Row(
                PolicyReference=1,
                KeyIdPolis="30",
                PolicyLastModifiedDate=date(2021, 1, 1),
                PolicySequenceNumber=4,
            ),
            Row(
                PolicyReference=2,
                KeyIdPolis="9",
                PolicyLastModifiedDate=date(2020, 1, 1),
                PolicySequenceNumber=1,
            ),
            Row(
                PolicyReference=2,
                KeyIdPolis="80",
                PolicyLastModifiedDate=date(2020, 1, 1),
                PolicySequenceNumber=2,
            ),
            Row(
                PolicyReference=2,
                KeyIdPolis="85",
                PolicyLastModifiedDate=date(2020, 1, 1),
                PolicySequenceNumber=3,
            ),
            Row(
                PolicyReference=2,
                KeyIdPolis="100",
                PolicyLastModifiedDate=date(2020, 1, 1),
                PolicySequenceNumber=4,
            ),
        ]
    )
    output_df = transform.add_policy_sequence(main_df)
    assert_dataframe_equality(output_df, expected_df)


def test_add_ipt_liable_col(spark):
    policy_df = spark.createDataFrame(
        [
            Row(PolicyReference=1, BrokerCode="1"),
            Row(PolicyReference=2, BrokerCode="2"),
            Row(PolicyReference=3, BrokerCode="3"),
        ]
    )
    broker_df = spark.createDataFrame(
        [
            Row(BrokerNumber="1", IPTLiable="Yes"),
            Row(BrokerNumber="2", IPTLiable="No"),
            Row(BrokerNumber="3", IPTLiable="Yes"),
        ]
    )
    expected_df = spark.createDataFrame(
        [
            Row(PolicyReference=1, BrokerCode="1", IPTLiable="Yes"),
            Row(PolicyReference=2, BrokerCode="2", IPTLiable="No"),
            Row(PolicyReference=3, BrokerCode="3", IPTLiable="Yes"),
        ]
    )
    output_df = transform.add_ipt_liable_col(df=policy_df, broker_df=broker_df)
    assert_dataframe_equality(output_df, expected_df)
