select distinct pb.idpolis as 'KeyIdPolis'
, pb.internpolisnummer + '/' +
  case when pyoa.ingangsdatum is null then ltrim(to_char(year(ifnull(hyoa.histhoofdpremievervaldatum, hyoa.ingangsdatum)))) else
                                           ltrim(to_char(year(ifnull(pyoa.histhoofdpremievervaldatum, pyoa.ingangsdatum)))) end as 'PolicyCode'
, ifnull(pyoa.polisnummer, hyoa.polisnummer)    as 'PolicyReference'
from pub.premieboekingverdeling pv
inner join pub.premieboeking pb on pb.bedrijffactuurnummer = pv.bedrijffactuurnummer
left outer join pub.polisversie pyoa on pyoa.idpolis = pb.idpolis
left outer join pub.histpolisversie hyoa on hyoa.idpolis = pb.idpolis

union

select distinct pb.idpolis as 'KeyIdPolis'
, pb.internpolisnummer + '/' +
  case when pyoa.ingangsdatum is null then ltrim(to_char(year(ifnull(hyoa.histhoofdpremievervaldatum, hyoa.ingangsdatum)))) else
                                           ltrim(to_char(year(ifnull(pyoa.histhoofdpremievervaldatum, pyoa.ingangsdatum)))) end as 'PolicyCode'
, ifnull(pyoa.polisnummer, hyoa.polisnummer)    as 'PolicyReference'
from pub.premieboekingtpverdeling pv
inner join pub.premieboekingtp pb on pb.idpremieboekingtp = pv.idpremieboekingtp
left outer join pub.polisversie pyoa on pyoa.idpolis = pb.idpolis
left outer join pub.histpolisversie hyoa on hyoa.idpolis = pb.idpolis