from pyspark.sql import DataFrame
from pyspark.sql import functions as F
from pyspark.sql import window as W

from models_scripts.transformations.common.traits import requires, transformation


@transformation
@requires(
    [
        "KeyInternSchadenummer",
        "KeyIdPolis",
        "KeyDekkingsNummer",
        "TransactionType",
        "TransactionSequenceNumber",
    ]
)
def get_openviva_boekings_nummer(input_df: DataFrame) -> DataFrame:
    """Add KeySchadeBoekingsNummer column to input_df. This column is calculated based on the following formula:
    "OV" + TransactionType ("R or "Z" depending if Movement/Rezerve or Payment/Zahlung) + Sequence
    The sequence is a rainking based on the TransactionDate (for R) or on the TransactionAuthorisationDate (for Z)
    The dates should be combined into one column, before calling this function.

    Args:
        input_df (DataFrame): Claim_Transaction DataFrame or Claim_TransactionComponent from openviva.
    """
    ## Part 1: basic code for OpenViva sourced data
    prefix_code = F.lit("OV")

    ## Part 2: Z or R depending on if Zahlung or Rezerve, taken from TransactionType
    transtype_code = F.col("TransactionType")

    ## Part 3: Create sequence based on rank from oldest to newest date and PK
    ### Make sure to always maintain the same order, by ordering by TransactionComponentAmount when there
    order_cond = ["TransactionSequenceNumber"]
    if "TransactionComponentAmount" in input_df.columns:
        order_cond.append("TransactionComponentAmount")

    window_func = W.Window.partitionBy(
        "KeyInternSchadenummer", "KeyIdPolis", "KeyDekkingsNummer", "TransactionType"
    ).orderBy(order_cond)
    sequence_code = F.row_number().over(window_func)

    ## End: Combine into KeySchadeBoekingsNummer and TransactionReference
    output_df = input_df.withColumn(
        "KeySchadeBoekingsNummer",
        F.concat_ws("-", prefix_code, transtype_code, sequence_code),
    )

    return output_df
