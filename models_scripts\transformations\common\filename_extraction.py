import pyspark.sql.functions as F
from pyspark.sql import Column, DataFrame


def extract_year_from_filename(column_name: str) -> Column:
    """
    Extracts the year from a column containing filenames in PySpark DataFrame.

    Args:
        column_name (str): The name of the column to be processed.

    Returns:
        Column: A column with the extracted year.
    """
    return F.regexp_extract(F.col(column_name), r"(\d{4})", 1).cast("int")


def extract_full_month_name_from_filename(column_name: str) -> DataFrame:
    """Create Quarter column, it will be populated with the value of Partition, since it represents the quarter"""
    month_pattern = r"January|February|March|April|May|June|July|August|September|October|November|December"
    return F.regexp_extract(F.col(column_name), month_pattern, 0).cast("string")
