import re
from datetime import datetime
from typing import Union

import numpy as np
import pandas as pd
from babel.numbers import NumberFormatError, parse_decimal
from pyspark.sql import DataFrame

from models_scripts.transformations.common.misc import pandas_to_spark, spark_to_pandas
from models_scripts.transformations.common.traits import business_logic

NATType = type(pd.NaT)
NAType = type(pd.NA)
parquet_folder = "./"


def read_dataframes(df_dict: dict[str, DataFrame]) -> dict[str, pd.DataFrame]:
    """Reads the dataframes from the parquet format"""
    read_parquet = lambda parquet_name: spark_to_pandas(df_dict[parquet_name])

    return {
        "Spain_pol_temp": read_parquet("LegacyRateAdequacyES_main"),
        "Spain_pol_adj": read_parquet("LegacyRateAdequacyES_Spain_pol_adj"),
        "Spain_firstCTE": read_parquet("LegacyRateAdequacyES_Spain_firstCTE"),
        "Spain_lastCTE": read_parquet("LegacyRateAdequacyES_Spain_lastCTE"),
        "Spain_pol_prod_mapping": read_parquet(
            "LegacyRateAdequacyES_Spain_pol_prod_mapping"
        ),
        "Spain_lim_adj": read_parquet("LegacyRateAdequacyES_Spain_lim_adj"),
        "Spain_lim_adj_sgn": read_parquet("LegacyRateAdequacyES_Spain_lim_adj_sgn"),
        "Spain_migrating_pol": read_parquet("LegacyRateAdequacyES_Spain_migrating_pol"),
        "Spain_turnover_pol": read_parquet("LegacyRateAdequacyES_Spain_turnover"),
        "Spain_check_polimpo": read_parquet("LegacyRateAdequacyES_Spain_check_polimpo"),
        "Spain_pol_map": read_parquet("LegacyRateAdequacyES_Spain_pol_map"),
        "Spain_quote_mapping": read_parquet("LegacyRateAdequacyES_Spain_quote_mapping"),
        "Spain_product_mapping": read_parquet(
            "LegacyRateAdequacyES_Spain_product_mapping"
        ),
        "Spain_type_mapping": read_parquet("LegacyRateAdequacyES_Spain_type_mapping"),
        "Spain_broker_mapping": read_parquet(
            "LegacyRateAdequacyES_Spain_broker_mapping"
        ),
    }


# noinspection PyTypeChecker
def parse_number(number: str, locale, errors="raise") -> float:
    """Parse number of given locale into float.
    Examples:
    >>> parse_number('1', 'en_GB')
    1.0
    >>> parse_number('1.0', 'en_GB')
    1.0
    >>> parse_number('1,0', 'en_GB') # doctest: +IGNORE_EXCEPTION_DETAIL
    Traceback (most recent call last):
    NumberFormatError: Invalid number format
    >>> parse_number('1,0', 'en_GB', errors='ignore')
    0.0
    >>> parse_number('1,1004', 'de_DE')
    1.1004
    >>> parse_number(np.nan, 'de_DE')
    nan
    """
    if locale == "en_GB":
        expected_format_regex = r"^-?\d{1,3}(,\d{3})*(\.\d+)?$"
        thousand_sep = ","
    elif locale == "de_DE":
        expected_format_regex = r"^-?\d{1,3}(\.\d{3})*,\d+$"
        thousand_sep = "."
    else:
        raise ValueError(f"Locale {locale} not supported")

    try:
        if number is not None and (pd.isna(number) == False):
            if thousand_sep in number and not re.match(expected_format_regex, number):
                raise NumberFormatError("Invalid number format")
            parsed_number = float(parse_decimal(number, locale=locale))
        else:
            parsed_number = np.nan
    except NumberFormatError:
        if errors == "raise":
            raise
        else:
            parsed_number = 0.0
    return parsed_number


def parse_number_unkown_locale(number: str) -> Union[float, None]:
    # noinspection PyTypeChecker
    """Parse number of Unknown format into german or uk format.
    Examples:
    >>> parse_number_unkown_locale('0.0')
    0.0
    >>> parse_number_unkown_locale('1')
    1.0
    >>> parse_number_unkown_locale('1.0')
    1.0
    >>> parse_number_unkown_locale('1,0')
    1.0
    >>> parse_number_unkown_locale('1.1004') #since its clear . isn't thousand separator. Go to UK
    1.1004
    >>> parse_number_unkown_locale('1,1004')
    1.1004
    >>> parse_number_unkown_locale('100.109') #if has . the priority go to uk, if both are valid
    100.109
    >>> parse_number_unkown_locale('100,109')
    100.109
    >>> parse_number_unkown_locale('100.109,10')
    100109.1
    >>> parse_number_unkown_locale('100.109,10')
    100109.1
    >>> parse_number_unkown_locale('100.109,100.10') #should be none since no valid parse on both locale
    nan
    >>> parse_number_unkown_locale(np.nan)
    nan
    >>> parse_number_unkown_locale(None)
    nan
    """

    # get possible formats
    try:
        parsed_uk_format = parse_number(number, locale="en_GB", errors="raise")
    except NumberFormatError:
        parsed_uk_format = None
    try:
        parsed_german_format = parse_number(number, locale="de_DE", errors="raise")
    except NumberFormatError:
        parsed_german_format = None

    if pd.isna(parsed_uk_format) and pd.isna(parsed_german_format):
        parsed_number = np.nan
    elif (
        parsed_uk_format is not None and parsed_german_format is not None
    ):  # both formats are valid
        if parsed_uk_format == parsed_german_format:
            parsed_number = parsed_uk_format  # both formats are the same value
        elif (
            "." in number
        ):  # First untie rule: if the number has a dot, it is in UK format
            parsed_number = parsed_uk_format
        else:  # default untie rule: Give priority to German format
            parsed_number = parsed_german_format
    elif parsed_uk_format is not None:  # only UK format is valid
        parsed_number = parsed_uk_format
    elif parsed_german_format is not None:  #    only German format is valid
        parsed_number = parsed_german_format
    else:
        parsed_number = np.nan  #    no valid format

    return parsed_number  # type: ignore


def deduplicate_records(df: pd.DataFrame) -> pd.DataFrame:
    # Calculate how many NAs by line
    df["checknas"] = df.apply(lambda x: sum(pd.isna(x)), axis=1)

    # Select the lower NAS by PolycyID (ID_DPOLIZAS)/Policy subversion(ID_DPOLSCON)
    df = df.loc[df.groupby(["ID_DPOLIZAS", "ID_DPOLSCON"])["checknas"].idxmin()].drop(
        "checknas", axis=1
    )
    return df


def apply_business_rules(df: pd.DataFrame) -> pd.DataFrame:
    df["FACTOCOM"] = df["FACTOCOM"].fillna(0)
    df["FACTOTEC"] = df["FACTOTEC"].fillna(0)
    df["SGREDSTE"] = df["SGREDSTE"].fillna("none")
    df["SGNRECDS"] = df["SGNRECDS"].fillna("none")
    df["adj_tec"] = np.where(df["SGREDSTE"] == "-", -df["FACTOTEC"], df["FACTOTEC"])
    df["adj_com"] = np.where(df["SGNRECDS"] == "-", -df["FACTOCOM"], df["FACTOCOM"])
    df = df.rename(columns={"POL_VERSION": "PolicyID"})

    return df


def parse_numeric_cols(df: pd.DataFrame, columns: list, parse_function) -> pd.DataFrame:
    for col in columns:
        df[col] = df[col].apply(parse_function)
        df[col] = pd.to_numeric(df[col], errors="raise")
    return df


def clean_names(colname: str) -> str:
    stringtoclean = r"~!@#$%^&*(){}_+:<>?,./;'[]-="
    pattern = (
        r"\s+|/|&|:|,|\."
        + "|".join(re.escape(c) for c in stringtoclean)
        + r'|"|\'|`|\(|\)|\?|\%|\u2019|\u20AC|(.)([A-Z][a-z]+)|1_st|2_nd'
    )
    colname = re.sub(pattern, lambda m: "_" if m.group(0).isspace() else "", colname)
    colname = colname.lower()
    colname = re.sub(r"__+", "_", colname)
    return colname


def clean_dafaframes_colnames(dfs: dict[str, pd.DataFrame]) -> dict[str, pd.DataFrame]:
    """Clean dataframes colnames.

    Args:
        dfs (dict[str, pd.DataFrame]): Dataframes.

    Returns:
        dict[str, pd.DataFrame]: Dataframes with cleaned colnames.
    """
    for key, df in dfs.items():
        df.columns = [clean_names(colname) for colname in df.columns]
        dfs[key] = df
    return dfs


def get_fiscal_year(date_: datetime) -> int:
    """Get fiscal year from date."""
    if date_.month <= 3:
        return date_.year - 1
    else:
        return date_.year


def get_policy_base_info(dfs: dict[str, pd.DataFrame]) -> pd.DataFrame:
    """Get"""
    spain_pol_df = dfs["Spain_pol_temp"]
    spain_pol_df["YOA"] = spain_pol_df["InceptionDate"].dt.year
    spain_pol_df = spain_pol_df.sort_values(
        by=["CertificateNumber", "YOA", "VersionNumber"]
    )
    spain_pol_df[["prev", "prevIPT", "prevCommission", "prevCLEA", "prevConsorcio"]] = (
        spain_pol_df.groupby(["CertificateNumber", "YOA"])[
            ["AnnualPrem", "IPT", "Commission", "Clea", "Consorcio"]
        ]
        .shift(1)
        .fillna(0)
    )
    spain_pol_df["daystocount"] = np.where(
        (spain_pol_df["LeapYear"].fillna(0) != 1),
        365,
        366,
    )
    spain_pol_df["daydiff"] = (
        pd.to_datetime(spain_pol_df["ExpiryDate"])
        - pd.to_datetime(spain_pol_df["InceptionDate"])
    ).dt.days

    spain_pol_df["AnnualPremDelta"] = np.where(
        (spain_pol_df["AnnualPrem"] < 0) | (spain_pol_df["prev"] < 0),
        spain_pol_df["AnnualPrem"],
        spain_pol_df["AnnualPrem"] - spain_pol_df["prev"],
    )

    tempinde = spain_pol_df["CICLOREN"].fillna("NullSafe") == "TEMPINDE"
    AnnualPrem_tempinde = spain_pol_df.loc[tempinde, "AnnualPrem"]
    AnnualPrem_not_tempinde = spain_pol_df.loc[~tempinde, "AnnualPrem"]
    prev_tempinde = spain_pol_df.loc[tempinde, "prev"]
    prev_not_tempinde = spain_pol_df.loc[~tempinde, "prev"]
    EffectiveDays_tempinde = spain_pol_df.loc[tempinde, "EffectiveDays"]
    EffectiveDays_not_tempinde = spain_pol_df.loc[~tempinde, "EffectiveDays"]
    daydiff_tempinde = spain_pol_df.loc[tempinde, "daydiff"]
    daydiff_not_tempinde = spain_pol_df.loc[~tempinde, "daydiff"]
    IPT_tempinde = spain_pol_df.loc[tempinde, "IPT"]
    IPT_not_tempinde = spain_pol_df.loc[~tempinde, "IPT"]
    prevIPT_tempinde = spain_pol_df.loc[tempinde, "prevIPT"]
    prevIPT_not_tempinde = spain_pol_df.loc[~tempinde, "prevIPT"]
    Commission_tempinde = spain_pol_df.loc[tempinde, "Commission"]
    Commission_not_tempinde = spain_pol_df.loc[~tempinde, "Commission"]
    prevCommission_tempinde = spain_pol_df.loc[tempinde, "prevCommission"]
    prevCommission_not_tempinde = spain_pol_df.loc[~tempinde, "prevCommission"]
    Clea_tempinde = spain_pol_df.loc[tempinde, "Clea"]
    Clea_not_tempinde = spain_pol_df.loc[~tempinde, "Clea"]
    prevCLEA_tempinde = spain_pol_df.loc[tempinde, "prevCLEA"]
    prevCLEA_not_tempinde = spain_pol_df.loc[~tempinde, "prevCLEA"]
    Consorcio_tempinde = spain_pol_df.loc[tempinde, "Consorcio"]
    Consorcio_not_tempinde = spain_pol_df.loc[~tempinde, "Consorcio"]
    prevConsorcio_tempinde = spain_pol_df.loc[tempinde, "prevConsorcio"]
    prevConsorcio_not_tempinde = spain_pol_df.loc[~tempinde, "prevConsorcio"]
    daystocount_tempinde = spain_pol_df.loc[tempinde, "daystocount"]
    daystocount_not_tempinde = spain_pol_df.loc[~tempinde, "daystocount"]

    spain_pol_df.loc[tempinde, "GWPDelta"] = np.where(
        (AnnualPrem_tempinde < 0) | (prev_tempinde < 0),
        (AnnualPrem_tempinde * EffectiveDays_tempinde) / daydiff_tempinde,
        ((AnnualPrem_tempinde - prev_tempinde) * EffectiveDays_tempinde)
        / daydiff_tempinde,
    )

    spain_pol_df.loc[tempinde, "IPTDelta"] = np.where(
        (IPT_tempinde < 0) | (prevIPT_tempinde < 0),
        (IPT_tempinde * EffectiveDays_tempinde) / daydiff_tempinde,
        ((IPT_tempinde - prevIPT_tempinde) * EffectiveDays_tempinde) / daydiff_tempinde,
    )

    spain_pol_df.loc[tempinde, "CommissionDelta"] = np.where(
        (Commission_tempinde < 0) | (prevCommission_tempinde < 0),
        (Commission_tempinde * EffectiveDays_tempinde) / daydiff_tempinde,
        ((Commission_tempinde - prevCommission_tempinde) * EffectiveDays_tempinde)
        / daydiff_tempinde,
    )

    spain_pol_df.loc[tempinde, "CLEADelta"] = np.where(
        (Clea_tempinde < 0) | (prevCLEA_tempinde < 0),
        (Clea_tempinde * EffectiveDays_tempinde) / daydiff_tempinde,
        ((Clea_tempinde - prevCLEA_tempinde) * EffectiveDays_tempinde)
        / daydiff_tempinde,
    )

    spain_pol_df.loc[tempinde, "ConsorcioDelta"] = np.where(
        (Consorcio_tempinde < 0) | (prevConsorcio_tempinde < 0),
        (Consorcio_tempinde * EffectiveDays_tempinde) / daydiff_tempinde,
        ((Consorcio_tempinde - prevConsorcio_tempinde) * EffectiveDays_tempinde)
        / daydiff_tempinde,
    )

    spain_pol_df.loc[~tempinde, "GWPDelta"] = np.where(
        (AnnualPrem_not_tempinde < 0) | (prev_not_tempinde < 0),
        (AnnualPrem_not_tempinde * EffectiveDays_not_tempinde)
        / daystocount_not_tempinde,
        ((AnnualPrem_not_tempinde - prev_not_tempinde) * EffectiveDays_not_tempinde)
        / daystocount_not_tempinde,
    )

    spain_pol_df.loc[~tempinde, "IPTDelta"] = np.where(
        (IPT_not_tempinde < 0) | (prevIPT_not_tempinde < 0),
        (IPT_not_tempinde * EffectiveDays_not_tempinde) / daystocount_not_tempinde,
        ((IPT_not_tempinde - prevIPT_not_tempinde) * EffectiveDays_not_tempinde)
        / daystocount_not_tempinde,
    )

    spain_pol_df.loc[~tempinde, "CommissionDelta"] = np.where(
        (Commission_not_tempinde < 0) | (prevCommission_not_tempinde < 0),
        (Commission_not_tempinde * EffectiveDays_not_tempinde)
        / daystocount_not_tempinde,
        (
            (Commission_not_tempinde - prevCommission_not_tempinde)
            * EffectiveDays_not_tempinde
        )
        / daystocount_not_tempinde,
    )

    spain_pol_df.loc[~tempinde, "CLEADelta"] = np.where(
        (Clea_not_tempinde < 0) | (prevCLEA_not_tempinde < 0),
        (Clea_not_tempinde * EffectiveDays_not_tempinde) / daystocount_not_tempinde,
        ((Clea_not_tempinde - prevCLEA_not_tempinde) * EffectiveDays_not_tempinde)
        / daystocount_not_tempinde,
    )

    spain_pol_df.loc[~tempinde, "ConsorcioDelta"] = np.where(
        (Consorcio_not_tempinde < 0) | (prevConsorcio_not_tempinde < 0),
        (Consorcio_not_tempinde * EffectiveDays_not_tempinde)
        / daystocount_not_tempinde,
        (
            (Consorcio_not_tempinde - prevConsorcio_not_tempinde)
            * EffectiveDays_not_tempinde
        )
        / daystocount_not_tempinde,
    )

    Spain_pol = pd.concat(
        [spain_pol_df, dfs["Spain_pol_adj"]], ignore_index=True, sort=False
    )

    Spain_pol_agg = (
        Spain_pol.groupby(
            [
                "EDFSource",
                "Policyid",
                "CertificateNumber",
                "pol_version",
                "InceptionDate",
            ]
        )
        .agg(
            GWP=("GWPDelta", "sum"),
            IPT=("IPTDelta", "sum"),
            clea=("CLEADelta", "sum"),
            commission=("CommissionDelta", "sum"),
            Consorcio=("ConsorcioDelta", "sum"),
        )
        .reset_index()
    )

    additional_info_df = pd.merge(
        pd.merge(
            dfs["Spain_firstCTE"][
                [
                    "CertificateNumber",
                    "InceptionDate",
                    "PolicyStatus",
                    "Office",
                    "Brokercode",
                    "Broker",
                    "underwritercode",
                    "Underwriter",
                    "WritingCompany",
                ]
            ],
            dfs["Spain_lastCTE"][
                [
                    "CertificateNumber",
                    "Policyholder",
                    "ProductCode",
                    "ActivityCode",
                    "Reserving_Class",
                    "Section_Reporting_Group",
                    "InceptionDate",
                ]
            ],
            on=["CertificateNumber", "InceptionDate"],
        ),
        Spain_pol_agg[
            [
                "CertificateNumber",
                "pol_version",
                "InceptionDate",
                "GWP",
                "commission",
                "IPT",
                "clea",
                "Consorcio",
            ]
        ],
        left_on=["CertificateNumber", "InceptionDate"],
        right_on=["CertificateNumber", "InceptionDate"],
    )
    additional_info_df["commission_amount"] = additional_info_df["commission"]

    final_spain_df = pd.merge(
        spain_pol_df[
            ["CertificateNumber", "InceptionDate", "Policyid"]
        ].drop_duplicates(),
        additional_info_df,
        left_on=["CertificateNumber", "InceptionDate"],
        right_on=["CertificateNumber", "InceptionDate"],
        how="left",
    )

    #    final_spain_df.columns = [clean_names(col) for col in final_spain_df.columns]
    # convert inception_date to datetime format
    final_spain_df["InceptionDate"] = pd.to_datetime(
        final_spain_df["InceptionDate"], format="%d/%m/%Y"
    )

    # create new columns
    final_spain_df = final_spain_df.assign(
        week=final_spain_df["InceptionDate"].dt.week.apply(  # type: ignore
            lambda x: 52 if x > 52 else x
        ),
        yoa=final_spain_df["InceptionDate"].dt.year,
        mon=final_spain_df["InceptionDate"].dt.month.apply(
            lambda x: 12 if x > 12 else x
        ),
        HT_list=np.where(
            final_spain_df["underwritercode"].fillna("NullSafe") == "CPEN151",
            "High Touch",
            "Low Touch",
        ),
    )

    final_spain_df = final_spain_df[
        ~final_spain_df["CertificateNumber"].isin(
            dfs["Spain_migrating_pol"]["POLIZTRA"]
        )
    ]
    return final_spain_df


def get_latest_adjusted_limit(dfs: dict[str, pd.DataFrame]) -> pd.DataFrame:
    """Get adjusted limit for Spain."""

    spain_lim_adj_df = dfs["Spain_lim_adj"].copy()
    spain_lim_adj_df["VALOR"] = spain_lim_adj_df["VALOR"].apply(
        parse_number_unkown_locale
    )

    # create wide table for limit and deductables
    lim_adj_long_df = spain_lim_adj_df.pivot_table(
        index=["ID_DPOLIZAS", "pol_version", "ID_DPOLSCON"],
        columns="NOMBDATO",
        values="VALOR",
        aggfunc="first",
    ).reset_index()

    # MANUALLY SOLVE A DQ ISSUE FOR 3 POLICY "RC03 2020/2775_2","RC01 2021/229_1","RC03 2021/849_1"
    mapping = {
        "RC03 2020/2775_2": 95.9656,
        "RC01 2021/229_1": 13.5516,
        "RC03 2021/849_1": 11.1864,
    }

    # Apply the mapping to the FACTOTEC column
    lim_adj_long_df.loc[
        lim_adj_long_df["pol_version"].isin(mapping.keys())
        & (~lim_adj_long_df["FACTOTEC"].isna()),
        "FACTOTEC",
    ] = lim_adj_long_df["pol_version"].map(mapping)

    spain_lim_adj_sgn_long_df = (
        dfs["Spain_lim_adj_sgn"]
        .pivot_table(
            index=[
                "ID_DPOLIZAS",
                "ID_DPOLSCON_FK",
                "POLIZTRA",
                "DPODATOS_SCON",
                "ID_DPOLSCON",
                "pol_version",
            ],
            columns="NOMBDATO",
            aggfunc="first",
            values="VALOR",
        )
        .reset_index()
    )
    lim_adj_long_df = pd.merge(
        lim_adj_long_df,
        spain_lim_adj_sgn_long_df,
        on=["ID_DPOLIZAS", "pol_version", "ID_DPOLSCON"],
    )
    lim_adj_long_df["checknas"] = lim_adj_long_df.apply(
        lambda x: x.isna().sum(), axis=1
    )

    latest_lim_adj_df = (
        lim_adj_long_df.groupby(["ID_DPOLIZAS"])
        .apply(lambda x: x.loc[x["checknas"].idxmin()])
        .reset_index(drop=True)
    )
    latest_lim_adj_df = (
        latest_lim_adj_df.groupby(["ID_DPOLIZAS", "pol_version"]).nth(-1).reset_index()
    )

    return latest_lim_adj_df


def merge_policy_with_limit(
    spain_policy_df: pd.DataFrame, latest_lim_adj_df: pd.DataFrame
) -> pd.DataFrame:
    """Merge policy with limit."""

    prem_pol_lim_df = pd.merge(
        spain_policy_df,
        latest_lim_adj_df,
        how="left",
        left_on=["Policyid", "pol_version"],
        right_on=["ID_DPOLIZAS", "pol_version"],
    )
    return prem_pol_lim_df


def get_technical_premium(
    dfs: dict[str, pd.DataFrame], prem_pol_lim_df: pd.DataFrame
) -> pd.DataFrame:
    """Get technical premium for Spain."""

    df = pd.merge(
        prem_pol_lim_df,
        dfs["Spain_check_polimpo"],
        how="inner",
        left_on="Policyid",
        right_on="ID_DPOLIZAS",
    )

    df = df[df["Brokercode"] != "900"]
    df = df[df["GWP"] != 0]

    df["FACTOCOM"].fillna(0, inplace=True)
    df["FACTOTEC"].fillna(0, inplace=True)
    df["SGREDSTE"].fillna("none", inplace=True)
    df["SGNRECDS"].fillna("none", inplace=True)

    df["adj_tec"] = df.apply(
        lambda row: -row["FACTOTEC"] if row["SGREDSTE"] == "-" else row["FACTOTEC"],
        axis=1,
    )
    df["adj_com"] = df.apply(
        lambda row: -row["FACTOCOM"] if row["SGNRECDS"] == "-" else row["FACTOCOM"],
        axis=1,
    )

    df["adj_total"] = df["adj_com"] + df["adj_tec"]

    df = df[~np.isinf(df["GWP"].astype(float)) & (df["yoa"] >= 2020)]
    df["brk"] = round((df["commission_amount"] / df["GWP"]).astype(float), 3)

    df["tec_prem"] = df["POLIPNET"] * (1 - df["brk"]) / (1 + df["adj_tec"] / 100)
    df["com_prem"] = df["POLIPNET"] * (1 - df["brk"]) / (1 + df["adj_com"] / 100)
    df["final_tech_prem"] = (
        df["POLIPNET"] * (1 - df["brk"]) / (1 + df["adj_total"] / 100)
    )
    df["final_net_prem"] = df["POLIPNET"] * (1 - df["brk"])

    return df


def clean_outliers_tech_premium(tech_premium_df: pd.DataFrame) -> pd.DataFrame:
    """Clean outliers for technical premium."""

    lq = tech_premium_df["adj_total"].quantile(0.001)
    uq = tech_premium_df["adj_total"].quantile(0.999)
    tech_premium_df = pd.merge(
        tech_premium_df,
        tech_premium_df[["yoa", "adj_total"]]
        .groupby("yoa")
        .quantile([0.001, 0.999])  # type: ignore
        .unstack(level=1),
        on="yoa",
    )
    tech_premium_df["ext_val"] = np.where(
        (tech_premium_df["adj_total"] < lq) | (tech_premium_df["adj_tec"] > uq),
        "Outliers",
        "No Outliers",
    )
    return tech_premium_df


def add_turnover_info(
    dfs: dict[str, pd.DataFrame], tech_premium_df: pd.DataFrame
) -> pd.DataFrame:
    spain_turnover_pol_df = dfs["Spain_turnover_pol"].copy()
    spain_turnover_pol_df["RESPUESTA"] = spain_turnover_pol_df["RESPUESTA"].apply(
        parse_number_unkown_locale
    )
    spain_turnover_pol_df["POLIEFEC"] = pd.to_datetime(
        spain_turnover_pol_df["POLIEFEC"], format="%d/%m/%Y"
    )

    spain_tun_long_df = pd.pivot_table(
        spain_turnover_pol_df[
            ~spain_turnover_pol_df[
                ["POLIZTRA", "POLIEFEC", "ID_DPOLIZAS", "CODIPREG"]
            ].duplicated()
        ],
        values="RESPUESTA",
        index=["ID_DPOLIZAS", "POLIZTRA", "POLIEFEC"],
        columns="CODIPREG",
    )

    tech_premium_df = pd.merge(
        tech_premium_df,
        spain_tun_long_df,
        left_on=["CertificateNumber", "InceptionDate"],
        right_on=["POLIZTRA", "POLIEFEC"],
        how="left",
    )

    return tech_premium_df


def fill_na_columns(tech_premium_df: pd.DataFrame) -> pd.DataFrame:
    """Fill na columns."""
    na_col = ["GWP", "POLIPNET", "final_tech_prem", "final_net_prem"]
    tech_premium_df[na_col] = tech_premium_df[na_col].apply(lambda x: x.fillna(0))
    return tech_premium_df


def mark_unmapped(df: pd.DataFrame) -> pd.DataFrame:
    """Mark unmapped with a special label."""
    df.loc[df["Reserving_Class"].isna(), "Reserving_Class"] = "Unmapped"
    df.loc[df["Reserving_Class"] == "Unmapped", "section_reporting_group"] = "Unmapped"
    df["section_reporting_group"] = df["section_reporting_group"].str.replace(
        "[0-9]", ""
    )
    df["section_reporting_group"] = df["section_reporting_group"].str.replace(
        ". ", "", regex=False
    )

    ##  6.5 Unmapped Underwriter ####
    df.loc[df["Underwriter"].isna(), "Underwriter"] = "Unmapped"
    df["Policyholder"] = df["Policyholder"].str.title()

    df = (
        df.sort_values(by=["pol_version", "Policyid"], ascending=[True, False])
        .drop_duplicates(subset=["pol_version"])
        .reset_index(drop=True)
    )
    return df


def select_final_columns(df: pd.DataFrame) -> pd.DataFrame:
    """Select final columns."""
    column_map = {
        "PolicyID": "ES:" + df["pol_version"],
        "AdjTec": df["adj_tec"],
        "AdjCom": df["adj_com"],
        "TecPrem": df["tec_prem"],
        "ComPrem": df["com_prem"],
        "FinalTechPrem": df["final_tech_prem"],
        "FinalNetPrem": df["final_net_prem"],
        "AdjTotal": df["adj_total"],
        "TechnicalRatio": df["final_net_prem"] / df["final_tech_prem"],
    }
    df = df.assign(**column_map)[list(column_map.keys())]

    return df


def process_spain_rate_adequacy(dfs: dict[str, pd.DataFrame]) -> pd.DataFrame:
    """Process Spain rate adequacy data.

    Args:
        dfs (dict[str, pd.DataFrame]): Dataframes.

    Returns
        pd.DataFrame: Processed data.
    """
    spain_policy_df = get_policy_base_info(dfs)
    latest_lim_adj_df = get_latest_adjusted_limit(dfs)
    prem_pol_lim_df = merge_policy_with_limit(spain_policy_df, latest_lim_adj_df)
    technical_premium_df = get_technical_premium(dfs, prem_pol_lim_df)
    tech_premium_clean_df = clean_outliers_tech_premium(technical_premium_df)
    tech_premium_clean_df = add_turnover_info(dfs, tech_premium_clean_df)
    tech_premium_clean_df = fill_na_columns(tech_premium_clean_df)
    tech_premium_clean_df = mark_unmapped(tech_premium_clean_df)
    rate_adequacy_df = select_final_columns(tech_premium_clean_df)

    return rate_adequacy_df


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    pdfs = read_dataframes(df_dict)
    pdf_processed = process_spain_rate_adequacy(pdfs)

    output_df = pandas_to_spark(pdf_processed)

    return output_df
