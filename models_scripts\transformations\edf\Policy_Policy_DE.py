import pyspark.sql.functions as F
from pyspark.sql import DataFrame, Window

from models_scripts.transformations.common.misc import (
    enforce_nulls_type,
    pandas_to_spark,
    spark_to_pandas,
)
from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.add_columns import (
    add_business_classification,
    add_german_country,
)
from models_scripts.transformations.edf.common.currency import (
    PolicyPolicyCurrencyConversor,
)
from models_scripts.transformations.edf.common.fix_renewal_values import (
    fix_renewal_values_de,
)
from models_scripts.transformations.edf.common.re_underwriting import (
    add_re_underwriting_col,
)


def add_policy_sequence(df: DataFrame) -> DataFrame:
    window = Window.partitionBy("PolicyReference").orderBy("KeyIdPolis")
    df = df.withColumn("PolicySequenceNumber", F.row_number().over(window))
    return df


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_pdf = spark_to_pandas(df_dict["Policy_main"])
    re_underwriting_pdf = spark_to_pandas(df_dict["Reference_ReUnderwriting"])
    exchange_rate_df = df_dict["Policy_exchange_rate"]

    main_pdf = add_re_underwriting_col(main_pdf, re_underwriting_pdf)
    main_df = pandas_to_spark(main_pdf)

    main_df = fix_renewal_values_de(main_df)
    main_df = add_business_classification(main_df)
    main_df = add_german_country(main_df)
    main_df = add_policy_sequence(main_df)
    main_df = enforce_nulls_type(main_df)
    main_df = PolicyPolicyCurrencyConversor.add_currency_columns(
        main_df, exchange_rate_df
    )

    return main_df
