SELECT DISTINCT
    A<PERSON>LAIREFE AS 'KeyInternSchadenummer',
    B<PERSON>POLICODE AS 'KeyIdPolis',
    C.SECTREFE AS 'KeyDekkingsNummer',
    D.ID_MEDFSITR AS 'KeySchadeBoekingsNummer',
    <PERSON><PERSON> AS 'Payee',
    D<PERSON>EX<PERSON> AS 'RateOfExchange',
    '' AS 'TransactionAuthorisationDate',
    <PERSON><PERSON> AS 'TransactionCurrencyCode',
    D.TRANDATE AS 'TransactionDate',
    D.TRANNARR AS 'TransactionNarrative',
    <PERSON><PERSON>TRANREF<PERSON> AS 'TransactionReference',
    D<PERSON>TRASEQNU AS 'TransactionSequenceNumber',
    <PERSON><PERSON>RA<PERSON> AS 'TransactionTypeCode',
    <PERSON><PERSON>T<PERSON> AS 'TransactionTypeDescription'
FROM
    NTJDWHMRK..MEDFSINI A,
    NTJDWHMRK..MEDFSIPO B,
    NTJDWHMRK..MEDFSISE C,
    NTJDWHMRK..MEDFSITR D
WHERE
    A.ID_MEDFSINI = B.ID_MEDFSINI_FK
    AND B.ID_MEDFSIPO = C.ID_MEDFSIPO_FK
    AND C.ID_MEDFSISE = D.ID_MEDFSISE_FK
    AND D.ID_MEDFSITR IN (
        SELECT MAX(Z.ID_MEDFSITR)
        FROM NTJDWHMRK..MEDFSITR Z
        WHERE
            Z.TRATYPCO = D.TRATYPCO
            AND Z.TRANREFE = D.TRANREFE
    )
    AND A.CLAMODDA >= CONVERT(datetime,'01/01/2000',103)
