Country: custom
existCustomTransformation: 'True'

dataSource:
- name: main
  type: SourceSisnetES
  parameters:
    sqlFileName: 'Policy ES Manual Business.sql'
    querySourceType: SQL_FILE
    selectColumnsFromSchema: False
    enforceSourceSchemaOnStandard: 'False'

- name: exchange_rate
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: test_mintdatalake_02
      uat: test_mintdatalake_02
      prod: prod_mintdatalake_02
    schema: rdm_dbo
    table: mint_mapping_roe
    sourceSystem: MINT_RAW_DATALAKE

ColumnSpecs:
  ClaimsPolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  BrokerCodeID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: BrokerCode
        sep: ':'
  ExpiryDate:
    dateTimeFormat: ISO
  InceptionDate:
    dateTimeFormat: ISO
  PeerReview1Date:
    dateTimeFormat: ISO
  PeerReview2Date:
    dateTimeFormat: ISO
  PolicyLastModifiedDate:
    dateTimeFormat: ISO
  UnderwriterCodeID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: UnderwriterCode
        sep: ':'
  WrittenDate:
    dateTimeFormat: ISO
  ReUnderwritingExercise:
    NotInSource: True
  BusinessClassification:
    NotInSource: True
  PolicySequenceNumber:
    NotInSource: True
  IPTLiable:
    NotInSource: True
