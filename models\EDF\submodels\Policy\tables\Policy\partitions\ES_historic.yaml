Country: ES
existCustomTransformation: 'True'

dataSource:
- name: main
  type: DatabricksCatalogSQLQuery
  parameters:
    sourceSystem: SCS
    catalogName:
      mintdatalake:
        dev: test_mintdatalake_02
        uat: test_mintdatalake_02
        prod: prod_mintdatalake_02
    sqlFileName: policy.sql

- name: mapping_table
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Support
    Table: SisnetSCSMappingTableES
    Partitions:
      - ES

- name: activity_code
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Reference
    Table: ActivityCodeES
    Partitions:
      - ES

- name: scs_policy_section
  type: EuropeanDatalake
  parameters:
    Layer: standard
    subModel: Policy
    Table: Section_main

- name: sisnet_policy_policy
  type: EuropeanDatalake
  parameters:
    Layer: standard
    subModel: Policy
    Table: Policy_main
    Partitions:
      - ES

- name: sisnet_policy_section
  type: EuropeanDatalake
  parameters:
    Layer: standard
    subModel: Policy
    Table: Section_main
    Partitions:
      - ES

- name: re_underwriting
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Reference
    Table: ReUnderwriting

- name: new_renewal
  type: SourceSisnetES
  parameters:
    sqlFileName: Policy_NewRenewalSISNET_query.sql
    querySourceType: SQL_FILE
    enforceSourceSchemaOnStandard: 'False'
    selectColumnsFromSchema: False

- name: exchange_rate
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: test_mintdatalake_02
      uat: test_mintdatalake_02
      prod: prod_mintdatalake_02
    schema: rdm_dbo
    table: mint_mapping_roe
    sourceSystem: MINT_RAW_DATALAKE

ColumnSpecs:
  IPTLiable:
    NotInSource: True
  ClaimsPolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  BrokerCodeID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: BrokerCode
        sep: ':'
  ExpiryDate:
    dateTimeFormat: dd-MMM-yy
  InceptionDate:
    dateTimeFormat: dd-MMM-yy
  PeerReview1Date:
    dateTimeFormat: dd-MMM-yy
  PeerReview2Date:
    dateTimeFormat: dd-MMM-yy
  PolicyLastModifiedDate:
    dateTimeFormat: dd-MMM-yy
  UnderwriterCodeID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: UnderwriterCode
        sep: ':'
  WrittenDate:
    dateTimeFormat: dd-MMM-yy
  AssuredAnnualTurnover:
    locale: en_US.utf8
  OrderPercentage:
    locale: en_US.utf8
  ReUnderwritingExercise:
    NotInSource: True
  BusinessClassification:
    NotInSource: True
  PolicySequenceNumber:
    NotInSource: True
