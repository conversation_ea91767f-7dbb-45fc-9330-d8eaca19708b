/* This query contains the logic to New|Renewal split based on SISNET policies */
SELECT DISTINCT
    tmp.[KeyIdPolis], tmp.[BusinessType] as 'RenewalPolicyIndicator'
FROM (
    SELECT
        A.POLIZTRA AS 'CertificateNumber'
        ,CASE WHEN A.POLIZTRA IN (
            SELECT CERTIFICATE_NUMBER FROM (
                SELECT * FROM (
                    VALUES  /* manual adjustments */
                         ('022S00530ACO' ,'223392')
                        ,('022S00709ACO' ,'022S00530ACO')
                        ,('022S00815ACO' ,'022S00709ACO')
                        ,('022S00531ACO' ,'223396')
                        ,('022S00710ACO' ,'022S00531ACO')
                        ,('022S00816ACO' ,'022S00710ACO')
                        ,('022S00526ACO' ,'282720')
                        ,('022S00814ACO' ,'022S00526ACO')
                        ,('022S00450ACO' ,'021EC0001ACC')
                        ,('022S00327ACO' ,'274723')
                        ,('023S00577RCD' ,'20S00750RCD')
                ) AS T(CERTIFICATE_NUMBER, POL_ORIGEN_ADJUST)
            ) ORIG_ADJUS)
              THEN ORIG_ADJUS.POL_ORIGEN_ADJUST ELSE ORIG.VALOR END AS 'Policy_Origen'
        ,CONCAT(A.PRODUCTO,' ',A.POLIZANN,'/',A.POLIZSEC,'_',(C.NCICLORE+1)) AS 'KeyIdPolis'
        ,CASE WHEN (ORIG_ADJUS.CERTIFICATE_NUMBER IS NULL AND C.NCICLORE = 0 AND (ORIG.VALOR = '' OR ORIG.VALOR IS NULL))
                OR (ORIG_ADJUS.CERTIFICATE_NUMBER IS NOT NULL AND C.NCICLORE = 0 AND (ORIG_ADJUS.POL_ORIGEN_ADJUST = ''))
              THEN 'New' ELSE 'Renewal' END AS 'BusinessType'
    FROM NTJDATMRK..DPOLIZAS A
    LEFT JOIN NTJDATMRK..DPOLSCON B ON A.ID_DPOLIZAS = B.ID_DPOLIZAS_FK
    LEFT JOIN NTJDATMRK..DPOLPER C ON B.DPOLPER_SCON = C.ID_DPOLSCON_FK
    LEFT JOIN (
        SELECT * FROM (
            VALUES  /* manual adjustments */
            ('022S00530ACO' ,'223392')
            ,('022S00709ACO' ,'022S00530ACO')
            ,('022S00815ACO' ,'022S00709ACO')
            ,('022S00531ACO' ,'223396')
            ,('022S00710ACO' ,'022S00531ACO')
            ,('022S00816ACO' ,'022S00710ACO')
            ,('022S00526ACO' ,'282720')
            ,('022S00814ACO' ,'022S00526ACO')
            ,('022S00450ACO' ,'021EC0001ACC')
            ,('022S00327ACO' ,'274723')
            ,('023S00577RCD' ,'20S00750RCD')
        ) AS T(CERTIFICATE_NUMBER, POL_ORIGEN_ADJUST)
    ) ORIG_ADJUS ON A.POLIZTRA = ORIG_ADJUS.CERTIFICATE_NUMBER
    LEFT JOIN (
        SELECT
            A.POLIZTRA
            ,CASE WHEN ORIG.VALOR IS NULL THEN ''
            ELSE ORIG.VALOR END VALOR
        FROM NTJDATMRK..DPOLIZAS A
        LEFT JOIN NTJDATMRK..DPOLSCON B ON A.ID_DPOLIZAS = B.ID_DPOLIZAS_FK AND B.NUMSITUA = 1
        LEFT JOIN (
                SELECT * FROM NTJDATMRK..DPODATOS
                WHERE NOMBDATO = 'POLIORIG'
            ) ORIG ON B.DPODATOS_SCON = ORIG.ID_DPOLSCON_FK
    ) ORIG ON A.POLIZTRA = ORIG.POLIZTRA
) as tmp
