repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
    -   id: trailing-whitespace
    -   id: check-yaml
  - repo: https://github.com/ambv/black
    rev: 23.1.0
    hooks:
      - id: black
  - repo: https://github.com/compilerla/conventional-pre-commit
    rev: v2.1.1
    hooks:
      - id: conventional-pre-commit
        stages: [commit-msg]
        args: [major, feat, fix, ci, test, refactor, style, docs, perf, build, partition, table, model]
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        name: isort (python)
        args: ["--profile", "black"]

