from pandas import DataFrame


# TODO: Convert to Spark and move this function to edf/common/add_columns.py script
def add_re_underwriting_col(df_policy: DataFrame, df_re_underwriting: DataFrame):
    """Add boolean column ReUnderwritingExercise, will be True if has a match
    on df_re_underwriting on KeyIdPolis and PolicyReference"""
    df_policy["KeyIdPolis"] = df_policy["KeyIdPolis"].astype("string")
    df_re_underwriting["PolicyReference"] = df_re_underwriting[
        "PolicyReference"
    ].astype("string")

    # Drop duplicated rows, keeping the first one, to make sure that the merge is
    # one-to-one (to avoid duplicates in the final dataframe):
    df_policy.drop_duplicates(subset="KeyIdPolis", keep="first", inplace=True)
    df_re_underwriting.drop_duplicates(
        subset="PolicyReference", keep="first", inplace=True
    )
    rows_number = df_policy.shape[0]

    suff_alias = "__re_underwriting_col__"
    df = df_policy.merge(
        df_re_underwriting.add_suffix(suff_alias),
        how="left",
        left_on=["KeyIdPolis"],
        right_on=["PolicyReference" + suff_alias],
    )

    # Check if the merge was one-to-one:
    assert df.shape[0] == rows_number, (
        "The merge was not one-to-one. "
        "Please check that the primary key is unique in both dataframes."
    )

    # Add boolean column:
    df["ReUnderwritingExercise"] = df["PolicyReference" + suff_alias].notnull()
    df.drop(
        [column for column in df.columns if column.endswith(suff_alias)],
        axis=1,
        inplace=True,
    )
    return df
