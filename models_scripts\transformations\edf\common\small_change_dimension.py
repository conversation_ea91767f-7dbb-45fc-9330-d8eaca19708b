from datetime import date

import pyspark.sql.functions as F
from pyspark.sql import DataFrame, Window


def apply_scd2_from_long(
    df: DataFrame,
    primary_key: str,
    value_col: str,
    column_with_dates: str,
    valid_from_col="ValidFrom",
    valid_to_col="ValidTo",
) -> DataFrame:
    """Apply SCD2 on the dataframe. from long format to SCD type 2 format.
    so we expect a record for each key and date"""

    # 1. Create a new column with the next date that has a different value:
    original_cols = df.columns

    # 2. Create a new column to help group sequences of the same values:
    df = df.withColumn(
        "group",
        F.when(
            (
                df[value_col]
                == F.lag(df[value_col]).over(
                    Window.partitionBy(primary_key).orderBy(column_with_dates)
                )
            ),
            F.lit(None),
        ).otherwise(F.monotonically_increasing_id()),
    )

    # 2. Drop the rows with null values in the groupAux column:
    df = df.filter(df["group"].isNotNull())

    # 3. Create a new column with the start and end dates for each group:
    df = df.withColumn(
        valid_from_col,
        F.first(df[column_with_dates]).over(Window.partitionBy(primary_key, "group")),
    )

    # 4. Get Valid to col
    df = df.withColumn(
        valid_to_col,
        F.lead(F.date_sub(df[valid_from_col], days=1), default=date(2099, 12, 31)).over(
            Window.partitionBy(primary_key).orderBy("group")
        ),
    )

    # 5. Drop the intermediate columns:
    df = df.select(original_cols + [valid_from_col, valid_to_col])

    return df
