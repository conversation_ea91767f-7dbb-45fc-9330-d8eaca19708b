Country: DE
existCustomTransformation: 'True'

dataSource:
- name: main
  type: SourceCSV
  parameters:
    fileName: EDF ClaimArrayTransactionComponent.csv
    Separator: ','
    Encoding: UTF-8
    sourceSystem: SCS

- name: claim_transaction
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Claim
    Table: Transaction

- name: exchange_rate
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: test_mintdatalake_02
      uat: test_mintdatalake_02
      prod: prod_mintdatalake_02
    schema: rdm_dbo
    table: mint_mapping_roe
    sourceSystem: MINT_RAW_DATALAKE

ColumnSpecs:
  ClaimsID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: DE
        - source: COLUMN
          name: KeyInternSchadenummer
        sep: ':'
  ClaimsSectionTransID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: DE
        - source: COLUMN
          name: KeyInternSchadenummer
        - source: COLUMN
          name: KeyDekkingsNummer
        - source: COLUMN
          name: KeySchadeBoekingsNummer
        sep: ':'
  TransactionComponentAmount:
    locale: en_US.utf8
  TransactionComponentAmountGBP:
    NotInSource: True
  TransactionComponentAmountEUR:
    NotInSource: True
  TransactionComponentAmountUSD:
    NotInSource: True
  TransactionComponentAmountCAD:
    NotInSource: True
  TransactionComponentAmountRounded:
    NotInSource: True
  TransactionComponentAmountRoundedGBP:
    NotInSource: True
  TransactionComponentAmountRoundedEUR:
    NotInSource: True
  TransactionComponentAmountRoundedUSD:
    NotInSource: True
  TransactionComponentAmountRoundedCAD:
    NotInSource: True
