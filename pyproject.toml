[tool.pytest.ini_options]
pythonpath = [
  ".", "models_scripts"
]

[tool.commitizen]
name = "cz_customize"
version = "7.21.0"
version_files = [
    "pyproject.toml:version"
]
bump_message = "bump: Version $current_version → $new_version [skip ci]"

[tool.commitizen.customize]
message_template =  "{{change_type}}:{% if show_message %} {{message}}{% endif %}"
example = "feature: this feature enable customize through config file"
schema = "<type>: <body>"
schema_pattern = "(major|feat|model|fix|ci|test|refactor|style|docs|perf|build|partition|table):(\\s.*)"
bump_pattern = "^(major|feat|model|fix|ci|test|refactor|style|docs|perf|build|partition|table)"
bump_map = {"major" = "MAJOR", "feat" = "MINOR", "model" = "MINOR", "fix" = "PATCH", "ci" = "PATCH", "test" = "PATCH", "refactor" = "PATCH", "style" = "PATCH", "docs" = "PATCH", "build" = "PATCH", "partition" = "PATCH", "table" = "PATCH"}
change_type_order=["major", "feat", "model", "table", "fix", "refactor", "perf"]
commit_parser = "^(?P<change_type>major|feat|model|fix|ci|test|refactor|style|docs|perf|build|partition|table):\\s(?P<message>.*)?"
changelog_pattern = "^(major|feat|model|fix|ci|test|refactor|style|docs|perf|build|partition|table)?(!)?"

[[tool.commitizen.customize.questions]]
type = "list"
name = "change_type"
choices = [{value = "major", name = "major: A new major version of the solution. A major breaking change."}, {value = "feat", name = "feat: Any new feature/capability of the solution."}, {value = "model", name = "model: Any new model, or change at model level."},{value = "table", name = "table: Any new table or change on table level."}, {value = "partition", name = "partition: Any new partition or change on partition level."},{value = "fix", name = "fix: Any fix, regardless of model (yaml) fix or code fix"}, {value = "refactor", name = "refactor: Refactoring of code that does not change the behaviour"}, {value = "perf", name = "perf: Change in the code that affect only the performance, trying to improve it."}, {value = "build", name = "build: Change related to the build and versionf of the solution."}, {value = "ci", name = "ci: Change related to the azure devops pipelines."},{value = "test", name = "test: changes focused on the the suite, like new tests, tests fixes and so on."}, {value = "style", name = "style: style related, like removing white spaces."}, {value = "docs", name = "docs: Changes to docstrings, comments and documentation."}]
message = "Select the type of change you are committing"

[[tool.commitizen.customize.questions]]
type = "input"
name = "message"
message = "Write an assertive short description of the commit: "

[[tool.commitizen.customize.questions]]
type = "confirm"
name = "show_message"
message = "Do you want to add the body message in commit?"

[tool.poetry]
name = "models_scripts"
version = "7.21.0"
description = "Custom Transformations  and validations for Models"
authors = ["William Mesquita <<EMAIL>>", "Jonathan Sales <<EMAIL>>"]
license = "Internal Use Only"
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.12.3"
mines2 = "^2.8.1"
pyyaml = "^6.0.1"
requests = "^2.31.0"
unidecode = "^1.3.6"
babel = "^2.12.1"
pandas = "^1.5.3"
numpy = "1.26.4"

[tool.poetry.group.dev]
optional = true

[tool.poetry.group.dev.dependencies]
black = "^24.4.2"
pre-commit = "^2.21.0"
commitizen = "^2.38.0"
ipykernel = "^6.28.0"

[tool.poetry.group.validation]
optional = true

[tool.poetry.group.validation.dependencies]
pytest-azurepipelines = "^1.0.4"
pytest-cov = "^4.0.0"

[tool.poetry.group.tests]
optional = true

[tool.poetry.group.tests.dependencies]
pytest = "^7.2.0"
pytest-mock = "^3.10.0"
pytest-xdist = "^3.5.0"
freezegun = "^1.2.2"
databricks-connect = "^16.1.1"
pyarrow = "^15.0.2"

[[tool.poetry.source]]
name = "nm-data-engineering-download"
url = "https://markel.jfrog.io/artifactory/api/pypi/nm-data-engineering-pypi-prod-local/simple"
priority = "primary"  # Set to 'primary' when using JFrog's Pandas
 
[[tool.poetry.source]]
name = "pypi-official"
url = "https://pypi.org/simple"
priority = "supplemental"  # Set to 'supplemental' when using JFrog's Pandas

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
