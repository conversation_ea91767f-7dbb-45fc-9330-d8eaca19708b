from pyspark.sql import DataFrame

from models_scripts.transformations.common.misc import pandas_to_spark, spark_to_pandas
from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.backward_populate_records import (
    add_missing_ids,
)
from models_scripts.transformations.edf.common.currency import (
    PolicySubLimitCurrencyConversor,
)


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    """The objective of this transformation is to add the ids created on
    TransactionComponent custom transformation into the Transaction Table."""
    main_pdf = spark_to_pandas(df_dict["SubLimit_main"])
    extra_records_df = df_dict["Support_NewTransactionComponents"]
    exchange_rate_df = df_dict["SubLimit_exchange_rate"]

    assert "SubLimit" in main_pdf.columns, "Missing SubLimit column"
    main_pdf.rename(columns={"SubLimit": "SubLimitAOC"}, inplace=True)
    main_pdf["SubLimitAGG"] = main_pdf["SubLimitAOC"]

    output_df = pandas_to_spark(main_pdf)

    del main_pdf

    output_df = add_missing_ids(output_df, extra_records_df)
    output_df = PolicySubLimitCurrencyConversor.add_currency_columns(
        output_df, exchange_rate_df
    )

    return output_df
