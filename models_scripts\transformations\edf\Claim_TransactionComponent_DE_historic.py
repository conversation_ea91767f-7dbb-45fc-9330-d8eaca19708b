# import mines2.core.constants as const
import pyspark.sql.functions as F
from pyspark.sql import DataFrame

from models_scripts.transformations.common.misc import enforce_nulls_type
from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.currency import ClaimCurrencyConversor
from models_scripts.transformations.edf.DE_historic_openviva.get_openviva_boekings_nummer import (
    get_openviva_boekings_nummer,
)
from models_scripts.transformations.edf.DE_historic_openviva.get_openviva_transaction_sequence_number import (
    get_openviva_transaction_sequence_number,
)


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    # Load tables
    input_df = df_dict["TransactionComponent_main"]
    transaction_df = df_dict["Claim_Transaction"]
    exchange_rate_df = df_dict["TransactionComponent_exchange_rate"]

    # add TransactionSequenceNumber
    output_w_transsec_df = get_openviva_transaction_sequence_number(
        input_df, "yyyyMMddHHmmss"
    )

    # add KeySchadeBoekingsNummer
    output_w_boeking_nummer_df = get_openviva_boekings_nummer(output_w_transsec_df)

    # create TransactionReference from KeySchadeBoekingsNummer
    output_w_trans_ref_df_tc = output_w_boeking_nummer_df.withColumn(
        "TransactionReference", F.col("KeySchadeBoekingsNummer")
    )

    component_with_converted_currencies_df = ClaimCurrencyConversor.add_new_columns(
        output_w_trans_ref_df_tc, transaction_df, exchange_rate_df
    )

    output_df = enforce_nulls_type(component_with_converted_currencies_df)
    return output_df
