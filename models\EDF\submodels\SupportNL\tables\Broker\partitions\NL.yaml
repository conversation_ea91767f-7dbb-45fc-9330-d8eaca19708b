Country: NL
existCustomTransformation: 'False'
dataSource:
- name: main
  type: SourceProgressNL
  parameters:
    querySourceType: SCHEMA
    table: PUB.Tussenpersoon
ColumnSpecs:
  Address:
    sourceName: Adres
  IPTLiable:
    sourceName: Belastingplichtig
  BrokerNumber:
    sourceName: Tussenpersoonnummer
  City:
    sourceName: Woonplaats
  CountryCode:
    sourceName: Landcode
  DescriptionStatus:
    sourceName: OmschrijvingStatus
  Name:
    sourceName: Naam
  Name2:
    sourceName: Naam2
  NameNumber:
    sourceName: NaamNummer
  StatusCode:
    sourceName: Statuscode
  ZipCode:
    sourceName: Postcode
  BrokerCodeID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: BrokerNumber
        sep: ':'
