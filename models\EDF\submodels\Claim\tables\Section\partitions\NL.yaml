Country: NL
existCustomTransformation: 'True'

dataSource:
- name: claims
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: SupportNL
    Table: Claims

- name: hist_policy_version
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: SupportNL
    Table: HistPolicyVersion

- name: hist_section
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: SupportNL
    Table: HistSection

- name: claims_payments
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: SupportNL
    Table: ClaimPayments

- name: claims_reserves
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: SupportNL
    Table: ClaimReserves

ColumnSpecs:
  ClaimsID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: KeyInternSchadenummer
        sep: ':'
  ClaimsSectionPolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  ClaimsSectionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: KeyInternSchadenummer
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  KeyInternSchadenummer:
    locale: en_US.utf8
  KeyIdPolis:
    locale: en_US.utf8
  KeyDekkingsNummer:
    locale: en_US.utf8
  SectionProductCode:
    locale: en_US.utf8
  SectionReference:
    locale: en_US.utf8
