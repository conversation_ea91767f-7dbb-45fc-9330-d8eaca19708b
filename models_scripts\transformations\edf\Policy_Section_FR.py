import pyspark.sql.functions as F
from pyspark.sql import DataFrame
from pyspark.sql.window import Window

from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.currency import (
    PolicySectionCurrencyConversor,
)


class DeduplicateFrenchSectionData:
    def __init__(self, df: DataFrame):
        self.hyalin_df = df.filter(F.col("BrokerFullName") == "HYALIN")
        self.klarity_df = df.filter(F.col("BrokerFullName") == "KLARITY")
        self.portfolio_df = df.filter(F.col("BrokerFullName") == "MARKEL")
        self.spvie_df = df.filter(F.col("BrokerFullName") == "SPVIE")

    def deduplicate_data(self) -> DataFrame:
        klarity_df = self._deduplicate_klarity_data(self.klarity_df)
        hyalin_df = self._deduplicate_hyalin_spvie_data(self.hyalin_df)
        spvie_df = self._deduplicate_hyalin_spvie_data(self.spvie_df)

        result_df = hyalin_df.unionByName(klarity_df, allowMissingColumns=True)
        result_df = result_df.unionByName(spvie_df, allowMissingColumns=True)
        result_df = result_df.unionByName(self.portfolio_df, allowMissingColumns=True)

        return result_df

    @staticmethod
    def _deduplicate_klarity_data(klarity_df: DataFrame) -> DataFrame:
        """Deduplicate section data for Klarity binder"""
        # Summing the premium columns
        aggregated_df = klarity_df.groupBy("IdPolis", "PolicySequenceNumber").agg(
            F.sum("GWP").alias("GWP"),
            F.sum("NWP").alias("NWP"),
            F.sum("IPT").alias("IPT"),
            F.sum("Commission").alias("Commission"),
            F.max("DateReference").alias("DateReference"),
        )

        # Joining back to get the other columns
        window_spec = Window.partitionBy("IdPolis", "PolicySequenceNumber").orderBy(
            F.desc("DateReference")
        )
        klarity_df = klarity_df.drop(
            "GWP", "NWP", "IPT", "Commission", "Prime_HT", "Prime_TTC"
        )
        klarity_df = klarity_df.withColumn("row_num", F.row_number().over(window_spec))
        klarity_df = klarity_df.filter(F.col("row_num") == 1).drop("row_num")

        result_df = klarity_df.join(
            aggregated_df,
            on=["IdPolis", "PolicySequenceNumber", "DateReference"],
            how="inner",
        )
        return result_df

    @staticmethod
    def _deduplicate_hyalin_spvie_data(binder_df: DataFrame) -> DataFrame:
        """Deduplicate section data by keeping only the rows with the latest
        DateReference for each IdPolis and SectionProductCode.
        """
        # Summing the premium columns
        aggregated_df = binder_df.groupBy(
            "KeyIdPolis", "PolicySequenceNumber", "KeyDekkingsNummer"
        ).agg(
            F.max("GWP").alias("GWP"),
            F.max("NWP").alias("NWP"),
            F.max("IPT").alias("IPT"),
            F.max("Commission").alias("Commission"),
            F.max("DateReference").alias("DateReference"),
        )

        binder_df = binder_df.drop("GWP", "NWP", "IPT", "Commission")
        result_df = binder_df.join(
            aggregated_df,
            on=[
                "KeyIdPolis",
                "PolicySequenceNumber",
                "KeyDekkingsNummer",
                "DateReference",
            ],
            how="inner",
        )
        return result_df


def add_currency_columns(df: DataFrame, exchange_rate_df: DataFrame) -> DataFrame:
    """Adds currency columns to the DataFrame using the exchange rate DataFrame."""
    df_with_currency_code = df.withColumn("LimitCurrencyCode", F.lit("EUR"))
    output_df = PolicySectionCurrencyConversor.add_currency_columns(
        df_with_currency_code, exchange_rate_df
    )
    output_df = output_df.drop("LimitCurrencyCode")
    return output_df


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["Section_main"]
    exchange_rate_df = df_dict["Section_exchange_rate"]

    main_df = DeduplicateFrenchSectionData(main_df).deduplicate_data()
    main_df = main_df.withColumnRenamed("Inception_Date", "SectionEffectiveFromDate")
    main_df = main_df.withColumnRenamed("Expiry_Date", "SectionEffectiveToDate")
    main_df = main_df.withColumn("SectionReference", F.col("KeyDekkingsNummer"))
    main_df = add_currency_columns(main_df, exchange_rate_df)

    return main_df
