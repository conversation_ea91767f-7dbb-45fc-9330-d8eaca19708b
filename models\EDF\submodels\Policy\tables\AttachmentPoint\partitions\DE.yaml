Country: custom
existCustomTransformation: 'True'

dataSource:
- name: main
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: dev_europeandatalake_01
      uat: uat_europeandatalake_01
      prod: prod_europeandatalake_01
    schema: DE_Direct
    table: Policy_PolicyArrayAttachmentPoint
    sourceSystem: SPARK_EDF_EXPORT

- name: policy
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Policy
    Table: Policy

- name: exchange_rate
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: test_mintdatalake_02
      uat: test_mintdatalake_02
      prod: prod_mintdatalake_02
    schema: rdm_dbo
    table: mint_mapping_roe
    sourceSystem: MINT_RAW_DATALAKE

ColumnSpecs:
  AttachmentPoint:
    locale: en_US.utf8
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: DE
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  PolicySectionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: DE
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
