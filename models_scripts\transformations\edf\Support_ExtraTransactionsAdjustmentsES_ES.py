import pyspark.sql.functions as F
from pyspark.sql import DataFrame, Window

from models_scripts.transformations.common.traits import business_logic, transformation


@transformation
def add_calculated_columns(main_df: DataFrame) -> DataFrame:
    """Calculate columns that are missing in extra_records_df."""
    main_columns = main_df.columns
    sequential_col = F.row_number().over(Window.orderBy(*main_columns))
    prefix = "EXTRA_RECORDS_SOURCE01:"

    calc_transaction_reference_col = F.concat(
        F.lit(prefix), sequential_col.cast("string")
    )

    reference_col = calc_transaction_reference_col.alias("TransactionReference")
    key_schade_boekingsnummer_col = calc_transaction_reference_col.alias(
        "KeySchadeBoekingsNummer"
    )

    return main_df.select(
        *main_columns,
        reference_col,
        key_schade_boekingsnummer_col,
    )


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["ExtraTransactionsAdjustmentsES_main"]
    output_df = add_calculated_columns(main_df)
    return output_df
