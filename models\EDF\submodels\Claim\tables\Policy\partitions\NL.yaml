Country: NL
existCustomTransformation: 'True'
dataSource:
- name: main
  type: SourceProgressNL
  parameters:
    sqlFileName: Query ClaimPolicyArray.sql
    querySourceType: SQL_FILE
- name: policy
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Policy
    Table: Policy
ColumnSpecs:
  ClaimsPolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  ClaimsID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: KeyInternSchadenummer
        sep: ':'
  KeyInternSchadenummer:
    locale: en_US.utf8
  PolicyCode:
    locale: en_US.utf8
  PolicyReference:
    locale: en_US.utf8
  KeyIdPolis:
    locale: en_US.utf8
  PolicyYearOfAccount:
    NotInSource: True
