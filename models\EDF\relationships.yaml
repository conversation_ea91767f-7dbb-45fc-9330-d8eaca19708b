# Yaml to save all the relationships between the different tables
# This file is used by the mines to enforce the relationships between the tables
# The relationships are defined in the following way:
# - tableA: the name of the table A
#   tableB: the name of the table B
#   type: "1:0..1", "1:1", "0..1:0..N", "0..N:1", "0..N:0..N"
#   join_clause: A.value < 10 and A.id = B.id
#   description: a description of the relationship (optional)

- tableA: Policy.Policy
  tableB: Policy.Section
  type: "1:1..N"
  join_clause: A.PolicyID = B.PolicyID

- tableA: Policy.Excess
  tableB: Policy.Section
  type: "0..1:1"
  join_clause: A.PolicySectionID = B.PolicySectionID

- tableA: Policy.Limit
  tableB: Policy.Section
  type: "0..1:1"
  join_clause: A.PolicySectionID = B.PolicySectionID

- tableA: Policy.Section
  tableB: Policy.SubLimit
  type: "1:0..N"
  join_clause: A.PolicySectionID = B.PolicySectionID

- tableA: Policy.Section
  tableB: Policy.Transaction
  type: "1:0..N"
  join_clause: A.PolicySectionID = B.PolicySectionID

- tableA: Policy.Transaction
  tableB: Policy.TransactionComponent
  type: "1:1..N"
  join_clause: A.PolicySectionTransactionID = B.PolicySectionTransactionID

- tableA: Policy.Section
  tableB: Claim.Section
  type: "1:0..N"
  join_clause: A.ClaimsSectionPolicyID = B.ClaimsSectionPolicyID

- tableA: Policy.Policy
  tableB: Claim.Policy
  type: "1:0..N"
  join_clause: A.PolicyID = B.PolicyID

- tableA: Claim.Claim
  tableB: Claim.Policy
  type: "1:1"
  join_clause: A.ClaimsID = B.ClaimsID

- tableA: Claim.Claim
  tableB: Claim.Section
  type: "1:1..N"
  join_clause: A.ClaimsID = B.ClaimsID

- tableA: Claim.Section
  tableB: Claim.Transaction
  type: "1:0..N"
  join_clause: A.ClaimsSectionID = B.ClaimsSectionID

- tableA: Claim.Transaction
  tableB: Claim.TransactionComponent
  type: "1:1..N"
  join_clause: A.ClaimsSectionTransID = B.ClaimsSectionTransID

- tableA: Settlement.Policy
  tableB: Settlement.Section
  type: "1:1..N"
  join_clause: A.PolicyID = B.PolicyID

- tableA: Settlement.Section
  tableB: Settlement.Transaction
  type: "1:1..N"
  join_clause: A.PolicySectionID = B.PolicySectionID

- tableA: Settlement.Transaction
  tableB: Settlement.TransactionComponent
  type: "1:1..N"
  join_clause: A.SettledSectionTransactionID = B.SettledSectionTransactionID

- tableA: Reference.ReservingClasses
  tableB: Policy.Section
  type: "1:1..N"
  join_clause: A.KeyReserving = B.KeyReserving

- tableA: Policy.Policy
  tableB: Reference.RateAdequacyES
  type: "1:0..1"
  join_clause: A.PolicyID = B.PolicyID

- tableA: Reference.ActivityCodeES
  tableB: Support.ActivityCodeES
  type: "1:1..N"
  join_clause: A.CertificateNumber = B.CertificateNumber
