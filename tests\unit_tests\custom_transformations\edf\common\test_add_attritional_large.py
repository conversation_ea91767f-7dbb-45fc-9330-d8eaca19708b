from datetime import date

from mines2.core.extensions.misc import assert_dataframe_equality

from models_scripts.transformations.edf.common.add_attritional_large_flag import (
    AttritionalLargeFlagCalculator,
)


def test_add_attritional_large_flag(spark):
    """Test add_key_reserving_and_german_country_col function."""
    claim_claim_df = spark.createDataFrame(
        [
            ("Claim1", "NL"),
            ("Claim2", "NL"),
            ("Claim3", "NL"),
        ],
        ["KeyInternSchadenummer", "Partition"],
    )

    claim_section_df = spark.createDataFrame(
        [
            ("Claim1", "Policy1", "Section1", "NL"),
            ("Claim1", "Policy1", "Section2", "NL"),
            ("Claim1", "Policy1", "Section3", "NL"),
            ("Claim2", "Policy2", "Section4", "NL"),
            ("Claim2", "Policy2", "Section5", "NL"),
            ("Claim3", "Policy3", "Section6", "NL"),
        ],
        ["KeyInternSchadenummer", "KeyIdPolis", "KeyDekkingsNummer", "Partition"],
    )

    policy_section_df = spark.createDataFrame(
        [
            ("Reserving1", "Policy1", "Section1", "NL"),
            ("Reserving1", "Policy1", "Section2", "NL"),
            ("Reserving2", "Policy1", "Section3", "NL"),
            ("Reserving1", "Policy2", "Section4", "NL"),
            ("Reserving3", "Policy2", "Section5", "NL"),
            ("Reserving4", "Policy3", "Section6", "NL"),
        ],
        ["KeyReserving", "KeyIdPolis", "KeyDekkingsNummer", "Partition"],
    )

    reserving_class_df = spark.createDataFrame(
        [
            ("Reserving1", "Class1", 3000, "NL"),
            ("Reserving2", "Class1", 3000, "NL"),
            ("Reserving3", "Class1", 3000, "NL"),
            ("Reserving4", "Class2", 1000, "NL"),
            ("Reserving5", "Class3", 10, "NL"),
        ],
        [
            "KeyReserving",
            "ProductReservingClass",
            "AttritionalLargeThreshold",
            "Partition",
        ],
    )

    claim_transaction_component_df = spark.createDataFrame(
        [
            ("Claim1", "Section1", "TransactionID1", 1000.0, "NL"),
            ("Claim1", "Section1", "TransactionID1", 2000.0, "NL"),
            ("Claim1", "Section1", "TransactionID1", -1000.0, "NL"),
            ("Claim1", "Section1", "TransactionID2", 1500.0, "NL"),
            ("Claim1", "Section1", "TransactionID2", -1200.0, "NL"),
            ("Claim2", "Section4", "TransactionID3", 3000.0, "NL"),
            ("Claim2", "Section4", "TransactionID3", 0.0, "NL"),
            ("Claim2", "Section5", "TransactionID4", 300.0, "NL"),
            ("Claim2", "Section5", "TransactionID5", -2800.0, "NL"),
            ("Claim3", "Section6", "TransactionID6", 500.0, "NL"),
            ("Claim3", "Section6", "TransactionID7", -200.0, "NL"),
            ("Claim3", "Section6", "TransactionID8", 900.0, "NL"),
            ("Claim3", "Section6", "TransactionID9", 10.0, "NL"),
            ("Claim3", "Section6", "TransactionID10", -40.0, "NL"),
        ],
        [
            "KeyInternSchadenummer",
            "KeyDekkingsNummer",
            "KeySchadeBoekingsNummer",
            "TransactionComponentAmount",
            "Partition",
        ],
    )

    claim_transaction_df = spark.createDataFrame(
        [
            ("Claim1", "Section1", "TransactionID1", date(2020, 1, 1), "NL"),
            ("Claim1", "Section1", "TransactionID2", date(2020, 1, 1), "NL"),
            ("Claim2", "Section4", "TransactionID3", date(2020, 1, 3), "NL"),
            ("Claim2", "Section5", "TransactionID4", date(2020, 1, 4), "NL"),
            ("Claim2", "Section5", "TransactionID5", date(2020, 1, 5), "NL"),
            ("Claim3", "Section6", "TransactionID6", date(2020, 1, 6), "NL"),
            ("Claim3", "Section6", "TransactionID7", date(2020, 1, 7), "NL"),
            ("Claim3", "Section6", "TransactionID8", date(2020, 1, 8), "NL"),
            ("Claim3", "Section6", "TransactionID9", date(2020, 1, 9), "NL"),
            ("Claim3", "Section6", "TransactionID10", date(2020, 1, 10), "NL"),
        ],
        [
            "KeyInternSchadenummer",
            "KeyDekkingsNummer",
            "KeySchadeBoekingsNummer",
            "TransactionDate",
            "Partition",
        ],
    )

    expected_df = spark.createDataFrame(
        [
            ("Claim1", "NL", "Attritional"),
            ("Claim2", "NL", "Large"),
            ("Claim3", "NL", "Large"),
        ],
        ["KeyInternSchadenummer", "Partition", "AttritionalLargeFlag"],
    )

    output_df = AttritionalLargeFlagCalculator(
        claim_claim_df,
        claim_section_df,
        claim_transaction_df,
        claim_transaction_component_df,
        policy_section_df,
        reserving_class_df,
        partition="NL",
    ).add_flag()

    assert_dataframe_equality(output_df, expected_df)
