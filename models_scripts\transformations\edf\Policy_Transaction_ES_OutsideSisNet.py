import pyspark.sql.functions as F
from pyspark.sql import DataFrame, Window

from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.add_columns import (
    AddUSGAAPDateColumn,
    get_spanish_country_using_policy_table,
)


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["Transaction_main"]
    policy_df = df_dict["Policy_Policy"]
    gaap_df = df_dict["Reference_DatesGAAP"]

    main_df = main_df.withColumn(
        "TransactionDate", F.col("TransactionDate").cast("date")
    )
    output_df = get_spanish_country_using_policy_table(main_df, policy_df)

    # Drop duplicates in policy_df based on KeyIdPolis and Partition:
    window = Window.partitionBy("KeyIdPolis").orderBy(F.col("Partition").desc())
    policy_df = policy_df.withColumn("row_number", F.row_number().over(window))
    policy_df = policy_df.filter(F.col("row_number") == 1).drop("row_number")

    output_df = AddUSGAAPDateColumn(output_df, gaap_df, policy_df).add_usgaap_date()

    return output_df
