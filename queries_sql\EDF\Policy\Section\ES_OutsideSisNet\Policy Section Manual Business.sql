/* QUERY POLICY_SECTION MANUAL DEF */

SELECT DISTINCT
    CASE WHEN B.Certificate_number IS NOT NULL THEN B.KeyIdPolis ELSE A.KeyIdPolis END AS 'KeyIdPolis',
    CASE WHEN B.Certificate_number IS NOT NULL THEN CONCAT(<PERSON><PERSON>KeyDek<PERSON>Nummer,RIGHT(<PERSON><PERSON>,4)) ELSE A.KeyDekkingsNummer END AS 'KeyDekkingsNummer',
    A.<PERSON>st<PERSON>asisCode AS 'CostBasisCode',
    A.CostBasisDescription AS 'CostBasisDescription',
    A.CoverageType AS 'CoverageType',
    A<PERSON>stSignedDown AS 'EstSignedDown',
    A.IBCCoverageCode AS 'IBCCoverageCode',
    A.InsurerCarrierCode AS 'InsurerCarrierCode',
    A.InsurerCarrierDescription AS 'InsurerCarrierDescription',
    A.InsurerCarrierPercentage AS 'InsurerCarrierPercentage',
    A.<PERSON>lai<PERSON>Bon<PERSON> AS 'NoClaimsBonus',
    A.Jurisdiction AS 'Jurisdiction',
    A.<PERSON>Ter<PERSON> 'OperatingTerritory',
    A.TerritorialScope AS 'TerritorialScope',
    A.<PERSON>,
    A.PremiumBasisCode AS 'PremiumBasisCode',
    A.ProfitCommission AS 'ProfitCommission',
    A.SectionEffectiveFromDate AS 'SectionEffectiveFromDate',
    A.SectionEffectiveToDate AS 'SectionEffectiveToDate',
    A.SectionProductCode AS 'SectionProductCode',
    A.SectionProductDescription AS 'SectionProductDescription',
    CASE WHEN B.Certificate_number is not null THEN CONCAT(B.KeyDekkingsNummer,RIGHT(A.KeyDekkingsNummer,4)) ELSE A.KeyDekkingsNummer END AS 'SectionReference',
    A.SectionStatusCode AS 'SectionStatusCode',
    A.SignedLine AS 'SignedLine',
    A.SignedOrder AS 'SignedOrder',
    A.SubLimitsIndicator AS 'SubLimitsIndicator',
    A.WrittenLine AS 'WrittenLine',
    A.WrittenOrder AS 'WrittenOrder'

FROM ( /* MANUAL DATA */
    SELECT DISTINCT
       REPLACE(CASE WHEN A.NPOLICY = '' THEN TRIM(A.POLIORIG) ELSE TRIM(A.NPOLICY) END, ' ', '') Certificate_number,
       B.YEAREFEC,
            CASE
            WHEN (CASE WHEN A.NPOLICY = '' THEN A.POLIORIG ELSE A.NPOLICY END) IS NOT NULL THEN
                CONCAT(
                    A.CODPROD
                    ,' '
                    ,(SELECT MIN(CAST(Z.YEAREFEC AS float)) FROM NTJDWHMRK..MEDLMAPS Z WHERE B.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK)
                    ,'/'
                    ,CASE WHEN A.NPOLICY = '' THEN A.POLIORIG ELSE A.NPOLICY END
                    ,'_'
                    ,(CAST(B.YEAREFEC AS float) - (SELECT MIN(CAST(Z.YEAREFEC AS float)) FROM NTJDWHMRK..MEDLMAPS Z WHERE B.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK) + 1)
                )
            WHEN (CASE WHEN A.NPOLICY = '' THEN A.POLIORIG ELSE A.NPOLICY END) IS NULL THEN
                (
                    SELECT DISTINCT POLICODE
                    FROM NTJDWHMRK..MEDFPOLI X
                    WHERE REPLACE(CASE WHEN A.NPOLICY = '' THEN TRIM(A.POLIORIG) ELSE TRIM(A.NPOLICY) END,' ','') = (LEFT(X.POLIREFE, CHARINDEX('_',X.POLIREFE)-1))
                    AND B.YEAREFEC = X.YEAOFACC
                )
        END AS 'KeyIdPolis',
        CONCAT (
            TRIM(CASE WHEN A.NPOLICY = '' THEN A.POLIORIG ELSE A.NPOLICY END),
            CASE
                WHEN A.SECREGRU = '1. Construction Professional Risks' THEN '/MA1'
                WHEN A.SECREGRU = '2. Financial Services Professional Risks' THEN '/MA2'
                WHEN A.SECREGRU = '3. Miscellaneous Professional Risks' THEN '/MA3'
                WHEN A.SECREGRU = '4. Professions Professional Risks' THEN '/MA4'
                when A.SECREGRU = '6. D and O' THEN '/MA5'
                WHEN A.SECREGRU = '11. Liability' THEN '/MA6'
                WHEN A.SECREGRU = 'Surety' THEN '/MA7'
                WHEN A.SECREGRU = 'Personal Accident' THEN '/MA8'
                WHEN A.SECREGRU = 'Private Clinics' THEN '/MA9'
                WHEN A.SECREGRU = 'Energy' THEN '/MA10'
                WHEN A.SECREGRU = 'Terror' THEN '/MA11'
            ELSE '/MAN' END
        ) AS 'KeyDekkingsNummer',
        'None' AS 'CostBasisCode',
        'None' AS 'CostBasisDescription',
        'None' AS 'CoverageType',
        '' AS 'EstSignedDown',
        'None' AS 'IBCCoverageCode',
        'None' AS 'InsurerCarrierCode',
        'None' AS 'InsurerCarrierDescription',
        '' AS 'InsurerCarrierPercentage',
        'None' AS 'NoClaimsBonus',
        CASE WHEN A.PAIS = 'Portugal' THEN 'UE' WHEN A.PAIS = 'EspaÃ±a' THEN 'ESPA' ELSE 'UE' END AS 'Jurisdiction',
        CASE WHEN A.PAIS = 'Portugal' THEN 'PORT' WHEN A.PAIS = 'EspaÃ±a' THEN 'ESPA' ELSE A.PAIS END AS 'OperatingTerritory',
        CASE WHEN A.PAIS = 'Portugal' THEN 'PORT' WHEN A.PAIS = 'EspaÃ±a' THEN 'ESPA' ELSE A.PAIS END AS 'TerritorialScope',
        PAIS,
        'None' AS 'PremiumBasisCode',
        '' AS 'ProfitCommission',
        (SELECT MIN (Z.FECHVENC) FROM NTJDWHMRK..MEDLMAPS Z WHERE B.YEAREFEC = Z.YEAREFEC AND B.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK) AS 'SectionEffectiveFromDate',
        (SELECT MAX (Z.FECHEFEC) FROM NTJDWHMRK..MEDLMAPS Z WHERE B.YEAREFEC = Z.YEAREFEC AND B.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK) AS 'SectionEffectiveToDate',
        'INVA' AS 'SectionProductCode',
        'Invalidez' AS 'SectionProductDescription',
        CONCAT (
            TRIM(CASE WHEN A.NPOLICY = '' THEN A.POLIORIG ELSE A.NPOLICY END),
            CASE
                WHEN A.SECREGRU = '1. Construction Professional Risks' THEN '/MA1'
                WHEN A.SECREGRU = '2. Financial Services Professional Risks' THEN '/MA2'
                WHEN A.SECREGRU = '3. Miscellaneous Professional Risks' THEN '/MA3'
                WHEN A.SECREGRU = '4. Professions Professional Risks' THEN '/MA4'
                when A.SECREGRU = '6. D and O' THEN '/MA5'
                WHEN A.SECREGRU = '11. Liability' THEN '/MA6'
                WHEN A.SECREGRU = 'Surety' THEN '/MA7'
                WHEN A.SECREGRU = 'Personal Accident' THEN '/MA8'
                WHEN A.SECREGRU = 'Private Clinics' THEN '/MA9'
                WHEN A.SECREGRU = 'Energy' THEN '/MA10'
                WHEN A.SECREGRU = 'Terror' THEN '/MA11'
            ELSE '/MAN' END
        ) AS 'SectionReference',
        CASE
            WHEN (SELECT MAX (TIPOMOVI) FROM NTJDWHMRK..MEDLMAPS Z WHERE Z.ID_MEDLMAPS = (SELECT MAX (ID_MEDLMAPS) FROM NTJDWHMRK..MEDLMAPS Z WHERE B.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK)) = 'Suplemento' THEN 'VIGE'
            WHEN (SELECT MAX (TIPOMOVI) FROM NTJDWHMRK..MEDLMAPS Z WHERE Z.ID_MEDLMAPS = (SELECT MAX (ID_MEDLMAPS) FROM NTJDWHMRK..MEDLMAPS Z WHERE B.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK)) = 'Anulación' THEN 'ANUL'
            WHEN (SELECT MAX (TIPOMOVI) FROM NTJDWHMRK..MEDLMAPS Z WHERE Z.ID_MEDLMAPS = (SELECT MAX (ID_MEDLMAPS) FROM NTJDWHMRK..MEDLMAPS Z WHERE B.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK)) = 'Emisión' THEN 'VIGE'
            WHEN (SELECT MAX (TIPOMOVI) FROM NTJDWHMRK..MEDLMAPS Z WHERE Z.ID_MEDLMAPS = (SELECT MAX (ID_MEDLMAPS) FROM NTJDWHMRK..MEDLMAPS Z WHERE B.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK)) = 'Renovación' THEN 'VIGE'
            WHEN (SELECT MAX (TIPOMOVI) FROM NTJDWHMRK..MEDLMAPS Z WHERE Z.ID_MEDLMAPS = (SELECT MAX (ID_MEDLMAPS) FROM NTJDWHMRK..MEDLMAPS Z WHERE B.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK)) = 'Extorno' THEN 'VIGE'
            WHEN (SELECT MAX (TIPOMOVI) FROM NTJDWHMRK..MEDLMAPS Z WHERE Z.ID_MEDLMAPS = (SELECT MAX (ID_MEDLMAPS) FROM NTJDWHMRK..MEDLMAPS Z WHERE B.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK)) = 'Rehabilitación' THEN 'VIGE'
            WHEN (SELECT MAX (TIPOMOVI) FROM NTJDWHMRK..MEDLMAPS Z WHERE Z.ID_MEDLMAPS = (SELECT MAX (ID_MEDLMAPS) FROM NTJDWHMRK..MEDLMAPS Z WHERE B.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK)) = 'Regularización' THEN 'VIGE'
        ELSE '' END AS 'SectionStatusCode',
        '100%' AS 'SignedLine',
        '100%' AS 'SignedOrder',
        'N' AS 'SubLimitsIndicator',
        '100%' AS 'WrittenLine',
        '100%' AS 'WrittenOrder'
    FROM  NTJDWHMRK..MEDLMAPO A, NTJDWHMRK..MEDLMAPS B
    WHERE A.ID_MEDLMAPO = B.ID_MEDLMAPO_FK
) A

LEFT JOIN ( /* SISNET DATA */
    SELECT DISTINCT
        TRIM(LEFT(A.POLIREFE, CHARINDEX('_',A.POLIREFE) - 1)) Certificate_number,
        A.YEAOFACC,
        A.POLICODE AS 'KeyIdPolis',
        TRIM(LEFT(B.SECTREFE, CHARINDEX('/',B.SECTREFE) - 1)) 'KeyDekkingsNummer'
        --B.SECTREFE AS 'KeyDekkingsNummer',
        --'None' AS 'CostBasisCode',
        --'None' AS 'CostBasisDescription',
        --'None' AS 'CoverageType',
        --'None' AS 'Deductible',
        --'' AS 'EstSignedDown',
        --'None' AS 'IBCCoverageCode',
        --'None' AS 'InsurerCarrierCode',
        --'None' AS 'InsurerCarrierDescription',
        --'' AS 'InsurerCarrierPercentage',
        --B.JURISDIC AS 'Jurisdiction',
        --'None' AS 'NoClaimsBonus',
        --B.OPERTERR AS 'OperatingTerritory',
        --'None' AS 'PremiumBasisCode',
        --'' AS 'ProfitCommission',
        --'' AS 'RateOnExposure',
        --B.SEEFFRDA AS 'SectionEffectiveFromDate',
        --B.SEEFTODA AS 'SectionEffectiveToDate',
        --B.SECPROCO AS 'SectionProductCode',
        --B.SECPRODE AS 'SectionProductDescription',
        --B.SECTREFE AS 'SectionReference',
        --B.SECSTACO AS 'SectionStatusCode',
        --B.SIGNLINE AS 'SignedLine',
        --B.SIGNORDE AS 'SignedOrder',
        --B.SUBLIMIN AS 'SubLimitsIndicator',
        --B.TERRSCOP AS 'TerritorialScope',
        --B.WRITLINE AS 'WrittenLine',
        --B.WRITORDE AS 'WrittenOrder'
    FROM NTJDWHMRK..MEDFPOLI A, NTJDWHMRK..MEDFPOSE B
    WHERE
        A.ID_MEDFPOLI = B.ID_MEDFPOLI_FK AND
        B.ID_MEDFPOSE IN (
            SELECT MAX(X.ID_MEDFPOSE)
            FROM NTJDWHMRK..MEDFPOLI Z, NTJDWHMRK..MEDFPOSE X
            WHERE
                Z.ID_MEDFPOLI = X.ID_MEDFPOLI_FK AND
                Z.FECHALTA BETWEEN CONVERT(datetime,'01/01/2000',103) AND CONVERT(datetime,'31/12/2030',103) AND
                Z.POLICODE = A.POLICODE AND
                B.SECTREFE = X.SECTREFE
        )
) B ON TRIM(A.Certificate_number) = TRIM(B.Certificate_number) AND CAST(A.YEAREFEC AS FLOAT)= CAST(B.YEAOFACC AS FLOAT)
