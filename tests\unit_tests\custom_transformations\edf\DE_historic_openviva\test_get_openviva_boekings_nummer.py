import pytest
from mines2.core.extensions.misc import assert_dataframe_equality
from pyspark.sql.types import StringType, StructField, StructType

from models_scripts.transformations.edf.DE_historic_openviva.get_openviva_boekings_nummer import (
    get_openviva_boekings_nummer,
)


@pytest.fixture(scope="module")
def main_df(spark):
    return spark.createDataFrame(
        data=[
            # Reserves
            (
                "1001",
                "500000",
                "1-1",
                "R",
                "20201001000000",
            ),
            (
                "1001",
                "500000",
                "1-1",
                "R",
                "20210101000000",
            ),
            # Payments
            (
                "1001",
                "500000",
                "1-1",
                "Z",
                "20201001000000",
            ),
            (
                "1001",
                "500000",
                "1-1",
                "Z",
                "20210101000000",
            ),
        ],
        schema=StructType(
            [
                StructField("KeyInternSchadenummer", StringType()),
                StructField("KeyIdPolis", StringType()),
                StructField("KeyDekkingsNummer", StringType()),
                StructField("TransactionType", StringType()),
                StructField("TransactionSequenceNumber", StringType()),
            ]
        ),
    )


@pytest.fixture(scope="module")
def expected_df(spark):
    return spark.createDataFrame(
        data=[
            # Reserves
            (
                "1001",
                "500000",
                "1-1",
                "OV-R-1",
                "R",
                "20201001000000",
            ),
            (
                "1001",
                "500000",
                "1-1",
                "OV-R-2",
                "R",
                "20210101000000",
            ),
            # Payments
            (
                "1001",
                "500000",
                "1-1",
                "OV-Z-1",
                "Z",
                "20201001000000",
            ),
            (
                "1001",
                "500000",
                "1-1",
                "OV-Z-2",
                "Z",
                "20210101000000",
            ),
        ],
        schema=StructType(
            [
                StructField("KeyInternSchadenummer", StringType()),
                StructField("KeyIdPolis", StringType()),
                StructField("KeyDekkingsNummer", StringType()),
                StructField("KeySchadeBoekingsNummer", StringType()),
                StructField("TransactionType", StringType()),
                StructField("TransactionSequenceNumber", StringType()),
            ]
        ),
    )


@pytest.fixture(scope="module")
def result_df(main_df):
    return get_openviva_boekings_nummer(main_df)


def test_get_openviva_boekings_nummer(result_df, expected_df):
    assert_dataframe_equality(result_df, expected_df)


def test_get_assured_fullname_new_col(result_df, main_df):
    new_col_names = [col for col in result_df.columns if col not in main_df.columns]

    assert new_col_names == ["KeySchadeBoekingsNummer"]


def test_get_assured_fullname_count_eq(result_df, main_df):
    assert result_df.count() == main_df.count()
