import pyspark.sql.functions as F
from pyspark.sql import DataFrame

from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.add_columns import AddUSGAAPDateColumn
from models_scripts.transformations.edf.common.sisnet_scs_migration import (
    exclude_matched_policies_in_scs_and_sisnet,
)


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["Transaction_main"]
    policy_df = df_dict["Policy_Policy"]
    gaap_df = df_dict["Reference_DatesGAAP"]
    mapping_df = df_dict["scs_sisnet_compare"]

    main_df = main_df.withColumn(
        "TransactionDate", F.to_date(F.col("TransactionDate"), "dd/MM/yyyy")
    )
    main_df = exclude_matched_policies_in_scs_and_sisnet(main_df, mapping_df)
    output_df = AddUSGAAPDateColumn(main_df, gaap_df, policy_df).add_usgaap_date()

    return output_df
