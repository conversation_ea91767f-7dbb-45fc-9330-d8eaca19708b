Country: ES
existCustomTransformation: 'True'

dataSource:
- name: main
  type: SourceSisnetES
  parameters:
    querySourceType: SQL_FILE
    sqlFileName: claim_scs_sisnet_map.sql
    selectColumnsFromSchema: False

- name: claim_policy_sisnet
  type: EuropeanDatalake
  parameters:
    Layer: standard
    subModel: Claim
    Table: Policy_main

- name: claim_transaction_component_sisnet
  type: EuropeanDatalake
  parameters:
    Layer: standard
    subModel: Claim
    Table: TransactionComponent_main

- name: claim_section_scs
  type: EuropeanDatalake
  parameters:
    Layer: standard
    subModel: Claim
    Table: Section_main
    Partitions:
      - ES_historic

- name: unmapped_scs
  type: SourceCSV
  parameters:
    fileName: find_SCS_policy.csv
    Separator: ','
    Encoding: UTF-8

- name: original_scs_policy
  type: SourceCSV
  parameters:
    fileName: OriginalSCSPolicy.csv
    Separator: ';'
    Encoding: UTF-8

ColumnSpecs: {}
