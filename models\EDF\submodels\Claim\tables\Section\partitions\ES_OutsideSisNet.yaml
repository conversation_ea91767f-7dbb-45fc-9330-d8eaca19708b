Country: ES
existCustomTransformation: 'False'

dataSource:
- name: main
  type: SourceSisnetES
  parameters:
    sqlFileName: Claim Section Manual.sql
    querySourceType: SQL_FILE
    selectColumnsFromSchema: False

ColumnSpecs:
  ClaimsID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyInternSchadenummer
        sep: ':'
  ClaimsSectionPolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  ClaimsSectionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyInternSchadenummer
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  KeyInternSchadenummer:
    locale: en_US.utf8
  KeyIdPolis:
    locale: en_US.utf8
  KeyDekkingsNummer:
    locale: en_US.utf8
  SectionProductCode:
    locale: en_US.utf8
  SectionReference:
    locale: en_US.utf8
