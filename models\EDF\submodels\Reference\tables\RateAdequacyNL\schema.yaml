Columns:
  KeyIdPolis:
    dataType: string
  PolicyReference:
    dataType: string
  YearOfAccount:
    dataType: string
  Internpolisnummer:
    dataType: string
  ClientNumber:
    dataType: int
  SectionProductCode:
    dataType: string
  OriginalInceptionDate:
    dataType: date
  LimitAOC:
    dataType: string
  LimitAGG:
    dataType: string
  Deductible:
    dataType: string
  Commission:
    dataType: double
  IPT:
    dataType: double
  NetPremium:
    dataType: double
  Nm_document:
    dataType: string
  dtMod:
    dataType: date
  RCFBusinessClass:
    dataType: string
  InceptionDateYOA:
    dataType: date
  RCFLimitAOC:
    dataType: double
  RCFLimitAGG:
    dataType: double
  RCFDeductible:
    dataType: double
  RCFBrokerCommission:
    dataType: string
  RCFBasePremium:
    dataType: double
  RCFDiscSurch:
    dataType: double
  RCFFinalPremium:
    dataType: double
  _rescued_data:
    dataType: string
