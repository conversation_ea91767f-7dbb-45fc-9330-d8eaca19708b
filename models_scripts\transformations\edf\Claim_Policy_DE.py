from pyspark.sql import DataFrame

from models_scripts.transformations.common.add_columns import (
    add_column_from_match_table,
)
from models_scripts.transformations.common.misc import (
    COUNTRY_COLUMN_NAME,
    TEMP_COUNTRY_COLUMN,
    enforce_nulls_type,
)
from models_scripts.transformations.common.traits import business_logic


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["Policy_main"]
    policy_df = df_dict["Policy_Policy"]
    output_df = add_column_from_match_table(
        main_df,
        policy_df,
        "PolicyCode",
        {
            COUNTRY_COLUMN_NAME: TEMP_COUNTRY_COLUMN,
            "YearOfAccount": "PolicyYearOfAccount",
        },
    )
    output_df = enforce_nulls_type(output_df)

    return output_df
