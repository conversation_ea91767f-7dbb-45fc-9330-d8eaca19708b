import mines2.core.constants as const
import pyspark.sql.functions as F
from mines2.singlepartition.transform_functions import concat
from pyspark.sql import DataFrame

from models_scripts.transformations.common.add_columns import (
    add_column_from_match_table,
)
from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.currency import (
    PolicySectionCurrencyConversor,
)
from models_scripts.transformations.edf.common.keyreserving import (
    add_key_reserving_sisnet,
)


def add_currency_columns(
    df: DataFrame, exchange_rate_df: DataFrame, limit_df: DataFrame
) -> DataFrame:
    """Adds currency columns to the DataFrame using the exchange rate DataFrame."""
    # Create the PolicySectionID calculated column:
    columns = [
        {"source": "CONSTANT", "value": "ES"},
        {"source": "COLUMN", "name": "KeyIdPolis"},
        {"source": "COLUMN", "name": "KeyDekkingsNummer"},
    ]
    df = df.withColumn("PolicySectionID", concat(columns, ":"))

    df_with_currency_code = add_column_from_match_table(
        df,
        limit_df,
        "PolicySectionID",
        {"LimitCurrencyCode": None},
    )
    if "Deductible" not in df_with_currency_code.columns:
        df_with_currency_code = df_with_currency_code.withColumn(
            "Deductible", F.lit(None).cast("float")
        )
    output_df = PolicySectionCurrencyConversor.add_currency_columns(
        df_with_currency_code, exchange_rate_df
    )
    output_df = output_df.drop("LimitCurrencyCode", "PolicySectionID")
    return output_df


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    sisnet_section_df = df_dict["Section_main"]
    policy_df = df_dict["Policy_Policy"].drop(const.SOURCE_SYSTEM)
    activity_code_df = df_dict["Support_ActivityCodeES"].drop(const.SOURCE_SYSTEM)
    exchange_rate_df = df_dict["Section_exchange_rate"]
    limit_df = df_dict["Policy_Limit"]

    output_df = add_key_reserving_sisnet(sisnet_section_df, policy_df, activity_code_df)
    output_df = add_currency_columns(output_df, exchange_rate_df, limit_df)

    return output_df
