SELECT DISTINCT
    A<PERSON>POLICODE AS 'KeyIdPolis',
    B.SECTREFE AS 'KeyDekkingsNummer',
    'None' AS 'CostBasisCode',
    'None' AS 'CostBasisDescription',
    'None' AS 'CoverageType',
    '' AS 'EstSignedDown',
    'None' AS 'IBCCoverageCode',
    'None' AS 'InsurerCarrierCode',
    'None' AS 'InsurerCarrierDescription',
    '' AS 'InsurerCarrierPercentage',
    B.JURISDIC AS 'Jurisdiction',
    'None' AS 'NoClaimsBonus',
    B.OPERTERR AS 'OperatingTerritory',
    'None' AS 'PremiumBasisCode',
    '' AS 'ProfitCommission',
    B.SEEFFRDA AS 'SectionEffectiveFromDate',
    B.SEEFTODA AS 'SectionEffectiveToDate',
    B.SECPROCO AS 'SectionProductCode',
    B.SECPRODE AS 'SectionProductDescription',
    B.SECTREFE AS 'SectionReference',
    B.SECSTACO AS 'SectionStatusCode',
    <PERSON><PERSON>SIGNLINE AS 'SignedLine',
    B<PERSON>SIGNORDE AS 'SignedOrder',
    B.SUBLIMIN AS 'SubLimitsIndicator',
    B.TERRSCOP AS 'TerritorialScope',
    B.WRITLINE AS 'WrittenLine',
    B.WRITORDE AS 'WrittenOrder'
FROM
    NTJDWHMRK..MEDFPOLI A,
    NTJDWHMRK..MEDFPOSE B
WHERE
    A.ID_MEDFPOLI = B.ID_MEDFPOLI_FK
    AND B.ID_MEDFPOSE IN (
        SELECT MAX(X.ID_MEDFPOSE)
        FROM
            NTJDWHMRK..MEDFPOLI Z,
            NTJDWHMRK..MEDFPOSE X
        WHERE
            Z.ID_MEDFPOLI = X.ID_MEDFPOLI_FK
            AND Z.FECHALTA >= CONVERT(datetime,'01/01/2000',103)
            AND Z.POLICODE = A.POLICODE
            AND B.SECTREFE = X.SECTREFE
    )
