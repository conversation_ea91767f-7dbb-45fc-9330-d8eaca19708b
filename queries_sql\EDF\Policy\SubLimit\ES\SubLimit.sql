SELECT DISTINCT
    'SISnet' AS Source,
    B.POLICODE AS 'KeyIdPolis',
    C.SECTREFE AS 'KeyDekkingsNummer',
    A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> AS 'SubLimit',
    <PERSON><PERSON>SLIMBACO AS 'SubLimitBasisCode',
    A<PERSON><PERSON>IMBADE AS 'SubLimitBasisDescription',
    <PERSON><PERSON>IM<PERSON> AS 'SubLimitCurrencyCode'
FROM
    NTJDWHMRK..MEDFPOSL A,
    NTJDWHMRK..MEDFPOLI B,
    NTJDWHMRK..MEDFPOSE C
WHERE
    B.ID_MEDFPOLI = C.ID_MEDFPOLI_FK
    AND C.ID_MEDFPOSE = A.ID_MEDFPOSE_FK
    AND C.ID_MEDFPOSE IN (
        SELECT MAX(X.ID_MEDFPOSE)
        FROM
            NTJDWHMRK..MEDFPOLI Z,
            NTJDWHMRK..MEDFPOSE X
        WHERE
            Z.ID_MEDFPOLI = X.ID_MEDFPOLI_FK
            AND Z.FECHALTA >= CONVERT(datetime,'01/01/2000',103)
            AND Z.POLICODE = B.POLICODE
            AND C.SECTREFE = X.SECTREFE
    )
