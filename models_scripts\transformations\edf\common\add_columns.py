import pyspark.sql.functions as F
from pyspark.sql import DataFrame, Window

from models_scripts.transformations.common.add_columns import (
    add_column_from_match_table,
)
from models_scripts.transformations.common.misc import (
    COUNTRY_COLUMN_NAME,
    TEMP_COUNTRY_COLUMN,
)


def add_german_country(df: DataFrame) -> DataFrame:
    """Split the country column between swiss and german based on the currency."""
    swiss_code = "CH"
    german_code = "DE"
    is_swiss_cond = F.col("AssuredAnnualTurnoverCurrency") == "CHF"
    country_code = F.when(is_swiss_cond, swiss_code).otherwise(german_code)
    df = df.withColumn(TEMP_COUNTRY_COLUMN, country_code)
    return df


def get_german_country_using_policy_table(
    df: DataFrame, policy_df: DataFrame, join_col="KeyIdPolis"
) -> DataFrame:
    """Replicate the Country column on PolicyPolicy to all the german tables."""
    enhanced_df = add_column_from_match_table(
        df, policy_df, join_col, {COUNTRY_COLUMN_NAME: TEMP_COUNTRY_COLUMN}
    )
    return enhanced_df


def add_key_reserving_and_german_country_col(
    main_df: DataFrame, policy_df: DataFrame
) -> DataFrame:
    enhanced_df = add_column_from_match_table(
        main_df,
        policy_df,
        "KeyIdPolis",
        {
            COUNTRY_COLUMN_NAME: TEMP_COUNTRY_COLUMN,
            "PolicyProductCode": "PolicyProductCode_policy",
        },
    )

    enhanced_df = enhanced_df.withColumn(
        "KeyReserving",
        F.concat(
            F.lit("DE"),
            F.lit(":"),
            F.col("PolicyProductCode_policy"),
            F.lit(":"),
            F.col("SectionProductCode"),
        ),
    )
    enhanced_df = enhanced_df.drop("PolicyProductCode_policy")

    return enhanced_df


def convert_business_classification(df: DataFrame) -> DataFrame:
    """Update business classification explicitly when it matches mapping keys."""

    df = df.withColumn(
        "BusinessClassification",
        F.when(
            F.trim(F.col("BusinessClassification")) == "HighTouch",
            "Tailormade Solutions",
        )
        .when(
            F.trim(F.col("BusinessClassification")) == "LowTouch", "Product Solutions"
        )
        .otherwise(F.col("BusinessClassification")),
    )

    return df


def add_business_classification(df: DataFrame) -> DataFrame:
    """Create BusinessClassification column from IBCIndustryCode and fill
    IBCIndustryCode column with nulls.
    """
    df = df.withColumn("BusinessClassification", df["IBCIndustryCode"])
    df = df.withColumn("IBCIndustryCode", F.lit(None).cast("string"))
    df = convert_business_classification(df)
    return df


def add_spanish_country(df: DataFrame) -> DataFrame:
    """Add a temporary country column based on the AssuredCountry field.

    This function determines the country code by checking the AssuredCountry field:
    - If AssuredCountry is 'PORT', assigns 'PT' (Portugal)
    - If AssuredCountry is 'Italia', assigns 'IT' (Italy)
    - Otherwise, assigns 'ES' (Spain)

    Args:
        df (DataFrame): Input DataFrame containing the AssuredCountry column

    Returns:
        DataFrame: DataFrame with an additional temporary country column (TEMP_COUNTRY_COLUMN)
    """
    port_code = "PT"
    spain_code = "ES"
    italy_code = "IT"
    is_port_cond = F.col("AssuredCountry") == "PORT"
    is_italy_cond = F.col("AssuredCountry") == "Italia"
    country_code = (
        F.when(is_port_cond, port_code)
        .when(is_italy_cond, italy_code)
        .otherwise(spain_code)
    )
    df = df.withColumn(TEMP_COUNTRY_COLUMN, country_code)
    return df


def get_spanish_country_using_policy_table(
    df: DataFrame, policy_df: DataFrame, join_col="KeyIdPolis"
) -> DataFrame:
    """Replicate the Country column on Policy.Policy table to all the spanish tables
    with ES_OutsideSisNet partition.
    """
    # Drop duplicates in policy_df based on KeyIdPolis and COUNTRY_COLUMN_NAME:
    window = Window.partitionBy("KeyIdPolis").orderBy(F.col(COUNTRY_COLUMN_NAME).desc())
    policy_df = policy_df.withColumn("row_number", F.row_number().over(window))
    policy_df = policy_df.filter(F.col("row_number") == 1).drop("row_number")

    enhanced_df = add_column_from_match_table(
        df, policy_df, join_col, {COUNTRY_COLUMN_NAME: TEMP_COUNTRY_COLUMN}
    )
    return enhanced_df


class AddUSGAAPDateColumn:
    """Add the USGAAP_Date column based on InceptionDate and TransactionDate columns
    and the GAAPEndDate column from the Reference_DatesGAAP table.
    """

    def __init__(self, df: DataFrame, gaap_df: DataFrame, policy_df: DataFrame):
        self.df = df
        self.gaap_df = gaap_df.select("EndDate", "GAAPMonthYear", "GAAPEndDate")
        self.policy_df = policy_df

    def add_usgaap_date(self) -> DataFrame:
        self.policy_df = self._add_inception_date_for_max_id()
        df = self._add_usgaap_date()
        return df

    def _add_inception_date_for_max_id(self) -> DataFrame:
        """Add a temporary column with the first InceptionDate for each window.

        The window is sorted by PolicySequenceNumber in descending order. The first
        InceptionDate in each window is the InceptionDate for the max PolicySequenceNumber.
        The max PolicySequenceNumber corresponds to the last PolicyID.
        """
        partition_by = ["Country", "PolicyReference", "YearOfAccount"]
        sort_cols_by = [F.col("PolicySequenceNumber").desc()]
        window = Window.partitionBy(partition_by).orderBy(sort_cols_by)

        self.policy_df = self.policy_df.withColumn(
            "InceptionDate_for_max_id", F.first("InceptionDate").over(window)
        )
        return self.policy_df

    def _add_usgaap_date(self) -> DataFrame:
        """Adds the USGAAP_Date column to the DataFrame by performing various transformations.

        Returns:
            DataFrame: The DataFrame with the USGAAP_Date column added.
        """
        df = self.df
        original_columns = df.columns

        inception_date_col = "InceptionDate_for_max_id"

        # Join df with Policy_Policy table using KeyIdPolis:
        df = add_column_from_match_table(
            df,
            self.policy_df,
            "KeyIdPolis",
            {inception_date_col: None},
        )

        # Verify that InceptionDate and TransactionDate have the same data type:
        assert (
            df.schema[inception_date_col].dataType
            == df.schema["TransactionDate"].dataType
        ), (
            "The InceptionDate and TransactionDate columns must have the same data type. "
            f"InceptionDate has data type {df.schema[inception_date_col].dataType} "
            f"and TransactionDate has data type {df.schema['TransactionDate'].dataType}."
        )

        # Calculate max date (greatest of InceptionDate and TransactionDate):
        max_date_col = "tmp__maxdate"
        df = df.withColumn(
            max_date_col,
            F.greatest(F.col(inception_date_col), F.col("TransactionDate")),
        )

        # Calculate MonthYear from maxdate:
        month_year_col = "tmp__maxdate_MonthYear"
        df = df.withColumn(
            month_year_col, F.date_format(F.col(max_date_col), "MMM-yyyy")
        )

        # Join df and gaap_df using month_year_col and GAAPMonthYear:
        df = df.join(
            self.gaap_df,
            on=[df[month_year_col] == self.gaap_df["GAAPMonthYear"]],
            how="left",
        )

        # Calculate _maxdate_StartOfNextMonth:
        next_month_col = "tmp__maxdate_StartOfNextMonth"
        df = df.withColumn(
            next_month_col,
            F.add_months(F.trunc(F.col(max_date_col), "month"), 1),
        )

        # Calculate the USGAAP_Date column:
        df = df.withColumn(
            "USGAAP_Date",
            F.when(
                F.col(max_date_col) > F.col("GAAPEndDate"),
                F.col(next_month_col),
            ).otherwise(F.col(max_date_col)),
        )

        df = df.select(original_columns + ["USGAAP_Date"])
        return df
