Columns:
  Country:
    dataType: string
  ClaimsID:
    dataType: string
  KeyInternSchadenummer:
    dataType: string
  ClaimReference:
    dataType: string
  ClaimHandler:
    dataType: string
  ClaimYearOfAccount:
    dataType: int
  ClaimInsured:
    dataType: string
  ClaimOpenDate:
    dataType: date
  ClaimReportDate:
    dataType: date
  CloseDate:
    dataType: date
  DateOfLoss:
    dataType: date
  ClaimCauseCode:
    dataType: int
  Product:
    dataType: string
  ReportingClass:
    dataType: string
  KeyReserving:
    dataType: string
  Class:
    dataType: string
  AS_AT_DATE:
    dataType: date
  ReportStatus:
    dataType: string
  MaximumPotentialLoss:
    dataType: double
  MaximumPotentialLossPercentage:
    dataType: double
  TriageCode:
    dataType: string
  LitigationCode:
    dataType: string
  ClaimLeadIndicator:
    dataType: string
  Paid:
    dataType: double
  Outstanding:
    dataType: double
  Incurred:
    dataType: double
  AS_AT_DATE_PrvsMth:
    dataType: date
  ReportStatus_PrvsMth:
    dataType: string
  Paid_PrvsMth:
    dataType: double
  Outstanding_PrvsMth:
    dataType: double
  Incurred_PrvsMth:
    dataType: double
  AS_AT_DATE_PrvsQtr:
    dataType: date
  ReportStatus_PrvsQtr:
    dataType: string
  Paid_PrvsQtr:
    dataType: double
  Outstanding_PrvsQtr:
    dataType: double
  Incurred_PrvsQtr:
    dataType: double
  ReportMonth:
    dataType: string
  Mth:
    dataType: string
  Qtr:
    dataType: string
  ReportQuarter:
    dataType: string
  QuarterStart:
    dataType: date
  MvmtMthPaid:
    dataType: double
  MvmtQtrPaid:
    dataType: double
  MvmtMthOutstanding:
    dataType: double
  MvmtQtrOutstanding:
    dataType: double
  MvmtMthIncurred:
    dataType: double
  MvmtQtrIncurred:
    dataType: double
  BgnMthOpenClaims:
    dataType: double
  BgnQtrOpenClaims:
    dataType: double
  NewMthClaims:
    dataType: double
  NewQtrClaims:
    dataType: double
  NewMthBdxClaims:
    dataType: double
  NewQtrBdxClaims:
    dataType: double
  ReopenMthClaims:
    dataType: double
  ReopenQtrClaims:
    dataType: double
  ClosedMthClaims:
    dataType: double
  ClosedQtrClaims:
    dataType: double
  EffectiveFromDate:
    dataType: date
  EURPERUSD:
    dataType: double
  CountryID:
    dataType: string
  _rescued_data:
    dataType: string
