SELECT DISTINCT
    B.POLICODE AS 'KeyIdPolis',
    C<PERSON>SECTREFE AS 'KeyDekkingsNummer',
    A<PERSON>LIMIT    AS 'Limit',
        CASE
        WHEN A.LIMBASCO = 'IMMAXIND' THEN
            'AGG'
        WHEN A.LIMBASCO = 'IMPOASEG' THEN
            'AOC'
        WHEN A.LIMBASCO = 'IMTOCLAS' THEN
            'AGG'
        WHEN A.LIMBASCO = 'LIMIANIO' THEN
            'AGG'
        WHEN A.LIMBASCO = 'LIMIPOLI' THEN
            'AGG'
        WHEN A.LIMBASCO = 'LIMISINI' THEN
            'AOC'
    END AS 'LimitBasisCode',
    A<PERSON>IMBA<PERSON> AS 'LimitBasisDescription',
    A<PERSON> AS 'LimitCurrencyCode',
    A.TOPLIMIT AS 'TopLimit'
FROM
    NTJDWHMRK..MEDFPOPL A,
    NTJDWHMRK..MEDFPOLI B,
    NTJDWHMRK..MEDFPOSE C
WHERE
    B.ID_MEDFPOLI = C.ID_MEDFPOLI_FK
    AND C.ID_MEDFPOSE = A.ID_MEDFPOSE_FK
    AND C.ID_MEDFPOSE IN (
        SELECT MAX(X.ID_MEDFPOSE)
        FROM
            NTJDWHMRK..MEDFPOLI Z,
            NTJDWHMRK..MEDFPOSE X
        WHERE
            Z.ID_MEDFPOLI = X.ID_MEDFPOLI_FK
            AND Z.FECHALTA >= CONVERT(datetime,'01/01/2000',103)
            AND Z.POLICODE = B.POLICODE
            AND C.SECTREFE = X.SECTREFE
    )
