from unittest.mock import MagicMock

from mines2.core.extensions.misc import assert_dataframe_equality

from models_scripts.transformations.common.misc import (
    COUNTRY_COLUMN_NAME,
    TEMP_COUNTRY_COLUMN,
)
from models_scripts.transformations.edf.common.add_columns import (
    AddUSGAAPDateColumn,
    add_key_reserving_and_german_country_col,
    get_spanish_country_using_policy_table,
)


def test_add_key_reserving_and_german_country_col(spark):
    """Test add_key_reserving_and_german_country_col function."""
    main_df = spark.createDataFrame(
        [
            ("123", "abc"),
            ("456", "def"),
            ("789", "ghi"),
        ],
        ["KeyIdPolis", "SectionProductCode"],
    )
    policy_df = spark.createDataFrame(
        [
            ("DE", "123", "A"),
            ("FR", "456", "B"),
            ("DE", "789", "C"),
        ],
        ["Country", "KeyIdPolis", "PolicyProductCode"],
    )
    expected_df = spark.createDataFrame(
        [
            ("123", "abc", "DE", "DE:A:abc"),
            ("456", "def", "FR", "DE:B:def"),
            ("789", "ghi", "DE", "DE:C:ghi"),
        ],
        ["KeyIdPolis", "SectionProductCode", TEMP_COUNTRY_COLUMN, "KeyReserving"],
    )
    output_df = add_key_reserving_and_german_country_col(main_df, policy_df)

    assert_dataframe_equality(output_df, expected_df)


def test_get_spanish_country_using_policy_table(spark):
    main_df = spark.createDataFrame(
        [
            ("123", "abc"),
            ("456", "def"),
            ("789", "ghi"),
        ],
        ["KeyIdPolis", "SectionProductCode"],
    )
    policy_df = spark.createDataFrame(
        [
            ("123", "ES"),
            ("456", "PT"),
            ("789", None),
            ("789", "ES"),
        ],
        ["KeyIdPolis", COUNTRY_COLUMN_NAME],
    )
    expected_df = spark.createDataFrame(
        [
            ("123", "abc", "ES"),
            ("456", "def", "PT"),
            ("789", "ghi", "ES"),
        ],
        ["KeyIdPolis", "SectionProductCode", TEMP_COUNTRY_COLUMN],
    )
    output_df = get_spanish_country_using_policy_table(main_df, policy_df)
    assert_dataframe_equality(output_df, expected_df)


class TestAddUSGAAPDateColumn:
    def test__add_usgaap_date_method(self, spark):
        """Test the add_usgaap_date function."""
        main_df = spark.createDataFrame(
            [
                ("1", "2019-01-01", ""),
                ("2", "2020-03-02", ""),
                ("3", "2023-10-03", ""),
                ("4", "2024-12-04", ""),
                ("5", None, None),
                ("6", "2025-09-01", ""),
                ("7", "2022-08-03", ""),
                ("7", "2022-08-03", ""),
                ("8", "2022-01-16", ""),
                ("9", None, None),
            ],
            ["KeyIdPolis", "TransactionDate", "extra_column"],
        )
        policy_df = spark.createDataFrame(
            [
                ("1", "2019-01-02"),
                ("2", "2020-03-01"),
                ("3", "2023-10-04"),
                ("4", "2024-12-01"),
                ("5", "2025-07-01"),
                ("7", "2026-02-23"),
                ("8", "2020-01-01"),
            ],
            ["KeyIdPolis", "InceptionDate_for_max_id"],
        )
        gaap_df = spark.createDataFrame(
            [
                ("01-Jan-19", "Jan-2019", "2019-01-01", ""),
                ("03-Mar-20", "Mar-2020", "2020-03-03", ""),
                ("03-Oct-23", "Oct-2023", "2023-10-03", ""),
                ("05-Dec-24", "Dec-2024", "2024-12-05", ""),
                ("15-Jul-25", "Jul-2025", "2025-07-15", ""),
                ("15-Jan-22", "Jan-2022", "2022-01-15", ""),
            ],
            ["EndDate", "GAAPMonthYear", "GAAPEndDate", "extra_column"],
        )
        df_expected = spark.createDataFrame(
            [
                ("1", "2019-01-01", "", "2019-02-01"),
                ("2", "2020-03-02", "", "2020-03-02"),
                ("3", "2023-10-03", "", "2023-11-01"),
                ("4", "2024-12-04", "", "2024-12-04"),
                ("5", None, None, "2025-07-01"),
                ("6", "2025-09-01", "", "2025-09-01"),
                ("7", "2022-08-03", "", "2026-02-23"),
                ("7", "2022-08-03", "", "2026-02-23"),
                ("8", "2022-01-16", "", "2022-02-01"),
                ("9", None, None, None),
            ],
            ["KeyIdPolis", "TransactionDate", "extra_column", "USGAAP_Date"],
        )
        class_obj = AddUSGAAPDateColumn(main_df, gaap_df, policy_df)
        df_output = class_obj._add_usgaap_date()
        assert_dataframe_equality(df_output, df_expected)

    def test__add_inception_date_for_max_id_method(self, spark):
        policy_df = spark.createDataFrame(
            [
                ("DE", "123", 2021, 1, "2021-08-01"),
                ("DE", "123", 2021, 2, "2021-02-01"),
                ("ES", "456", 2022, 1, "2022-01-01"),
                ("ES", "456", 2022, 2, "2022-02-01"),
                ("ES", "456", 2023, 3, "2023-02-01"),
                ("NL", "789", 2020, 1, "2020-01-01"),
                ("NL", "789", 2024, 2, "2024-02-01"),
            ],
            [
                "Country",
                "PolicyReference",
                "YearOfAccount",
                "PolicySequenceNumber",
                "InceptionDate",
            ],
        )
        expected_df = spark.createDataFrame(
            [
                ("DE", "123", 2021, 1, "2021-08-01", "2021-02-01"),
                ("DE", "123", 2021, 2, "2021-02-01", "2021-02-01"),
                ("ES", "456", 2022, 1, "2022-01-01", "2022-02-01"),
                ("ES", "456", 2022, 2, "2022-02-01", "2022-02-01"),
                ("ES", "456", 2023, 3, "2023-02-01", "2023-02-01"),
                ("NL", "789", 2020, 1, "2020-01-01", "2020-01-01"),
                ("NL", "789", 2024, 2, "2024-02-01", "2024-02-01"),
            ],
            [
                "Country",
                "PolicyReference",
                "YearOfAccount",
                "PolicySequenceNumber",
                "InceptionDate",
                "InceptionDate_for_max_id",
            ],
        )
        class_obj = AddUSGAAPDateColumn(MagicMock(), MagicMock(), policy_df)
        output_df = class_obj._add_inception_date_for_max_id()
        assert_dataframe_equality(output_df, expected_df)
