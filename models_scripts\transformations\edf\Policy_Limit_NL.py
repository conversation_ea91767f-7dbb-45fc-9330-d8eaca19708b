from pyspark.sql import DataFrame

from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.backward_populate_records import (
    add_missing_ids,
)
from models_scripts.transformations.edf.common.currency import (
    PolicyLimitCurrencyConversor,
    conform_euro_currency,
)
from models_scripts.transformations.edf.common.handle_limit_agg_aoc import (
    calculate_limits,
    handle_null_limits_spark,
)


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["Limit_main"]
    extra_records_df = df_dict["Support_NewTransactionComponents"]
    exchange_rate_df = df_dict["Limit_exchange_rate"]

    main_df = conform_euro_currency(main_df, "LimitCurrencyCode")
    main_df = calculate_limits(main_df)
    output_df = handle_null_limits_spark(main_df)

    output_df = add_missing_ids(output_df, extra_records_df)
    # Handle null values in currency name after add_missing_ids function:
    output_df = output_df.fillna({"LimitCurrencyCode": "EUR"})

    output_df = PolicyLimitCurrencyConversor.add_currency_columns(
        output_df, exchange_rate_df
    )

    return output_df
