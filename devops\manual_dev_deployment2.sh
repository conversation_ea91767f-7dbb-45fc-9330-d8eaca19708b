#!/bin/bash

set -e -o pipefail

for f in $(ls dist/); do if [[ "$f" == *.whl ]]; then rm dist/"$f"; fi; done
for f in $(ls dist/); do if [[ "$f" == *.tar.gz ]]; then rm dist/"$f"; fi; done

poetry build

# databricks configure -token

BranchName=$(git symbolic-ref --short HEAD)
ModelName="EDF"
python -m devops.generate_model_json
echo "Updating files of ${BranchName} on datalake"
if az storage fs directory exists -f models -n $BranchName --account-name minteuwestaideveudl01 --account-key $ACCESS_KEY| jq -r '.exists' | grep -q 'true'; then
    echo "Deleteing branch folder"
    az storage fs directory delete -f models -n $BranchName --account-name minteuwestaideveudl01 --account-key $ACCESS_KEY --yes
fi
echo "Uploading models folder"
az storage fs directory upload -f models --account-name minteuwestaideveudl01 -s "models/*" -d $BranchName --account-key $ACCESS_KEY --recursive

if [ -d "queries_sql" ] && [ "$(ls -A queries_sql)" ]; then
    echo "Uploading sql queries folder"
    az storage fs directory upload -f models --account-name minteuwestaideveudl01 -s "queries_sql" -d $BranchName --recursive --account-key $ACCESS_KEY
else
    echo "Sql queries folder not present in local project"
fi
echo "Uploading .whl files"
az storage fs directory upload -f models --account-name minteuwestaideveudl01 -s "dist/*" -d $BranchName/package/$ModelName --recursive --account-key $ACCESS_KEY --recursive

echo "Uploading run_mines2.py"
az storage fs file upload -f models --account-name minteuwestaideveudl01 -s "run_mines2.py" -p $BranchName/package/$ModelName/run_mines2.py --account-key $ACCESS_KEY --overwrite

cd edf_databricks
databricks bundle deploy --target dev