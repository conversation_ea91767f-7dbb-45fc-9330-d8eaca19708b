Country: ES
existCustomTransformation: 'True'

dataSource:
- name: main
  type: SourceSisnetES
  parameters:
    sqlFileName: Excess.sql
    querySourceType: SQL_FILE
    selectColumnsFromSchema: False

- name: exchange_rate
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: test_mintdatalake_02
      uat: test_mintdatalake_02
      prod: prod_mintdatalake_02
    schema: rdm_dbo
    table: mint_mapping_roe
    sourceSystem: MINT_RAW_DATALAKE

ColumnSpecs:
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  PolicySectionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  KeyIdPolis:
    locale: en_US.utf8
  KeyDekkingsNummer:
    locale: en_US.utf8
  Excess:
    locale: en_US.utf8
  ExcessBasisCode:
    locale: en_US.utf8
  ExcessBasisDescription:
    locale: en_US.utf8
  ExcessCurrencyCode:
    locale: en_US.utf8
  ExcessRounded:
    NotInSource: True
  ExcessGBP:
    NotInSource: True
  ExcessEUR:
    NotInSource: True
  ExcessUSD:
    NotInSource: True
  ExcessCAD:
    NotInSource: True
  ExcessRoundedGBP:
    NotInSource: True
  ExcessRoundedEUR:
    NotInSource: True
  ExcessRoundedUSD:
    NotInSource: True
  ExcessRoundedCAD:
    NotInSource: True
