Columns:
  SettledSectionTransactionComponentID:
    dataType: int
    calculatedColumn: True
  SettledTransComponentID:
    dataType: string
  SettledSectionTransactionID:
    dataType: int
    calculatedColumn: True
  SettledTransactionID:
    dataType: string
  PolicyID:
    dataType: string
  KeyIdPolis:
    dataType: string
  KeyDekkingsNummer:
    dataType: string
  KeyFactuurnummer:
    dataType: string
  TransactionComponentAdditionsDeductionsIndicator:
    dataType: string
  TransactionComponentAmount:
    dataType: double
    restrictNull: True
  TransactionComponentAmountGBP:
    dataType: double
    restrictNull: True
  TransactionComponentAmountEUR:
    dataType: double
    restrictNull: True
  TransactionComponentAmountUSD:
    dataType: double
    restrictNull: True
  TransactionComponentAmountCAD:
    dataType: double
    restrictNull: True
  TransactionComponentAmountRounded:
    dataType: currency
    restrictNull: True
  TransactionComponentAmountRoundedGBP:
    dataType: currency
    restrictNull: True
  TransactionComponentAmountRoundedEUR:
    dataType: currency
    restrictNull: True
  TransactionComponentAmountRoundedUSD:
    dataType: currency
    restrictNull: True
  TransactionComponentAmountRoundedCAD:
    dataType: currency
    restrictNull: True
  TransactionComponentTypeCode:
    dataType: string
  TransactionComponentTypeDescription:
    dataType: string
  TransactionComponentPercentage:
    dataType: float
  TransactionComponentTerritory:
    dataType: string
primaryKey:
  - SettledSectionTransactionComponentID
