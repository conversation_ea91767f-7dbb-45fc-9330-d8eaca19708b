from pyspark.sql import DataFrame

from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.currency import ClaimCurrencyConversor
from models_scripts.transformations.edf.common.misc import (
    unify_keyDekkingsNummer_with_claim_section,
)


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["TransactionComponent_main"]
    transaction_df = df_dict["Claim_Transaction"]
    exchange_rate_df = df_dict["TransactionComponent_exchange_rate"]
    section_df = df_dict["Claim_Section"]

    unified_KeyDekkingsNummer_df = unify_keyDekkingsNummer_with_claim_section(
        main_df, section_df, logger=kwargs["logger"]
    )

    result_df = ClaimCurrencyConversor.add_new_columns(
        unified_KeyDekkingsNummer_df, transaction_df, exchange_rate_df
    )

    return result_df
