from mines2.singlepartition.transform_functions import concat
from pyspark.sql import DataFrame

from models_scripts.transformations.common.add_columns import (
    add_column_from_match_table,
)
from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.add_columns import (
    add_key_reserving_and_german_country_col,
)
from models_scripts.transformations.edf.common.currency import (
    PolicySectionCurrencyConversor,
)


def add_currency_columns(
    df: DataFrame, exchange_rate_df: DataFrame, limit_df: DataFrame
) -> DataFrame:
    """Adds currency columns to the DataFrame using the exchange rate DataFrame."""
    # Create the PolicySectionID calculated column:
    columns = [
        {"source": "CONSTANT", "value": "DE"},
        {"source": "COLUMN", "name": "KeyIdPolis"},
        {"source": "COLUMN", "name": "KeyDekkingsNummer"},
    ]
    df = df.withColumn("PolicySectionID", concat(columns, ":"))

    df_with_currency_code = add_column_from_match_table(
        df,
        limit_df,
        "PolicySectionID",
        {"LimitCurrencyCode": None},
    )
    output_df = PolicySectionCurrencyConversor.add_currency_columns(
        df_with_currency_code, exchange_rate_df
    )
    output_df = output_df.drop("LimitCurrencyCode", "PolicySectionID")
    return output_df


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["Section_main"]
    policy_df = df_dict["Policy_Policy"]
    exchange_rate_df = df_dict["Section_exchange_rate"]
    limit_df = df_dict["Policy_Limit"]

    output_df = add_key_reserving_and_german_country_col(main_df, policy_df)
    output_df = add_currency_columns(output_df, exchange_rate_df, limit_df)

    return output_df
