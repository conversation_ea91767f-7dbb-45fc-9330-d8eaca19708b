import pyspark.sql.functions as F
from pyspark.sql import DataFrame, Window

from models_scripts.transformations.common.misc import (
    enforce_nulls_type,
    pandas_to_spark,
    spark_to_pandas,
)
from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.add_columns import (
    add_business_classification,
)
from models_scripts.transformations.edf.common.currency import (
    PolicyPolicyCurrencyConversor,
)
from models_scripts.transformations.edf.common.fix_renewal_values import (
    fix_renewal_values_es,
    manual_fix_renewal_values_es,
)
from models_scripts.transformations.edf.common.re_underwriting import (
    add_re_underwriting_col,
)
from models_scripts.transformations.edf.common.sisnet_scs_migration import (
    exclude_matched_policies_in_scs_and_sisnet,
)


def add_policy_sequence(df: DataFrame) -> DataFrame:
    """Add PolicySequenceNumber column to the dataframe"""
    window = Window.partitionBy("PolicyReference").orderBy("KeyIdPolis")
    df = df.withColumn("PolicySequenceNumber", F.row_number().over(window))
    return df


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    """Add new Policies from mapping SCS SISNET table and other transformations."""
    scs_policy_df = df_dict["Policy_main"]
    re_underwriting_pdf = spark_to_pandas(df_dict["Reference_ReUnderwriting"])
    exchange_rate_df = df_dict["Policy_exchange_rate"]
    mapping_df = df_dict["scs_sisnet_compare"]

    scs_policy_df = exclude_matched_policies_in_scs_and_sisnet(
        scs_policy_df, mapping_df
    )

    output_pdf = add_re_underwriting_col(
        spark_to_pandas(scs_policy_df), re_underwriting_pdf
    )

    new_renewal_df = df_dict["Policy_new_renewal"]
    main_df = pandas_to_spark(output_pdf)

    main_df = fix_renewal_values_es(main_df)
    main_df = manual_fix_renewal_values_es(main_df, new_renewal_df)
    main_df = add_business_classification(main_df)
    main_df = add_policy_sequence(main_df)
    main_df = enforce_nulls_type(main_df)
    main_df = PolicyPolicyCurrencyConversor.add_currency_columns(
        main_df, exchange_rate_df
    )

    return main_df
