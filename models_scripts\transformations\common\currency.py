"""
Currency conversion base class. The logic of this function is to get from the ataccama database the exchange rate
It will always select the latest closing rate for each currency and then pivot the exchange rate dataframe.
The conversion will create new columns for each of the currencies defined in the CURRENCIES_TO_CONVERT list.
will also create new columns with the rounded values of the original amount and the converted amounts.

"""
import pyspark.sql.functions as F
from pyspark.sql import Column, DataFrame, Window

from models_scripts.transformations.common.traits import transformation


class CurrencyConversorBase:
    CURRENCIES_TO_CONVERT = ["GBP", "EUR", "USD", "CAD"]
    ROUNDED_SUFFIX = "Rounded"

    # ExchangeRate columns
    FROM_CURRENCY_COL = "FromCcy"
    TO_CURRENCY_COL = "ToCcy"
    RATE_TYPE_COL = "RateType"
    FXRATE_COL = "FXRate"
    RATE_DATE_COL = "RateDate"

    def __init__(self, amount_col):
        assert amount_col, "amount_col must be a valid column name."
        self.AMOUNT_COL = amount_col

    @classmethod
    @transformation
    def add_new_columns_using_single_currency(
        cls,
        transaction_component_df: DataFrame,
        exchange_rate_df: DataFrame,
        currency: str,
        amount_col: str,
    ) -> DataFrame:
        """Add new columns to the dataframe with the exchange rate applied.

        Consider that all the amount has the same pre-defined currency.
        """
        conversor = cls(amount_col)

        valid_exchange_rate_df = conversor._get_valid_exchange_rate(exchange_rate_df)

        transaction_components_with_converted_currencies_df = (
            conversor._add_converted_column_for_single_currency(
                transaction_component_df, valid_exchange_rate_df, currency
            )
        )
        return transaction_components_with_converted_currencies_df

    @classmethod
    def add_new_columns_using_currency_column_from_list_input(
        cls,
        df: DataFrame,
        exchange_rate_df: DataFrame,
        currency_amount_cols: list[tuple[str, str]],
    ) -> DataFrame:
        """Add the converted currency columns to the dataframe.

        Consider that they can be different currencies, and that the column with the
        currency is defined in the currency_col parameter.
        """
        for currency_col, amount_col in currency_amount_cols:
            df = cls.add_new_columns_using_currency_column(
                df, exchange_rate_df, currency_col, amount_col
            )
        return df

    @classmethod
    @transformation
    def add_new_columns_using_currency_column(
        cls,
        transaction_component_df: DataFrame,
        exchange_rate_df: DataFrame,
        currency_col: str,
        amount_col: str,
    ) -> DataFrame:
        """Add the converted currency columns to the dataframe.

        Consider that they can be different currencies, and that the column with the
        currency is defined in the currency_col parameter.
        """

        original_columns = transaction_component_df.columns
        required_columns = [currency_col, amount_col]
        assert set(required_columns) <= set(
            original_columns
        ), f"Columns {required_columns} not found in the dataframe."

        conversor = cls(amount_col)

        valid_exchange_rate_df = conversor._get_valid_exchange_rate(exchange_rate_df)
        joined_df = conversor.join_with_exchange_rate(
            transaction_component_df, valid_exchange_rate_df, currency_col
        )

        select_query = conversor._create_new_column_select_query()
        output_df = joined_df.select(*original_columns, *select_query)
        return output_df

    def join_with_exchange_rate(
        self, df: DataFrame, pivoted_exchange_rate_df: DataFrame, currency_col: str
    ) -> DataFrame:
        """Join the dataframe with the exchange rate dataframe."""
        original_columns = df.columns
        pivot_exchange_rate_selected_df = pivoted_exchange_rate_df.select(
            self.FROM_CURRENCY_COL, *self.CURRENCIES_TO_CONVERT
        )

        transactions_exchange_rates_df = df.join(
            pivot_exchange_rate_selected_df,
            df[currency_col] == pivoted_exchange_rate_df[self.FROM_CURRENCY_COL],
            "left",
        )

        return transactions_exchange_rates_df.select(
            *original_columns, *self.CURRENCIES_TO_CONVERT
        )

    def _get_valid_exchange_rate(self, exchange_rate_df: DataFrame) -> DataFrame:
        """get the exchange_rates."""
        latest_closing_rate_df = self._get_latest_closing_rate(exchange_rate_df)
        pivoted_exchange_rate_df = self._get_pivoted_exchange_rate(
            latest_closing_rate_df
        )
        return pivoted_exchange_rate_df

    def _get_latest_closing_rate(self, exchange_rate_df: DataFrame) -> DataFrame:
        """Get the latest closing rate for each currency."""
        exchange_rate_selected_df = exchange_rate_df.select(
            self.RATE_TYPE_COL,
            self.FROM_CURRENCY_COL,
            self.TO_CURRENCY_COL,
            self.FXRATE_COL,
            self.RATE_DATE_COL,
        )

        closing_rate_df = exchange_rate_selected_df.filter(
            F.col(self.RATE_TYPE_COL) == "Closing"
        )
        latest_closing_rate_df = closing_rate_df.withColumn(
            "date_reverse_order",
            F.row_number().over(
                Window.partitionBy(
                    self.FROM_CURRENCY_COL, self.TO_CURRENCY_COL
                ).orderBy(F.col(self.RATE_DATE_COL).desc())
            ),
        ).filter(F.col("date_reverse_order") == 1)
        return latest_closing_rate_df.select(
            self.FROM_CURRENCY_COL, self.TO_CURRENCY_COL, self.FXRATE_COL
        )

    def _get_pivoted_exchange_rate(self, exchange_rate_df: DataFrame) -> DataFrame:
        """Pivot the exchange rate dataframe."""
        pivoted_exchange_rate_df = (
            exchange_rate_df.groupBy(self.FROM_CURRENCY_COL)
            .pivot(self.TO_CURRENCY_COL, self.CURRENCIES_TO_CONVERT)
            .agg(F.max(self.FXRATE_COL).alias(self.FXRATE_COL))
        )
        return pivoted_exchange_rate_df.select(
            self.FROM_CURRENCY_COL, *self.CURRENCIES_TO_CONVERT
        )

    def _add_converted_column_for_single_currency(
        self,
        transaction_component_df: DataFrame,
        pivoted_exchange_rate_df: DataFrame,
        currency: str,
    ) -> DataFrame:
        """Add the converted currency columns to the dataframe. Consider that all the amount has
        the same pre-defined currency."""
        original_columns = transaction_component_df.columns
        assert (
            self.AMOUNT_COL in original_columns
        ), f"Column {self.AMOUNT_COL} not found in the dataframe."

        pivot_exchange_rate_selected_df = pivoted_exchange_rate_df.select(
            self.FROM_CURRENCY_COL, *self.CURRENCIES_TO_CONVERT
        )

        filtered_exchange_rate_df = pivot_exchange_rate_selected_df.filter(
            F.col(self.FROM_CURRENCY_COL) == currency
        )

        joined_df = transaction_component_df.crossJoin(filtered_exchange_rate_df)

        select_query = self._create_new_column_select_query()
        output_df = joined_df.select(*original_columns, *select_query)
        return output_df

    def _create_new_column_select_query(self) -> list[Column]:
        """Create the select query for the new columns. Will create the columns for the rounded values and the
        converted values."""
        rounded_component_amount = f"{self.AMOUNT_COL}{self.ROUNDED_SUFFIX}"
        new_columns_mapped = {
            rounded_component_amount: F.round(F.col(self.AMOUNT_COL), 2)
        }
        for currency in self.CURRENCIES_TO_CONVERT:
            converted_value_col = F.col(self.AMOUNT_COL) / F.col(currency)
            new_columns_mapped[f"{self.AMOUNT_COL}{currency}"] = converted_value_col
            new_columns_mapped[f"{rounded_component_amount}{currency}"] = F.round(
                converted_value_col, 2
            )

        new_columns_query = [
            value.alias(name) for name, value in new_columns_mapped.items()
        ]
        return new_columns_query
