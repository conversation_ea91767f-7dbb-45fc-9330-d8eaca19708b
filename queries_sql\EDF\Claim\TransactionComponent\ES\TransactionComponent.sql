SELECT DISTINCT
    'SISnet' AS Source,
    A<PERSON>LAIREFE AS 'KeyInternSchadenummer',
    B<PERSON><PERSON>OLICODE AS 'KeyIdPolis',
    C<PERSON>SECTREFE AS 'KeyDekkingsNummer',
    E<PERSON>ID_MEDFSITR_FK AS 'KeySchadeBoekingsNummer',
    <PERSON><PERSON><PERSON> AS 'TransactionComponentAmount',
    <PERSON><PERSON> AS 'TransactionComponentTypeCode',
    E<PERSON>TRCOTYDE AS 'TransactionComponentTypeDescription'
FROM
    NTJDWHMRK..MEDFSINI A,
    NTJDWHMRK..MEDFSIPO B,
    NTJDWHMRK..MEDFSISE C,
    NTJDWHMRK..MEDFSITR D,
    NTJDWHMRK..MEDFSITC E
WHERE
    A.ID_MEDFSINI = B.ID_MEDFSINI_FK
    AND B.ID_MEDFSIPO = C.ID_MEDFSIPO_FK
    AND C.ID_MEDFSISE = D.ID_MEDFSISE_FK
    AND D.ID_MEDFSITR = E.ID_MEDFSITR_FK
    AND D.ID_MEDFSITR IN (
        SELECT MAX(Z.ID_MEDFSITR)
        FROM NTJDWHMRK..MEDFSITR Z
        WHERE Z.TRATYPCO = D.TRATYPCO AND
              Z.TRANREFE = D.TRANREFE
    )
    AND A.CLAMODDA >= CONVERT(datetime,'01/01/2000',103)
