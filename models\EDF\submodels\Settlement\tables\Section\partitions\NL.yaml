Country: NL
existCustomTransformation: 'True'
dataSource:
- name: main
  type: SourceProgressNL
  parameters:
    sqlFileName: Query SettlementArraySection.sql
    querySourceType: SQL_FILE
ColumnSpecs:
  KeyIdPolis:
    locale: en_US.utf8
  KeyDekkingsNummer:
    locale: en_US.utf8
    sourceName: KeyDekkingsnummer
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  PolicySectionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  SectionProductCode:
    locale: en_US.utf8
  SectionReference:
    locale: en_US.utf8
  KeyReserving:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: SectionProductCode
        sep: ':'
