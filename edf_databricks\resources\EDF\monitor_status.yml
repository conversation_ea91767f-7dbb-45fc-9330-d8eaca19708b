resources:
  jobs:
    monitor_status_job_EDF:
      name: monitor_status_job [EDF]

      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 1200  # 20 minutes
      timeout_seconds: 2400  # 40 minutes
      max_concurrent_runs: 2

      tasks:
        - task_key: update_status
          notebook_task:
            notebook_path: ../../src/generic_runner_caller/generic_runner_caller.ipynb
            base_parameters:
              module_name: monitor.runner
              root_path: ${var.mines_root_path}
              args: '["EDF", "{{task.run_id}}"]'
              model_name: EDF
          job_cluster_key: generic_job_cluster
          max_retries: 1
          min_retry_interval_millis: 60000 # 1 minute
          libraries:
            - pypi:
                package: mines2

      job_clusters:
        - job_cluster_key: generic_job_cluster
          new_cluster:
          # Order of parameters determined by the order in the databricks API documentation
            cluster_name: ""
            spark_version: ${var.spark_version}
            spark_conf:
              spark.master: local[*, 4]
              spark.databricks.cluster.profile: singleNode
              spark.databricks.delta.preview.enabled: "true"
              spark.databricks.delta.schema.autoMerge.enabled: "true"
              spark.databricks.delta.rowLevelConcurrencyPreview: "true"
            azure_attributes:
              first_on_demand: 1
              availability: ON_DEMAND_AZURE
              spot_bid_max_price: -1
            node_type_id: Standard_E4d_v4
            driver_node_type_id: Standard_E4d_v4
            custom_tags:
              Project: ${bundle.target}_${bundle.name}
              ResourceClass: SingleNode
            cluster_log_conf:
              dbfs:
                destination: dbfs:/cluster-logs     
            init_scripts:
              - workspace:
                  destination: ${var.jfrog_pip_artifacts_path}       
            autoscale: {}
            spark_env_vars:
              PYSPARK_PYTHON: /databricks/python3/bin/python3
              MINES_ENVIRONMENT: ${var.environment_code}
              ARTIFACTORY_USER: "{{secrets/Key-vault-secret/markel-jfrog-user}}"
              ARTIFACTORY_TOKEN: "{{secrets/Key-vault-secret/markel-jfrog-api-token}}"
            enable_elastic_disk: true
            data_security_mode: SINGLE_USER
            runtime_engine: PHOTON
