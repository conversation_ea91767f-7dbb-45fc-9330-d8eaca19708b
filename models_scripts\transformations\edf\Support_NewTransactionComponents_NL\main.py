import os
import shutil
import subprocess

import pandas as pd
import pyspark.sql.functions as F
from mines2.core.constants import SOURCE_SYSTEM
from pyspark.sql import DataFrame

from models_scripts.transformations.common.misc import (
    conform_dbfs_path_for_python,
    pandas_to_spark,
    spark_to_pandas,
)
from models_scripts.transformations.common.traits import business_logic


def clean_white_spaces(_df: pd.DataFrame) -> pd.DataFrame:
    for column in _df.select_dtypes(["O"]).columns:
        _df[column] = _df[column].str.strip()
    return _df


def get_missing_records(
    main_pdf: pd.DataFrame, historic_pdf: pd.DataFrame
) -> pd.DataFrame:
    """The objective of this transformation is to add the ids that were missing on
    original EDF message.
    """
    aux_pdf = pd.concat([main_pdf, historic_pdf]).astype(str)

    aux_pdf.to_csv("input.csv", sep=",", encoding="utf-8", index=False)
    print(f"input.csv: {aux_pdf.shape}")
    del aux_pdf

    folder_path = os.path.abspath(os.path.dirname(__file__))
    shutil.copy(os.path.join(folder_path, "0_run_calculation.R"), ".")
    shutil.copy(os.path.join(folder_path, "1_Add-Fields_EDF.R"), ".")

    try:
        result = subprocess.run(
            ["/usr/bin/Rscript", "--vanilla", "0_run_calculation.R"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            check=True,  # This will automatically raise an error for non-zero return codes
            text=True,  # Ensures that stdout and stderr are captured as strings
        )
        print(result.stdout)
    except subprocess.CalledProcessError as e:
        print(f"Error occurred while running R script: {e}")
        print(e.stderr)

    # Use output.csv to make join:
    output_r_pdf = pd.read_csv("output.csv", sep=",", encoding="utf-8", dtype=str)
    print(f"output.csv: {output_r_pdf.shape}")

    output_r_pdf = clean_white_spaces(output_r_pdf)

    col_id = "KeyIdPolis"
    col_dek = "KeyDekkingsNummer"
    col_cod = "TransactionComponentTypeCode"
    col_fac = "KeyFactuurnummer"

    for column in [col_id, col_dek, col_cod]:
        if column in output_r_pdf.columns:
            output_r_pdf[column] = output_r_pdf[column].astype(str)

    return output_r_pdf


@business_logic
def custom_transformation(
    df_dict: dict[str, DataFrame], local_path: str, **kwargs
) -> DataFrame:
    local_path = conform_dbfs_path_for_python(local_path)
    os.makedirs(local_path, exist_ok=True)  # Create the directory if it does not exist
    os.chdir(local_path)

    main_df = df_dict["NewTransactionComponents_main"]
    historic_df = df_dict["NewTransactionComponents_historic"]

    # Get SOURCE_SYSTEM value:
    source_system = main_df.select(SOURCE_SYSTEM).first()[SOURCE_SYSTEM]

    # Drop SOURCE_SYSTEM column:
    main_df = main_df.drop(SOURCE_SYSTEM)
    historic_df = historic_df.drop(SOURCE_SYSTEM)

    main_pdf = spark_to_pandas(main_df)
    historic_pdf = spark_to_pandas(historic_df)
    output_pdf = get_missing_records(main_pdf, historic_pdf)
    output_df = pandas_to_spark(output_pdf)

    # Add SOURCE_SYSTEM column:
    output_df = output_df.withColumn(SOURCE_SYSTEM, F.lit(source_system).cast("string"))

    return output_df
