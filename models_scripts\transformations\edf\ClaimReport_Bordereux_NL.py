from pyspark.sql import DataFrame

from models_scripts.transformations.common.misc import filter_by_max_value
from models_scripts.transformations.common.traits import business_logic


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["Bordereux_main"]
    output_df = filter_by_max_value(main_df, "FileNameDate_yyyyMMdd_")
    return output_df
