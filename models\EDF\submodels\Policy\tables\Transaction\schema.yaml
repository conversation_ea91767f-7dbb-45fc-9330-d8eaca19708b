Columns:
  PolicySectionID:
    dataType: string
  PolicyTransactionID:
    dataType: string
  PolicySectionTransactionID:
    dataType: int
    calculatedColumn: True
  PolicyID:
    dataType: string
  KeyIdPolis:
    dataType: string
  KeyDekkingsNummer:
    dataType: string
  KeyFactuurnummer:
    dataType: string
  OriginalCurrencyCode:
    dataType: string
    conformCurrency: True
  RateOfExchange:
    dataType: float
  SettlementCurrencyCode:
    dataType: string
    conformCurrency: True
  TransactionDate:
    dataType: date
  TransactionReference:
    dataType: string
  TransactionTypeCode:
    dataType: string
  TransactionTypeDescription:
    dataType: string
  TransactionSequenceNumber:
    dataType: float
  USGAAP_Date:
    dataType: date

primaryKey:
  - PolicySectionTransactionID
