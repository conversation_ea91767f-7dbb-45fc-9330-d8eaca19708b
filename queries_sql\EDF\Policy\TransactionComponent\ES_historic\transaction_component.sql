SELECT 
  p.<PERSON><PERSON><PERSON><PERSON><PERSON>, 
  KeyD<PERSON><PERSON>s<PERSON><PERSON>mer, 
  KeyFactuurnummer, 
  TransactionComponentAdditionsDeductionsindicator,
  round(FT.TransactionComponentAmount, 2) as TransactionComponentAmount,
  FT.TransactionComponentTypeCode,
  FT.TransactionComponentTypeDescription,
  TransactionComponentPercentage,
  TransactionComponentTerritory,
  TransactionComponentTerritoryCode
FROM (
        SELECT DISTINCT
            CONCAT(p.PO_INTERNAL_POL_NO, '/', p.PO_INTERNAL_RSN)  AS KeyIdPolis,
            KeyDekkingsNummer,
            ft_grp.KeyFactuurnummer AS KeyFactuurnummer,
            'N/A' AS TransactionComponentAdditionsDeductionsindicator,
            1 AS TransactionComponentPercentage,
            'SUPL' AS TransactionComponentTerritory,
            'ESPA' AS TransactionComponentTerritoryCode
          FROM {mintdatalake}.scs_dbo.policies p
          LEFT JOIN {mintdatalake}.scs_dbo.financial_trans ft
              ON p.PO_INTERNAL_POL_NO = ft.FT_PO_INTERNAL_POL_NO
              AND p.PO_INTERNAL_RSN = ft.FT_PO_INTERNAL_RSN
          LEFT JOIN {mintdatalake}.scs_dbo.r_cbe_cob_extensions rc
              ON p.PO_RV_COB_CODE = rc.CBE_RV_COB_CODE
          LEFT JOIN {mintdatalake}.scs_dbo.refvalues rv
              ON rv.RV_VALUE = rc.CBE_REVISED_CLASS
              AND rv.RV_RD_DOMAIN = 'RCL'
          LEFT JOIN (
            SELECT DISTINCT PO_INTERNAL_POL_NO, PO_INTERNAL_RSN, KeyDekkingsNummer FROM (
            SELECT DISTINCT
                PO_INTERNAL_POL_NO,
                PO_INTERNAL_RSN,
                CASE 
                    WHEN RV_DESC = '1. Construction Professional Risks' THEN 1
                    WHEN RV_DESC = '2. Financial Services Professional Risks' THEN 2
                    WHEN RV_DESC = '3. Miscellaneous Professional Risks' THEN 3
                    WHEN RV_DESC = '4. Professions Professional Risks' THEN 4
                    WHEN RV_DESC= '6. D and O' THEN 5
                    WHEN RV_DESC = '11. Liability' THEN 6
                    WHEN RV_DESC = 'Surety' THEN 7
                    WHEN RV_DESC = 'Personal Accident' THEN 8
                    WHEN RV_DESC = 'Private Clinics' THEN 9
                  ELSE NULL
                END AS KeyDekkingsNummer
              FROM
              (
                SELECT DISTINCT
                    P.PO_INTERNAL_POL_NO as PO_INTERNAL_POL_NO,
                    P.PO_INTERNAL_RSN as PO_INTERNAL_RSN,
                    RV_DESC
                FROM {mintdatalake}.scs_dbo.policies p
                LEFT JOIN {mintdatalake}.scs_dbo.r_cbe_cob_extensions rc
                  ON p.PO_RV_COB_CODE = rc.CBE_RV_COB_CODE
                LEFT JOIN {mintdatalake}.scs_dbo.refvalues rv
                  ON rv.RV_VALUE = rc.CBE_REVISED_CLASS
                  AND rv.RV_RD_DOMAIN = 'RCL'
                WHERE p.PO_PROD_OFFICE IN ('SPAIN', 'MADRID', 'BARCELON', 'BARCELONA', 'BCN01', 'MDR01' )
              )
              UNION 
                SELECT DISTINCT
                    ft.FT_PO_INTERNAL_POL_NO as PO_INTERNAL_POL_NO,
                    ft.FT_PO_INTERNAL_RSN as PO_INTERNAL_RSN,
                    CASE 
                        WHEN RV_DESC = '1. Construction Professional Risks' THEN 1
                        WHEN RV_DESC = '2. Financial Services Professional Risks' THEN 2
                        WHEN RV_DESC = '3. Miscellaneous Professional Risks' THEN 3
                        WHEN RV_DESC = '4. Professions Professional Risks' THEN 4
                        WHEN RV_DESC= '6. D and O' THEN 5
                        WHEN RV_DESC = '11. Liability' THEN 6
                        WHEN RV_DESC = 'Surety' THEN 7
                        WHEN RV_DESC = 'Personal Accident' THEN 8
                        WHEN RV_DESC = 'Private Clinics' THEN 9
                      ELSE NULL
                    END AS KeyDekkingsNummer
                FROM {mintdatalake}.scs_dbo.financial_trans ft
                LEFT JOIN {mintdatalake}.scs_dbo.financial_trans_sections fs
                    ON ft.FT_KEY = fs.FS_FT_KEY
                LEFT JOIN {mintdatalake}.scs_dbo.r_cbe_cob_extensions rc
                    ON fs.FS_RV_COB_CODE = rc.CBE_RV_COB_CODE
                LEFT JOIN {mintdatalake}.scs_dbo.refvalues rv
                    ON rv.RV_VALUE = rc.CBE_REVISED_CLASS
                    AND rv.RV_RD_DOMAIN = 'RCL'
                WHERE rv.RV_DESC IS NOT NULL
                AND ft.FT_PO_INTERNAL_POL_NO IN 
                  (
                    SELECT DISTINCT PO_INTERNAL_POL_NO 
                    FROM {mintdatalake}.scs_dbo.policies 
                    WHERE PO_PROD_OFFICE IN ('SPAIN', 'MADRID', 'BARCELON', 'BARCELONA', 'BCN01', 'MDR01')
                  )
            ) WHERE KeyDekkingsNummer is NOT NULL
          ) rv ON rv.PO_INTERNAL_POL_NO = p.PO_INTERNAL_POL_NO AND rv.PO_INTERNAL_RSN = p.PO_INTERNAL_RSN
          LEFT JOIN (
            SELECT DISTINCT 
              FT_PO_INTERNAL_POL_NO, 
              FT_PO_INTERNAL_RSN, 
              CONCAT_WS('/','SCS',FT_PO_INTERNAL_POL_NO,FT_PO_INTERNAL_RSN, CAST(SUM(FT_INTERNAL_ID) AS STRING))  AS KeyFactuurnummer
            FROM {mintdatalake}.scs_dbo.financial_trans
          GROUP BY FT_PO_INTERNAL_POL_NO, FT_PO_INTERNAL_RSN
          ) ft_grp ON p.PO_INTERNAL_POL_NO = ft_grp.FT_PO_INTERNAL_POL_NO AND p.PO_INTERNAL_RSN = ft_grp.FT_PO_INTERNAL_RSN
          WHERE PO_PROD_OFFICE IN ('SPAIN', 'MADRID', 'BARCELON', 'BARCELONA', 'BCN01', 'MDR01' )
) P
 INNER JOIN (
              SELECT  KeyIdPolis,
                      TransactionComponentTypeCode,
                      TransactionComponentTypeDescription, 
                      (SUM(TransactionComponentAmount))/CountkeyIdPolis AS TransactionComponentAmount
                      FROM (
                        SELECT 
                          CONCAT(FT_PO_INTERNAL_POL_NO, '/', FT_PO_INTERNAL_RSN) AS KeyIdPolis,
                          CAST(FT_GROSS_AMOUNT AS DECIMAL(16,2)) AS TransactionComponentAmount,
                          COALESCE(CountkeyIdPolis,1) AS CountkeyIdPolis,
                          CASE 
                            WHEN FT_RV_PREMIUM_TYPE_CODE = 'BKG' THEN 'COM_COMPA'
                            ELSE 'Prima'
                          END AS TransactionComponentTypeCode,
                          CASE 
                            WHEN FT_RV_PREMIUM_TYPE_CODE = 'BKG' THEN 'Comisión cobro compañía'
                            ELSE 'Prima Neta'
                          END AS TransactionComponentTypeDescription
                      FROM {mintdatalake}.scs_dbo.financial_trans ft
                      LEFT JOIN (
                        SELECT 
                          keyIdPolis, 
                          COUNT(*) AS CountkeyIdPolis
                        FROM
                        (
                          SELECT 
                            DISTINCT CONCAT(FT_PO_INTERNAL_POL_NO, '/', FT_PO_INTERNAL_RSN) as keyIdPolis,
                            RV_DESC
                          FROM {mintdatalake}.scs_dbo.policies p
                          LEFT JOIN {mintdatalake}.scs_dbo.financial_trans ft
                              ON p.PO_INTERNAL_POL_NO = ft.FT_PO_INTERNAL_POL_NO
                              AND p.PO_INTERNAL_RSN = ft.FT_PO_INTERNAL_RSN
                          LEFT JOIN {mintdatalake}.scs_dbo.financial_trans_sections fs
                              ON ft.FT_KEY = fs.FS_FT_KEY
                          LEFT JOIN {mintdatalake}.scs_dbo.r_cbe_cob_extensions rc
                              ON fs.FS_RV_COB_CODE = rc.CBE_RV_COB_CODE
                          LEFT JOIN {mintdatalake}.scs_dbo.refvalues rv
                              ON rv.RV_VALUE = rc.CBE_REVISED_CLASS
                              AND rv.RV_RD_DOMAIN = 'RCL'
                          WHERE rv.RV_DESC IS NOT NULL
                          AND ft.FT_PO_INTERNAL_POL_NO IN 
                              (
                              SELECT DISTINCT PO_INTERNAL_POL_NO 
                              FROM {mintdatalake}.scs_dbo.policies 
                              WHERE PO_PROD_OFFICE IN ('SPAIN', 'MADRID', 'BARCELON', 'BARCELONA', 'BCN01', 'MDR01')
                              )
                          AND RV_DESC is not null
                        )  
                        GROUP BY keyIdPolis
                      ) fty on CONCAT(ft.FT_PO_INTERNAL_POL_NO, '/', ft.FT_PO_INTERNAL_RSN) = fty.keyIdPolis
              ) GROUP BY KeyIdPolis, TransactionComponentTypeCode, TransactionComponentTypeDescription, CountkeyIdPolis
) FT ON p.KeyIdPolis = FT.KeyIdPolis