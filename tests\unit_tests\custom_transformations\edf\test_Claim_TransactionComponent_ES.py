from datetime import date

import pytest
from pyspark.sql import DataFrame
from pyspark.sql.types import DoubleType, StringType, StructField, StructType
from pyspark.testing.utils import assertDataFrameEqual

from models_scripts.transformations.edf.Claim_TransactionComponent_ES import (
    ExtraColumnsClaimTransactionComponentMerger,
)


class TestExtraColumnsClaimTransactionComponentMerger:
    @pytest.fixture(scope="class")
    def main_schema(self):
        schema = StructType(
            [
                StructField("KeyInternSchadenummer", StringType()),
                StructField("KeyIdPolis", StringType()),
                StructField("KeyDekkingsNummer", StringType()),
                StructField("KeySchadeBoekingsNummer", StringType()),
                StructField("TransactionComponentAmount", DoubleType()),
                StructField("TransactionComponentTypeCode", StringType()),
                StructField("TransactionComponentTypeDescription", StringType()),
            ]
        )

        return schema

    @pytest.fixture(scope="class")
    def claim_transaction_df(self, spark, main_schema):
        data = [
            (
                "KeyInternSchadenummer1",
                "KeyIdPolis1",
                "KeyDekkingsNummer1",
                "KeySchadeBoekingsNummer1",
                1.0,
                "TransactionComponentTypeCode1",
                "TransactionComponentTypeDescription1",
            ),
            (
                "KeyInternSchadenummer1",
                "KeyIdPolis1",
                "KeyDekkingsNummer2",
                "KeySchadeBoekingsNummer2",
                2.0,
                "TransactionComponentTypeCode2",
                "TransactionComponentTypeDescription2",
            ),
            (
                "KeyInternSchadenummer1",
                "KeyIdPolis1",
                "KeyDekkingsNummer3",
                "KeySchadeBoekingsNummer3",
                3.0,
                "TransactionComponentTypeCode3",
                "TransactionComponentTypeDescription3",
            ),
            (
                "KeyInternSchadenummer2",
                "KeyIdPolis1",
                "KeyDekkingsNummer4",
                "KeySchadeBoekingsNummer4",
                4.0,
                "TransactionComponentTypeCode4",
                "TransactionComponentTypeDescription4",
            ),
            (
                "KeyInternSchadenummer3",
                "KeyIdPolis2",
                "KeyDekkingsNummer5",
                "KeySchadeBoekingsNummer5",
                5.0,
                "TransactionComponentTypeCode5",
                "TransactionComponentTypeDescription5",
            ),
            (
                "KeyInternSchadenummer3",
                "KeyIdPolis2",
                "KeyDekkingsNummer6",
                "KeySchadeBoekingsNummer6",
                6.0,
                "TransactionComponentTypeCode6",
                "TransactionComponentTypeDescription6",
            ),
            (
                "KeyInternSchadenummer3",
                "KeyIdPolis2",
                "KeyDekkingsNummer7",
                "KeySchadeBoekingsNummer7",
                7.0,
                "TransactionComponentTypeCode7",
                "TransactionComponentTypeDescription7",
            ),
            (
                "KeyInternSchadenummer3",
                "KeyIdPolis2",
                "KeyDekkingsNummer8",
                "KeySchadeBoekingsNummer8",
                8.0,
                "TransactionComponentTypeCode8",
                "TransactionComponentTypeDescription8",
            ),
        ]

        df = spark.createDataFrame(data, main_schema)

        return df

    @pytest.fixture(scope="class")
    def extra_claims_df(self, spark):
        data = [
            (
                "KeyInternSchadenummer1",
                "KeyDekkingsNummer1",
                "EXTRA_RECORDS_SOURCE01:1",
                9.0,
                "TransactionComponentTypeCode9",
                "TransactionComponentTypeDescription9",
            ),
            (
                "KeyInternSchadenummer1",
                "KeyDekkingsNummer1",
                "EXTRA_RECORDS_SOURCE01:2",
                10.0,
                "TransactionComponentTypeCode10",
                "TransactionComponentTypeDescription10",
            ),
            (
                "KeyInternSchadenummer2",
                "KeyDekkingsNummer4",
                "EXTRA_RECORDS_SOURCE01:3",
                11.0,
                "TransactionComponentTypeCode10",
                "TransactionComponentTypeDescription10",
            ),
            (
                "KeyInternSchadenummer3",
                "KeyDekkingsNummer5",
                "EXTRA_RECORDS_SOURCE01:4",
                12.0,
                "TransactionComponentTypeCode10",
                "TransactionComponentTypeDescription10",
            ),
            (
                "KeyInternSchadenummer3",
                "KeyDekkingsNummer5",
                "EXTRA_RECORDS_SOURCE01:5",
                13.0,
                "TransactionComponentTypeCode10",
                "TransactionComponentTypeDescription10",
            ),
        ]

        columns = [
            "KeyInternSchadenummer",
            "KeyDekkingsNummer",
            "KeySchadeBoekingsNummer",
            "TransactionComponentAmount",
            "TransactionComponentTypeCode",
            "TransactionComponentTypeDescription",
        ]

        # Create DataFrame
        df = spark.createDataFrame(data, columns)
        return df

    def test_append_new_records_to_main_df(
        self, spark, claim_transaction_df, extra_claims_df, main_schema
    ):
        merger = ExtraColumnsClaimTransactionComponentMerger(
            claim_transaction_df, extra_claims_df
        )
        output_df = merger.append_new_records_to_main_df()

        expected_appended_df = spark.createDataFrame(
            [
                (
                    "KeyInternSchadenummer1",
                    "KeyIdPolis1",
                    "KeyDekkingsNummer1",
                    "EXTRA_RECORDS_SOURCE01:1",
                    9.0,
                    "TransactionComponentTypeCode9",
                    "TransactionComponentTypeDescription9",
                ),
                (
                    "KeyInternSchadenummer1",
                    "KeyIdPolis1",
                    "KeyDekkingsNummer1",
                    "EXTRA_RECORDS_SOURCE01:2",
                    10.0,
                    "TransactionComponentTypeCode10",
                    "TransactionComponentTypeDescription10",
                ),
                (
                    "KeyInternSchadenummer2",
                    "KeyIdPolis1",
                    "KeyDekkingsNummer4",
                    "EXTRA_RECORDS_SOURCE01:3",
                    11.0,
                    "TransactionComponentTypeCode10",
                    "TransactionComponentTypeDescription10",
                ),
                (
                    "KeyInternSchadenummer3",
                    "KeyIdPolis2",
                    "KeyDekkingsNummer5",
                    "EXTRA_RECORDS_SOURCE01:4",
                    12.0,
                    "TransactionComponentTypeCode10",
                    "TransactionComponentTypeDescription10",
                ),
                (
                    "KeyInternSchadenummer3",
                    "KeyIdPolis2",
                    "KeyDekkingsNummer5",
                    "EXTRA_RECORDS_SOURCE01:5",
                    13.0,
                    "TransactionComponentTypeCode10",
                    "TransactionComponentTypeDescription10",
                ),
            ],
            main_schema,
        )

        expected_df = claim_transaction_df.unionByName(expected_appended_df)

        assert output_df.count() == 13

        assertDataFrameEqual(output_df, expected_df)
