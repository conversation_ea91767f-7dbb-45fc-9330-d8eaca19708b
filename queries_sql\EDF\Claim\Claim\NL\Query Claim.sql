select s.internschadenummer                                           as "KeyInternSchadenummer"
, replace(s.teksttoedracht, chr(10), '')                              as "BackgroundNarrative"
, ''                                                                  as "CatastropheCode"
, ''                                                                  as "CatastropheDescription"
, s.internschadenummer                                                as "ClaimCode"
, s.landcode                                                          as "ClaimCountry"
, case when s.mutatiereden = '218' then 'Y' else 'N' end              as "ClaimDeniedIndicator"
, replace(s.teksttoedracht, chr(10), '')                              as "ClaimDescription"
, sa.eindedatetime                                                    as "ClaimDiaryDate"
, ''                                                                  as "ClaimEventCode"
, ''                                                                  as "ClaimEventDescription"
, sm.naam                                                             as "ClaimHandler"
, s.medewerkernummer                                                  as "ClaimHandlerCode"
, r.naam + ' ' + r.naam3                                              as "ClaimInsured"
, ifnull(lm.aanmaakdatum, s.datum1ebehandeling)                       as "ClaimLastModifiedDate"
,  case when sv.Rubriek2 ='F' then 'N'  when sv.Rubriek2 ='L' then 'Y' else '' end    as "ClaimLeadIndicator"
, s.locatieschade                                                     as "ClaimLocationState"
, s.invoerdatum                                                       as "ClaimOpenDate"
, s.schadenummerkantoor                                               as "ClaimReference"
, s.datumgemeld                                                       as "ClaimReportDate"
, case when s.datumafgewerkt is null then 'Open' else 'Closed' end    as "ClaimStatus"
, s.tekenjaar                                                         as "ClaimYearOfAccount"
, s.datumafgewerkt                                                    as "CloseDate"
, ltrim(dt.tekst1) + ' ' + ltrim(dt.tekst2) + ' ' + ltrim(dt.tekst3) + ' ' + ltrim(dt.tekst4) + ' ' + ltrim(dt.tekst5) + ' '
  + ltrim(dt.tekst6) + ' ' + ltrim(dt.tekst7) + ' ' + ltrim(dt.tekst8) + ' ' + ltrim(dt.tekst9) + ' ' + ltrim(dt.tekst10) + ' '
  + ltrim(dt.tekst11) + ' ' + ltrim(dt.tekst12) + ' ' + ltrim(dt.tekst13) + ' ' + ltrim(dt.tekst14) + ' ' + ltrim(dt.tekst15) as "CoverageNarrative"
, 'N'                                                                 as "CoverholderWithClaimsAuthority"
, ld.aanmaakdatum                                                     as "DateOfDeclinature"
, s.schadedatum                                                       as "DateOfLoss"
, 'NL'                                                                as "GeographicalOriginOfTheClaim"
, ''                                                                  as "LineageReference"
, sv.rubriek1                                                         as "LitigationCode"
, svl.omschrijving                                                    as "LitigationDescription"
, sv.rubriek5                                                         as "MaximumPotentialLoss"
, 'EURO'                                                              as "MaximumPotentialLossCurrency"
, sv.rubriek4                                                         as "MaximumPotentialLossPercentage"
, 'EURO'                                                              as "OriginalCurrencyCode"
, case when instr(s.schadenummerkantoor, '/') > 0 then s.schadenummerkantoor else '' end as "PreviousClaimReference"
, case when instr(s.schadenummerkantoor, '/') > 0 then 'SCS' else '' end as "PreviousSourceSystem"
, case when instr(s.schadenummerkantoor, '/') > 0 then 'SCS' else '' end as "PreviousSourceSystemDescription"
, ''                                                                  as "ReasonDeclined"
, ltrim(rt.tekst1) + ' ' + ltrim(rt.tekst2) + ' ' + ltrim(rt.tekst3) + ' ' + ltrim(rt.tekst4) + ' ' + ltrim(rt.tekst5) + ' '
  + ltrim(rt.tekst6) + ' ' + ltrim(rt.tekst7) + ' ' + ltrim(rt.tekst8) + ' ' + ltrim(rt.tekst9) + ' ' + ltrim(rt.tekst10) + ' '
  + ltrim(rt.tekst11) + ' ' + ltrim(rt.tekst12) + ' ' + ltrim(rt.tekst13) + ' ' + ltrim(rt.tekst14) + ' ' + ltrim(rt.tekst15) as "ReserveNarrative"
, s.schadenummertussenpersoon                                         as "ServiceProviderReference"
, 'EURO'                                                              as "SettlementCurrencyCode"
, ''                                                                  as "SubrogationSalvageIndicator"
, ''                                                                  as "TacticsNarrative"
, ''                                                                  as "TPAHandleIndicator"
, sv.rubriek3                                                         as "TriageCode"
, ''                                                                  as "XCSClaimRef"
, ''                                                                  as "XCSClaimCode"
from pub.schade s
inner join pub.histpolisversie p on p.bedrijfinternschadenummer = s.bedrijfinternschadenummer
left outer join pub.tabelschadeoorzaak so on so.soortverzekeringoorzaakcode = s.soortverzekeringoorzaakcode
                                         and so.productcodevan >= p.productcode and so.productcodetm <= p.productcode
left outer join (select a.bedrijfinternschadenummer, min(a.eindedatetime) as eindedatetime
								 from pub.agenda a
								 where a.afwerkdatetime is null
								 group by a.bedrijfinternschadenummer) sa on sa.bedrijfinternschadenummer = s.bedrijfinternschadenummer
left outer join pub.tabelmedewerker sm on sm.bedrijfmedewerkernummer = s.bedrijfmedewerkernummer
left outer join pub.relatie r on r.relatienummer = s.relatienummer
left outer join (select a.bedrijfinternschadenummer, max(a.aanmaakdatum) as aanmaakdatum
                 from pub.logboekschade a
                 where a.logcode <> 2100
                 group by a.bedrijfinternschadenummer) lm on lm.bedrijfinternschadenummer = s.bedrijfinternschadenummer
left outer join (select a.bedrijfinternschadenummer, min(a.aanmaakdatum) as aanmaakdatum
                 from pub.logboekschade a
                 where a.logcode = 218
                 group by a.bedrijfinternschadenummer) ld on ld.bedrijfinternschadenummer = s.bedrijfinternschadenummer
left outer join pub.schadevrij sv on sv.bedrijfinternschadenummer = s.bedrijfinternschadenummer
left outer join pub.tabelvrijerubriektabel svl on svl.tabelcode = sv.rubriek1 and svl.soortvrijerubriek = 'S' and svl.volgnummerrubriek = 1
left outer join (select a.bedrijfinternschadenummer, max(a.assur_recid) as assur_recid
                 from pub.memoschade a
                 where a.memocode = 'DT'
                 group by a.bedrijfinternschadenummer) dtri on s.bedrijfinternschadenummer = dtri.bedrijfinternschadenummer
left outer join pub.memoschade dt on dt.assur_recid = dtri.assur_recid
left outer join (select a.bedrijfinternschadenummer, max(a.assur_recid) as assur_recid
                 from pub.memoschade a
                 where a.memocode = 'RT'
                 group by a.bedrijfinternschadenummer) rtri on s.bedrijfinternschadenummer = rtri.bedrijfinternschadenummer
left outer join pub.memoschade rt on rt.assur_recid = rtri.assur_recid
left outer join (
select a.bedrijfinternschadenummer, max(a.boekdatum) as boekdatum
from pub.schadeboeking a
  group by a.bedrijfinternschadenummer) b
  on b.bedrijfinternschadenummer = s.bedrijfinternschadenummer
left outer join (
select x.bedrijfinternschadenummer, max(x.boekdatum) as boekdatum
from pub.schadereserve x
  group by x.bedrijfinternschadenummer) m
  on m.bedrijfinternschadenummer = s.bedrijfinternschadenummer
