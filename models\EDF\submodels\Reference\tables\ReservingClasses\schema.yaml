Columns:
  PolicyProductCode:
    dataType: string
  SectionProductCode:
    dataType: string
  ProductDescription:
    dataType: string
  ExistingSectionReportingGroup:
    dataType: string
  ExistingMappingReservingClass:
    dataType: string
  ProductSectionReportingGroup:
    dataType: string
  ProductReservingClass:
    dataType: string
  AttritionalLargeThreshold:
    dataType: string
  EuropeanClass:
    dataType: string
  EuropeanSubClass:
    dataType: string
  KeyReserving:
    dataType: string
  AssuredMainActivityCode:
    dataType: string
  ProductName:
    dataType: string
