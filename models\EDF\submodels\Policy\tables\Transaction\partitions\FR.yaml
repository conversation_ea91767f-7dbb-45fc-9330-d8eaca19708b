Country: FR
existCustomTransformation: 'True'

dataSource:
- name: main
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: dev_europeandatalake_01
      uat: uat_europeandatalake_01
      prod: prod_europeandatalake_01
    schema: FrenchBordereaux
    table: Support_PolicyBase

- name: policy
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Policy
    Table: Policy

- name: dates_gaap
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Reference
    Table: DatesGAAP
    Partitions:
      - UN

ColumnSpecs:
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: FR
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  PolicySectionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: FR
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  PolicyTransactionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: FR
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        - source: COLUMN
          name: KeyFactuurnummer
        sep: ':'
  USGAAP_Date:
    NotInSource: True
