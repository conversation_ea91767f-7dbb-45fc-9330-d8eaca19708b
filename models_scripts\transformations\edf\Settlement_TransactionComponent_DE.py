from pyspark.sql import DataFrame

from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.add_columns import (
    get_german_country_using_policy_table,
)
from models_scripts.transformations.edf.common.currency import (
    SettlementCurrencyConversor,
)


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["TransactionComponent_main"]
    policy_df = df_dict["Policy_Policy"]
    transaction_df = df_dict["Settlement_Transaction"]
    exchange_rate_df = df_dict["TransactionComponent_exchange_rate"]

    main_with_country_df = get_german_country_using_policy_table(main_df, policy_df)

    component_with_converted_currencies_df = (
        SettlementCurrencyConversor.add_new_columns(
            main_with_country_df, transaction_df, exchange_rate_df
        )
    )
    return component_with_converted_currencies_df
