""""Module for miscellaneous transformations that don't fit in any other category."""

import pyspark.sql.functions as F
from pyspark.sql import DataFrame

from models_scripts.transformations.common.add_columns import (
    add_column_from_match_table,
)
from models_scripts.transformations.common.traits import (
    RecordsMarker,
    mark_records,
    transformation,
)


@transformation
@mark_records
def unify_keyDekkingsNummer_with_claim_section(
    df: DataFrame, section_df: DataFrame, records_marker: RecordsMarker
) -> DataFrame:
    """For Netherland, we had unified the KeyDekkingNummer for ClaimSection to have just one for each Claim. So
    we need to unify the KeyDekkingsNummer for the related Claim dataframes as well."""
    new_dekking_number_df = add_column_from_match_table(
        df=df,
        match_df=section_df,
        join_col="KeyInternSchadenummer",
        column_to_replicate={"KeyDekkingsNummer": "KeyDekkingsNummerNew"},
    )
    changed_cond = F.col("KeyDekkingsNummer") != F.col("KeyDekkingsNummerNew")
    marked_df = records_marker.mark_records(new_dekking_number_df, changed_cond)
    fixed_df = marked_df.drop("KeyDekkingsNummer").withColumnRenamed(
        "KeyDekkingsNummerNew", "KeyDekkingsNummer"
    )
    return fixed_df
