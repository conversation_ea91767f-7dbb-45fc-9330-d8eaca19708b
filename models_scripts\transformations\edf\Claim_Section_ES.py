import mines2.core.constants as const
import pyspark.sql.functions as F
from pyspark.sql import DataFrame

from models_scripts.transformations.common.add_columns import add_prefix_for_join
from models_scripts.transformations.common.traits import business_logic


def add_claims_from_mapping_scs_sisnet_table(
    main_df: DataFrame, sisnet_mapping_table_df: DataFrame
) -> DataFrame:
    """Add new Claims from mapping SCS SISNET table."""
    # Add SectionReference to mapping table
    mapping_with_reference_df = sisnet_mapping_table_df.withColumn(
        "SCS_SectionReference", F.col("SCS_KeyDekkingsNummer")
    )
    # Add new Claims from mapping SCS SISNET table

    prefix = "__MAPPING_"
    cols_without_prefix = [
        "KeyInternSchadenummer",
        "SCS_KeyInternSchadenummer",
        "SCS_KeyIdPolis",
        "SCS_KeyDekkingsNummer",
        "SCS_SectionReference",
    ]
    cols_to_prefix = ["SectionProductDescription", "SectionProductCode"]
    prefixed_mapping_df = add_prefix_for_join(
        mapping_with_reference_df, cols_without_prefix, cols_to_prefix, prefix
    )

    joined_df = main_df.join(prefixed_mapping_df, ["KeyInternSchadenummer"], "right")

    # columns mapping
    col_mapping = {col_name: F.col("col_name") for col_name in main_df.columns}
    col_mapping["KeyInternSchadenummer"] = F.coalesce(
        F.col("SCS_KeyInternSchadenummer"), F.col("KeyInternSchadenummer")
    )
    col_mapping["KeyIdPolis"] = F.coalesce(F.col("SCS_KeyIdPolis"), F.col("KeyIdPolis"))
    col_mapping["KeyDekkingsNummer"] = F.coalesce(
        F.col("SCS_KeyDekkingsNummer"), F.col("KeyDekkingsNummer")
    )
    col_mapping["SectionReference"] = F.coalesce(
        F.col("SCS_KeyDekkingsNummer"), F.col("SectionReference")
    )
    col_mapping["SectionProductCode"] = F.coalesce(
        F.when(
            F.col(prefix + "SectionProductCode").isNotNull(),
            F.col(prefix + "SectionProductDescription"),
        ).otherwise(F.col("SectionProductCode"))
    )
    new_claims_df = joined_df.select(
        *[value.alias(col_name) for col_name, value in col_mapping.items()]
    )

    new_claims_dedup_df = new_claims_df.dropDuplicates(
        ["KeyInternSchadenummer", "KeyDekkingsNummer"]
    )

    return new_claims_dedup_df.withColumn(
        const.SOURCE_SYSTEM, F.lit("SISNET_SCS_TEMP_SOURCE_TO_BE_REPLACED")
    )


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["Section_main"].drop(const.SOURCE_SYSTEM)
    sisnet_mapping_table_df = df_dict["Support_SisnetSCSMappingTableES"].drop(
        const.SOURCE_SYSTEM
    )

    output_df = add_claims_from_mapping_scs_sisnet_table(
        main_df, sisnet_mapping_table_df
    )

    return output_df
