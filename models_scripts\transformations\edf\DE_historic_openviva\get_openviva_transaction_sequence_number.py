from pyspark.sql import DataFrame
from pyspark.sql import functions as F

from models_scripts.transformations.common.traits import requires, transformation


@transformation
@requires(["TransactionType", "TransactionAuthorisationDate", "TransactionDate"])
def get_openviva_transaction_sequence_number(
    input_df: DataFrame, sequence_format: str
) -> DataFrame:
    """Add TransactionSequenceNumber column to input_df. This column is calculated based on the following formula:
    The sequence number is based on the TransactionDate (for R) or on the TransactionAuthorisationDate (for Z)
    The dates will be combined into one column and formatted based on the sequence_format param.

    Args:
        input_df (DataFrame): Claim_Transaction DataFrame or Claim_TransactionComponent from openviva.
    """
    auth_date_format = F.date_format(
        F.col("TransactionAuthorisationDate"), sequence_format
    )
    tran_date_format = F.date_format(F.col("TransactionDate"), sequence_format)
    formatted_dates = F.when(
        F.col("TransactionType") == "R", tran_date_format
    ).otherwise(auth_date_format)
    output_df = input_df.withColumn("TransactionSequenceNumber", formatted_dates)
    return output_df
