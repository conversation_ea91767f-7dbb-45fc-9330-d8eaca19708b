/* QUERY POLICY_LIMIT MANUAL DEF */

SELECT DISTINCT
    CASE WHEN B.Certificate_number IS NOT NULL THEN B.KeyIdPolis ELSE A.KeyIdPolis END AS 'KeyIdPolis',
    CASE WHEN B.Certificate_number IS NOT NULL THEN CONCAT(<PERSON><PERSON><PERSON>Dek<PERSON>sNummer, RIGHT(<PERSON><PERSON>um<PERSON>, 4)) ELSE A.KeyDekkingsNummer END AS 'KeyDekkingsNummer',
    A<PERSON> 'Limit',
    A.LimitBasisCode AS 'LimitBasisCode',
    A.LimitBasisDescription AS 'LimitBasisDescription',
    A<PERSON>itCurrencyC<PERSON> AS 'LimitCurrencyCode',
    A.TopLimit AS 'TopLimit'

FROM ( /* MANUAL DATA */
    SELECT DISTINCT
        REPLACE(CASE WHEN A.NPOLICY = '' THEN TRIM(A.POLIORIG) ELSE TRIM(A.NPOLICY) END, ' ', '') AS Certificate_number,
        B.YEAREFEC,
        CASE
            WHEN (CASE WHEN A.NPOLICY = '' THEN A.POLIORIG ELSE A.NPOLICY END) IS NOT NULL THEN
                CONCAT(
                    A.CODPROD, ' ',
                    (SELECT MIN(CAST(Z.YEAREFEC AS float)) FROM NTJDWHMRK..MEDLMAPS Z WHERE B.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK), '/',
                    CASE WHEN A.NPOLICY = '' THEN A.POLIORIG ELSE A.NPOLICY END, '_',
                    (CAST(B.YEAREFEC AS float) - (SELECT MIN(CAST(Z.YEAREFEC AS float)) FROM NTJDWHMRK..MEDLMAPS Z WHERE B.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK) + 1)
                )
            WHEN (CASE WHEN A.NPOLICY = '' THEN A.POLIORIG ELSE A.NPOLICY END) IS NULL THEN
                (
                    SELECT DISTINCT POLICODE
                    FROM NTJDWHMRK..MEDFPOLI X
                    WHERE REPLACE(CASE WHEN A.NPOLICY = '' THEN TRIM(A.POLIORIG) ELSE TRIM(A.NPOLICY) END, ' ', '') = LEFT(X.POLIREFE, CHARINDEX('_', X.POLIREFE) - 1)
                    AND B.YEAREFEC = X.YEAOFACC
                )
        END AS 'KeyIdPolis',
        CONCAT(
            TRIM(CASE WHEN A.NPOLICY = '' THEN A.POLIORIG ELSE A.NPOLICY END),
            CASE
                WHEN A.SECREGRU = '1. Construction Professional Risks' THEN '/MA1'
                WHEN A.SECREGRU = '2. Financial Services Professional Risks' THEN '/MA2'
                WHEN A.SECREGRU = '3. Miscellaneous Professional Risks' THEN '/MA3'
                WHEN A.SECREGRU = '4. Professions Professional Risks' THEN '/MA4'
                WHEN A.SECREGRU = '6. D and O' THEN '/MA5'
                WHEN A.SECREGRU = '11. Liability' THEN '/MA6'
                WHEN A.SECREGRU = 'Surety' THEN '/MA7'
                WHEN A.SECREGRU = 'Personal Accident' THEN '/MA8'
                WHEN A.SECREGRU = 'Private Clinics' THEN '/MA9'
                WHEN A.SECREGRU = 'Energy' THEN '/MA10'
			    WHEN A.SECREGRU = 'Terror' THEN '/MA11'
            ELSE '/MAN' END
        ) AS 'KeyDekkingsNummer',
        B.SUMAASEG AS 'Limit',
        'AOC' AS 'LimitBasisCode',
        'Límite por siniestro' AS 'LimitBasisDescription',
        'EUR' AS 'LimitCurrencyCode',
        'Y' AS 'TopLimit'
    FROM NTJDWHMRK..MEDLMAPO A, NTJDWHMRK..MEDLMAPS B
    WHERE A.ID_MEDLMAPO = B.ID_MEDLMAPO_FK
) A

LEFT JOIN ( /* SISNET DATA */
    SELECT DISTINCT
        TRIM(LEFT(B.POLIREFE, CHARINDEX('_', B.POLIREFE) - 1)) AS Certificate_number,
        B.YEAOFACC,
        B.POLICODE AS 'KeyIdPolis',
        TRIM(LEFT(C.SECTREFE, CHARINDEX('/', C.SECTREFE) - 1)) AS 'KeyDekkingsNummer',
        -- C.SECTREFE AS 'KeyDekkingsNummer',
        A.LIMIT AS 'Limit',
        A.LIMBASCO AS 'LimitBasisCode',
        A.LIMBASDE AS 'LimitBasisDescription',
        A.LIMCURCO AS 'LimitCurrencyCode',
        A.TOPLIMIT AS 'TopLimit'
    FROM
        NTJDWHMRK..MEDFPOPL A,
        NTJDWHMRK..MEDFPOLI B,
        NTJDWHMRK..MEDFPOSE C
    WHERE
        B.ID_MEDFPOLI = C.ID_MEDFPOLI_FK AND
        C.ID_MEDFPOSE = A.ID_MEDFPOSE_FK AND
        C.ID_MEDFPOSE IN (
            SELECT MAX(X.ID_MEDFPOSE)
            FROM NTJDWHMRK..MEDFPOLI Z, NTJDWHMRK..MEDFPOSE X
            WHERE
                Z.ID_MEDFPOLI = X.ID_MEDFPOLI_FK
                AND Z.FECHALTA > CONVERT(datetime, '01/01/2000', 103)
                AND Z.POLICODE = B.POLICODE
                AND C.SECTREFE = X.SECTREFE
        )
) B ON TRIM(A.Certificate_number) = TRIM(B.Certificate_number) AND CAST(A.YEAREFEC AS FLOAT) = CAST(B.YEAOFACC AS FLOAT)
