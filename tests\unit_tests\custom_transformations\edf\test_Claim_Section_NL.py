"""
Tests Cases
    1 For Claims with Paid and Outstanding Reserve transactions in one Section only, there is no issue. It is
   straightforward to select the Section Number and Section Code from the Claims data.
    * At the time of the validation exercise, there were 115 claims with Paid and Outstanding Reserve transactions in more
   than one Section. This lead to duplicate records being created. The logic to resolve this is now as follows:
    2 If the Total Incurred value on the claim across all sections is zero, then take the smallest Section Number.
    3 If the Total Incurred value on the claim across all sections is not zero AND there are Sections with the same
    Incurred value (e.g. opening reserve of EUR1), take the smallest Section Number.
    4 If the Total Incurred value on the claim across all sections is not zero, then take the Section where the Incurred
     value is highest.
"""
import datetime

import pytest
from mines2.core.extensions.misc import assert_dataframe_equality
from pyspark.sql import DataFrame
from pyspark.sql.types import *

from models_scripts.transformations.edf.Claim_Section_NL import custom_transformation


class TestExtraColumnsClaimTransactionMerger:
    @pytest.fixture(scope="class")
    def claims_df(self, spark):
        return spark.createDataFrame(
            # Example  of the base case, no duplication  on the matching tables
            [
                Row(
                    ClaimCauseCode=None,
                    ClaimsNumberBroker="QUS2335302",
                    ClaimsNumberOffice="1",
                    ClientNumber=1560790,
                    ClosedDate=None,
                    CompanyInternalClaimNumber="0000000002/000001",
                    DateOfLoss=datetime.datetime(2024, 5, 5, 1),
                    DateReported=datetime.datetime(2024, 5, 7, 1),
                    InternalClaimsNumber=1,
                    InternalPolicyNumber="SIIMAR34284",
                    ProductCode="1",
                    MTAReason=100,
                ),
                #  Example of test case 2, total incurred is 0.
                Row(
                    ClaimCauseCode="609",
                    ClaimsNumberBroker=None,
                    ClaimsNumberOffice="2",
                    ClientNumber=1349389,
                    ClosedDate=datetime.datetime(2022, 12, 29),
                    CompanyInternalClaimNumber="0000000002/000002",
                    DateOfLoss=datetime.datetime(2019, 9, 12, 1),
                    DateReported=datetime.datetime(2019, 9, 12, 1),
                    InternalClaimsNumber=2,
                    InternalPolicyNumber="SIIMAR30099",
                    ProductCode="2",
                    MTAReason=212,
                ),
                #  Example of test case 3, two sections with the same Total Incurred.
                Row(
                    ClaimCauseCode="609",
                    ClaimsNumberBroker=None,
                    ClaimsNumberOffice="3",
                    ClientNumber=1349389,
                    ClosedDate=datetime.datetime(2022, 12, 29),
                    CompanyInternalClaimNumber="0000000002/000003",
                    DateOfLoss=datetime.datetime(2019, 9, 12, 1),
                    DateReported=datetime.datetime(2019, 9, 12, 1),
                    InternalClaimsNumber=3,
                    InternalPolicyNumber="SIIMAR30092",
                    ProductCode="3",
                    MTAReason=212,
                ),
                # Example of test case 4, multiple sections with non zero incurred and needed to get the highest incurred.
                Row(
                    ClaimCauseCode="609",
                    ClaimsNumberBroker=None,
                    ClaimsNumberOffice="4",
                    ClientNumber=1349389,
                    ClosedDate=datetime.datetime(2022, 12, 29),
                    CompanyInternalClaimNumber="0000000002/000004",
                    DateOfLoss=datetime.datetime(2019, 9, 12, 1),
                    DateReported=datetime.datetime(2019, 9, 12, 1),
                    InternalClaimsNumber=4,
                    InternalPolicyNumber="SIIMAR30093",
                    ProductCode="4",
                    MTAReason=212,
                ),
            ],
            schema=StructType(
                [
                    StructField("ClaimCauseCode", StringType()),
                    StructField("ClaimsNumberBroker", StringType()),
                    StructField("ClaimsNumberOffice", StringType()),
                    StructField("ClientNumber", LongType()),
                    StructField("ClosedDate", TimestampType()),
                    StructField("CompanyInternalClaimNumber", StringType()),
                    StructField("DateOfLoss", TimestampType()),
                    StructField("DateReported", TimestampType()),
                    StructField("InternalClaimsNumber", LongType()),
                    StructField("InternalPolicyNumber", StringType()),
                    StructField("ProductCode", StringType()),
                    StructField("MTAReason", LongType()),
                ]
            ),
        )

    @pytest.fixture(scope="class")
    def hist_policy_version_df(self, spark):
        return spark.createDataFrame(
            [
                Row(
                    IdPolis=1,
                    InceptionDate=datetime.date(2018, 3, 26),
                    InternalPolicyNumber="SIIMAR34284",
                    InstallmentFreq=12,
                    ProductCode=135,
                    CompanyInternalClaimNumber="0000000002/000001",
                    InternalClaimsNumber=1,
                ),
                Row(
                    IdPolis=2,
                    InceptionDate=datetime.date(1999, 1, 19),
                    InternalPolicyNumber="SIIMAR30091",
                    InstallmentFreq=3,
                    ProductCode=1211,
                    CompanyInternalClaimNumber="0000000002/000002",
                    InternalClaimsNumber=2,
                ),
                Row(
                    IdPolis=3,
                    InceptionDate=datetime.date(1999, 1, 19),
                    InternalPolicyNumber="SIIMAR300991",
                    InstallmentFreq=3,
                    ProductCode=1214,
                    CompanyInternalClaimNumber="0000000002/000003",
                    InternalClaimsNumber=3,
                ),
                Row(
                    IdPolis=4,
                    InceptionDate=datetime.date(1999, 1, 19),
                    InternalPolicyNumber="SIIMAR30092",
                    InstallmentFreq=3,
                    ProductCode=1211,
                    CompanyInternalClaimNumber="0000000002/000004",
                    InternalClaimsNumber=4,
                ),
            ],
            schema=StructType(
                [
                    StructField("IdPolis", LongType()),
                    StructField("InceptionDate", DateType()),
                    StructField("InternalPolicyNumber", StringType()),
                    StructField("InstallmentFreq", LongType()),
                    StructField("ProductCode", LongType()),
                    StructField("CompanyInternalClaimNumber", StringType()),
                    StructField("InternalClaimsNumber", LongType()),
                ]
            ),
        )

    @pytest.fixture(scope="class")
    def hist_section_df(self, spark):
        return spark.createDataFrame(
            [
                Row(idpolis=1, SectionCode=305, SectionNumber=2),
                Row(idpolis=1, SectionCode=100, SectionNumber=1),
                Row(idpolis=2, SectionCode=110, SectionNumber=1),
                Row(idpolis=2, SectionCode=315, SectionNumber=2),
                Row(idpolis=3, SectionCode=113, SectionNumber=1),
                Row(idpolis=3, SectionCode=313, SectionNumber=2),
                Row(idpolis=4, SectionCode=150, SectionNumber=1),
                Row(idpolis=4, SectionCode=110, SectionNumber=2),
                Row(idpolis=4, SectionCode=120, SectionNumber=3),
                Row(idpolis=4, SectionCode=305, SectionNumber=4),
                Row(idpolis=4, SectionCode=585, SectionNumber=5),
                Row(idpolis=4, SectionCode=145, SectionNumber=6),
            ],
            schema=StructType(
                [
                    StructField("idpolis", LongType()),
                    StructField("SectionCode", LongType()),
                    StructField("SectionNumber", LongType()),
                ]
            ),
        )

    @pytest.fixture(scope="class")
    def claim_payments_df(self, spark):
        return spark.createDataFrame(
            [
                Row(
                    Amount=100,
                    AmountPaid=1634.35,
                    BookDate=datetime.datetime(2022, 10, 1, 1, 0),
                    ClientNumber=1349389,
                    CompanyInternalClaimNumber="0000000002/000003",
                    CompanyPaymentNumber="0000000002/0000015939",
                    FollowUpNumber=315900,
                    InternalClaimsNumber=3,
                    Paid=True,
                    SectionCode=113,
                    SectionNumber=1,
                    StatisticsCode=3,
                    StatusCode=1,
                    TypeOfPayment=6,
                    UnderwritingYear=2019,
                ),
                Row(
                    Amount=100,
                    AmountPaid=1634.35,
                    BookDate=datetime.datetime(2022, 10, 1, 1),
                    ClientNumber=1349389,
                    CompanyInternalClaimNumber="0000000002/000003",
                    CompanyPaymentNumber="0000000002/0000015930",
                    FollowUpNumber=315900,
                    InternalClaimsNumber=3,
                    Paid=True,
                    SectionCode=313,
                    SectionNumber=2,
                    StatisticsCode=3,
                    StatusCode=1,
                    TypeOfPayment=6,
                    UnderwritingYear=2019,
                ),
                Row(
                    Amount=0,
                    AmountPaid=1634.35,
                    BookDate=datetime.datetime(2022, 10, 1, 1, 0),
                    ClientNumber=1349389,
                    CompanyInternalClaimNumber="0000000002/000002",
                    CompanyPaymentNumber="0000000002/0000015939",
                    FollowUpNumber=315900,
                    InternalClaimsNumber=2,
                    Paid=True,
                    SectionCode=110,
                    SectionNumber=1,
                    StatisticsCode=3,
                    StatusCode=1,
                    TypeOfPayment=6,
                    UnderwritingYear=2019,
                ),
                Row(
                    Amount=1170.68,
                    AmountPaid=1170.68,
                    BookDate=datetime.datetime(2022, 9, 8, 1),
                    ClientNumber=1349389,
                    CompanyInternalClaimNumber="0000000002/000004",
                    CompanyPaymentNumber="0000000002/0000015781",
                    FollowUpNumber=311141,
                    InternalClaimsNumber=4,
                    Paid=True,
                    SectionCode=150,
                    SectionNumber=1,
                    StatisticsCode=3,
                    StatusCode=1,
                    TypeOfPayment=6,
                    UnderwritingYear=2019,
                ),
                Row(
                    Amount=178908.61,
                    AmountPaid=178908.61,
                    BookDate=datetime.datetime(2022, 8, 12, 1),
                    ClientNumber=1349389,
                    CompanyInternalClaimNumber="0000000002/000004",
                    CompanyPaymentNumber="0000000002/0000015598",
                    FollowUpNumber=308128,
                    InternalClaimsNumber=4,
                    Paid=True,
                    SectionCode=110,
                    SectionNumber=2,
                    StatisticsCode=0,
                    StatusCode=1,
                    TypeOfPayment=1,
                    UnderwritingYear=2019,
                ),
                Row(
                    Amount=353.93,
                    AmountPaid=353.93,
                    BookDate=datetime.datetime(2022, 7, 18, 1),
                    ClientNumber=1349389,
                    CompanyInternalClaimNumber="0000000002/000004",
                    CompanyPaymentNumber="0000000002/0000015382",
                    FollowUpNumber=304791,
                    InternalClaimsNumber=4,
                    Paid=True,
                    SectionCode=120,
                    SectionNumber=3,
                    StatisticsCode=3,
                    StatusCode=1,
                    TypeOfPayment=6,
                    UnderwritingYear=2019,
                ),
                Row(
                    Amount=1839.2,
                    AmountPaid=1839.2,
                    BookDate=datetime.datetime(2022, 7, 6, 1, 0),
                    ClientNumber=1349389,
                    CompanyInternalClaimNumber="0000000002/000004",
                    CompanyPaymentNumber="0000000002/0000015274",
                    FollowUpNumber=303967,
                    InternalClaimsNumber=4,
                    Paid=True,
                    SectionCode=305,
                    SectionNumber=4,
                    StatisticsCode=3,
                    StatusCode=1,
                    TypeOfPayment=6,
                    UnderwritingYear=2019,
                ),
                Row(
                    Amount=1524.06,
                    AmountPaid=1524.06,
                    BookDate=datetime.datetime(2022, 1, 24, 0, 0),
                    ClientNumber=1349389,
                    CompanyInternalClaimNumber="0000000002/000004",
                    CompanyPaymentNumber="0000000002/0000014124",
                    FollowUpNumber=276286,
                    InternalClaimsNumber=4,
                    Paid=True,
                    SectionCode=585,
                    SectionNumber=5,
                    StatisticsCode=3,
                    StatusCode=1,
                    TypeOfPayment=6,
                    UnderwritingYear=2019,
                ),
                Row(
                    Amount=10320.26,
                    AmountPaid=10320.26,
                    BookDate=datetime.datetime(2021, 12, 14, 0, 0),
                    ClientNumber=1349389,
                    CompanyInternalClaimNumber="0000000002/000004",
                    CompanyPaymentNumber="0000000002/0000013784",
                    FollowUpNumber=268695,
                    InternalClaimsNumber=4,
                    Paid=True,
                    SectionCode=145,
                    SectionNumber=6,
                    StatisticsCode=0,
                    StatusCode=1,
                    TypeOfPayment=7,
                    UnderwritingYear=2019,
                ),
                Row(
                    Amount=3011.54,
                    AmountPaid=3011.54,
                    BookDate=datetime.datetime(2021, 7, 21, 1, 0),
                    ClientNumber=1349389,
                    CompanyInternalClaimNumber="0000000002/000004",
                    CompanyPaymentNumber="0000000002/0000012815",
                    FollowUpNumber=231730,
                    InternalClaimsNumber=4,
                    Paid=True,
                    SectionCode=120,
                    SectionNumber=3,
                    StatisticsCode=3,
                    StatusCode=1,
                    TypeOfPayment=6,
                    UnderwritingYear=2019,
                ),
                Row(
                    Amount=10621.6,
                    AmountPaid=10621.6,
                    BookDate=datetime.datetime(2021, 3, 24, 0, 0),
                    ClientNumber=1349389,
                    CompanyInternalClaimNumber="0000000002/000004",
                    CompanyPaymentNumber="0000000002/0000011922",
                    FollowUpNumber=215602,
                    InternalClaimsNumber=4,
                    Paid=True,
                    SectionCode=120,
                    SectionNumber=3,
                    StatisticsCode=3,
                    StatusCode=1,
                    TypeOfPayment=6,
                    UnderwritingYear=2019,
                ),
                Row(
                    Amount=16627.13,
                    AmountPaid=16627.13,
                    BookDate=datetime.datetime(2021, 2, 10, 0, 0),
                    ClientNumber=1349389,
                    CompanyInternalClaimNumber="0000000002/000004",
                    CompanyPaymentNumber="0000000002/0000011562",
                    FollowUpNumber=208449,
                    InternalClaimsNumber=4,
                    Paid=True,
                    SectionCode=120,
                    SectionNumber=3,
                    StatisticsCode=0,
                    StatusCode=1,
                    TypeOfPayment=1,
                    UnderwritingYear=2019,
                ),
            ],
            schema=StructType(
                [
                    StructField("Amount", DoubleType()),
                    StructField("AmountPaid", DoubleType()),
                    StructField("BookDate", TimestampType()),
                    StructField("ClientNumber", LongType()),
                    StructField("CompanyInternalClaimNumber", StringType()),
                    StructField("CompanyPaymentNumber", StringType()),
                    StructField("FollowUpNumber", LongType()),
                    StructField("InternalClaimsNumber", LongType()),
                    StructField("Paid", BooleanType()),
                    StructField("SectionCode", LongType()),
                    StructField("SectionNumber", LongType()),
                    StructField("StatisticsCode", LongType()),
                    StructField("StatusCode", LongType()),
                    StructField("TypeOfPayment", LongType()),
                    StructField("UnderwritingYear", LongType()),
                ]
            ),
        )

    @pytest.fixture(scope="class")
    def claim_reserves_df(self, spark):
        return spark.createDataFrame(
            [
                Row(
                    Amount=1.0,
                    BookDate=datetime.datetime(2024, 5, 10, 1, 0),
                    CompanyInternalClaimNumber="0000000002/000001",
                    DescriptionTypeOfReserve="Schadereserve WA Materieel",
                    SectionCode=100,
                    SectionNumber=1,
                ),
                Row(
                    Amount=0.0,
                    BookDate=datetime.datetime(2022, 10, 1, 1),
                    CompanyInternalClaimNumber="0000000002/000002",
                    DescriptionTypeOfReserve="Schadereserve Casco Materieel",
                    SectionCode=110,
                    SectionNumber=2,
                ),
                Row(
                    Amount=1634.35,
                    BookDate=datetime.datetime(2022, 9, 30, 1, 0),
                    CompanyInternalClaimNumber="0000000002/000004",
                    DescriptionTypeOfReserve="Schadereserve Casco Materieel",
                    SectionCode=150,
                    SectionNumber=1,
                ),
                Row(
                    Amount=-3091.39,
                    BookDate=datetime.datetime(2022, 9, 19, 1, 0),
                    CompanyInternalClaimNumber="0000000002/000004",
                    DescriptionTypeOfReserve="Schadereserve WA Materieel",
                    SectionCode=150,
                    SectionNumber=1,
                ),
                Row(
                    Amount=-2951.33,
                    BookDate=datetime.datetime(2022, 9, 19, 1, 0),
                    CompanyInternalClaimNumber="0000000002/000004",
                    DescriptionTypeOfReserve="Schadereserve Casco Materieel",
                    SectionCode=110,
                    SectionNumber=2,
                ),
                Row(
                    Amount=-1170.68,
                    BookDate=datetime.datetime(2022, 9, 8, 1, 0),
                    CompanyInternalClaimNumber="0000000002/000004",
                    DescriptionTypeOfReserve="Schadereserve Casco Materieel",
                    SectionCode=120,
                    SectionNumber=3,
                ),
                Row(
                    Amount=-178908.61,
                    BookDate=datetime.datetime(2022, 8, 12, 1, 0),
                    CompanyInternalClaimNumber="0000000002/000004",
                    DescriptionTypeOfReserve="Schadereserve WA Materieel",
                    SectionCode=110,
                    SectionNumber=2,
                ),
                Row(
                    Amount=8946.39,
                    BookDate=datetime.datetime(2022, 8, 3, 1, 0),
                    CompanyInternalClaimNumber="0000000002/000004",
                    DescriptionTypeOfReserve="Schadereserve WA Materieel",
                    SectionCode=305,
                    SectionNumber=4,
                ),
                Row(
                    Amount=-353.93,
                    BookDate=datetime.datetime(2022, 7, 18, 1, 0),
                    CompanyInternalClaimNumber="0000000002/000004",
                    DescriptionTypeOfReserve="Schadereserve Casco Materieel",
                    SectionCode=145,
                    SectionNumber=6,
                ),
            ],
            schema=StructType(
                [
                    StructField("Amount", DoubleType()),
                    StructField("BookDate", TimestampType()),
                    StructField("CompanyInternalClaimNumber", StringType()),
                    StructField("DescriptionTypeOfReserve", StringType()),
                    StructField("SectionCode", LongType()),
                    StructField("SectionNumber", LongType()),
                ]
            ),
        )

    @pytest.fixture()
    def expected_df(self, spark):
        return spark.createDataFrame(
            [
                Row(
                    KeyDekkingsNummer="1",
                    KeyIdPolis=1,
                    KeyInternSchadenummer=1,
                    SectionProductCode="1100",
                    SectionReference="11100",
                    MINES_MarkedRecord=None,
                ),
                Row(
                    KeyDekkingsNummer="1",
                    KeyIdPolis=2,
                    KeyInternSchadenummer=2,
                    SectionProductCode="2110",
                    SectionReference="12110",
                    MINES_MarkedRecord=None,
                ),
                Row(
                    KeyDekkingsNummer="1",
                    KeyIdPolis=3,
                    KeyInternSchadenummer=3,
                    SectionProductCode="3113",
                    SectionReference="13113",
                    MINES_MarkedRecord="main_dedup_logic",
                ),
                Row(
                    KeyDekkingsNummer="3",
                    KeyIdPolis=4,
                    KeyInternSchadenummer=4,
                    SectionProductCode="4120",
                    SectionReference="34120",
                    MINES_MarkedRecord="main_dedup_logic",
                ),
            ],
            schema=StructType(
                [
                    StructField("KeyDekkingsNummer", StringType()),
                    StructField("KeyIdPolis", LongType()),
                    StructField("KeyInternSchadenummer", LongType()),
                    StructField("SectionProductCode", StringType()),
                    StructField("SectionReference", StringType()),
                    StructField("MINES_MarkedRecord", StringType()),
                ]
            ),
        )

    def test_custom_transformation_claim_section_nl(
        self,
        claims_df: DataFrame,
        hist_policy_version_df: DataFrame,
        hist_section_df: DataFrame,
        claim_payments_df: DataFrame,
        claim_reserves_df: DataFrame,
        expected_df: DataFrame,
    ) -> None:
        df_dict = {
            "SupportNL_Claims": claims_df,
            "SupportNL_HistPolicyVersion": hist_policy_version_df,
            "SupportNL_HistSection": hist_section_df,
            "SupportNL_ClaimPayments": claim_payments_df,
            "SupportNL_ClaimReserves": claim_reserves_df,
        }

        result_df = custom_transformation(df_dict)

        assert_dataframe_equality(result_df, expected_df)
