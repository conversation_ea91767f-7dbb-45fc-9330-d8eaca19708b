Country: custom
existCustomTransformation: 'True'

dataSource:
- name: main
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: dev_europeandatalake_01
      uat: uat_europeandatalake_01
      prod: prod_europeandatalake_01
    schema: DE_Direct
    table: Claim_Claim
    sourceSystem: SPARK_EDF_EXPORT

- name: claim_policy
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Claim
    Table: Policy

- name: claim_section
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Claim
    Table: Section

- name: claim_transaction
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Claim
    Table: Transaction

- name: claim_transaction_component
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Claim
    Table: TransactionComponent

- name: policy_section
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Policy
    Table: Section

- name: reference_reservingclasses
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Reference
    Table: ReservingClasses

- name: exchange_rate
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: test_mintdatalake_02
      uat: test_mintdatalake_02
      prod: prod_mintdatalake_02
    schema: rdm_dbo
    table: mint_mapping_roe
    sourceSystem: MINT_RAW_DATALAKE

ColumnSpecs:
  MaximumPotentialLoss:
    locale: en_US.utf8
  MaximumPotentialLossPercentage:
    locale: en_US.utf8
  ClaimsID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: DE
        - source: COLUMN
          name: KeyInternSchadenummer
        sep: ':'
  ClaimDiaryDate:
    dateTimeFormat: ISO
  ClaimLastModifiedDate:
    dateTimeFormat: ISO
  ClaimOpenDate:
    dateTimeFormat: ISO
  ClaimReportDate:
    dateTimeFormat: ISO
  CloseDate:
    dateTimeFormat: ISO
  DateOfDeclinature:
    dateTimeFormat: ISO
  DateOfLoss:
    dateTimeFormat: ISO
  PolicyYearOfAccount:
    NotInSource: True
  AttritionalLargeFlag:
    NotInSource: True
