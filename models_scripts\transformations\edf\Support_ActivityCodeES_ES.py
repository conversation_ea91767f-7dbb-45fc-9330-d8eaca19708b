import pyspark.sql.functions as F
from pyspark.sql import DataFrame

from models_scripts.transformations.common.traits import business_logic


def set_policy_id(ref_df: DataFrame, policy_df: DataFrame) -> DataFrame:
    """Creates the PolicyId field from the policy_policy DataFrame by joining it with
    the reference DataFrame.

    Args:
        ref_df (DataFrame): The reference DataFrame containing the data.
        policy_df (DataFrame): The policy DataFrame containing the policy references.

    Returns:
        DataFrame: The resulting DataFrame with the PolicyId added.
    """
    policy_df = policy_df.select("PolicyID", "PolicyReference").alias("B")
    join_clause = (
        "A.CertificateNumber = REGEXP_REPLACE(B.PolicyReference, '_\\\\d+$', '')"
    )
    df = policy_df.join(ref_df.alias("A"), F.expr(join_clause), how="left")
    return df


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    ref_df = df_dict["Reference_ActivityCodeES"]
    policy_df = df_dict["Policy_Policy"]

    output_df = set_policy_id(ref_df, policy_df)
    return output_df
