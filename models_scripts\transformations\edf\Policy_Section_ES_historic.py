import mines2.core.constants as const
import pyspark.sql.functions as F
from mines2.singlepartition.transform_functions import concat
from pyspark.sql import DataFrame

from models_scripts.transformations.common.add_columns import (
    add_column_from_match_table,
)
from models_scripts.transformations.common.traits import (
    business_logic,
    transformation,
)
from models_scripts.transformations.edf.common.currency import (
    PolicySectionCurrencyConversor,
)
from models_scripts.transformations.edf.common.keyreserving import (
    add_key_reserving_scs,
    uniform_null,
)
from models_scripts.transformations.edf.common.sisnet_scs_migration import (
    SCSPolicyUpdater,
    get_migrated_policy,
    exclude_matched_policies_in_scs_and_sisnet,
)


def add_currency_columns(
    df: DataFrame, exchange_rate_df: DataFrame, limit_df: DataFrame
) -> DataFrame:
    """Adds currency columns to the DataFrame using the exchange rate DataFrame."""
    # Create the PolicySectionID calculated column:
    columns = [
        {"source": "CONSTANT", "value": "ES"},
        {"source": "COLUMN", "name": "KeyIdPolis"},
        {"source": "COLUMN", "name": "KeyDekkingsNummer"},
    ]
    df = df.withColumn("PolicySectionID", concat(columns, ":"))

    df_with_currency_code = add_column_from_match_table(
        df,
        limit_df,
        "PolicySectionID",
        {"LimitCurrencyCode": None},
    )
    if "Deductible" not in df_with_currency_code.columns:
        df_with_currency_code = df_with_currency_code.withColumn(
            "Deductible", F.lit(None).cast("float")
        )
    output_df = PolicySectionCurrencyConversor.add_currency_columns(
        df_with_currency_code, exchange_rate_df
    )
    output_df = output_df.drop("LimitCurrencyCode", "PolicySectionID")
    return output_df


@transformation
def populate_section_product_code(
    main_df: DataFrame, policy_df: DataFrame
) -> DataFrame:
    """Populate null values in SectionProductCode column with the value of the
    PolicyProductCode column.
    """
    policy_df = policy_df.select("KeyIdPolis", "PolicyProductCode")
    merged_df = main_df.join(policy_df, on=["KeyIdPolis"], how="left")

    clean_section_product_code_col = uniform_null(F.col("SectionProductCode"))
    clean_policy_product_code_col = F.col("PolicyProductCode")

    output_df = merged_df.withColumn(
        "SectionProductCode",
        F.coalesce(clean_section_product_code_col, clean_policy_product_code_col),
    )
    return output_df


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    """Add new Policies from mapping SCS SISNET table and other transformations."""
    scs_section_df = df_dict["Section_main"].drop("source_file_name")
    standard_scs_policy_pdf = df_dict["Policy_Policy_main"].drop(
        "source_file_name",
        "source_folder_path",
        "source_modification_time",
        const.SOURCE_SYSTEM,
    )
    sisnet_policy_df = df_dict["Policy_Policy_main_ES"]
    sisnet_policy_section_df = df_dict["Policy_Section_main_ES"]
    policy_df = df_dict["Policy_Policy"]
    activity_code_df = df_dict["Support_ActivityCodeES"]
    sisnet_mapping_table_df = df_dict["Support_SisnetSCSMappingTableES"]
    exchange_rate_df = df_dict["Section_exchange_rate"]
    limit_df = df_dict["Policy_Limit"]
    mapping_df = df_dict["scs_sisnet_compare"]

    sisnet_policy_section_df = exclude_matched_policies_in_scs_and_sisnet(
        sisnet_policy_section_df, mapping_df
    )

    migrated_policies_df = get_migrated_policy(
        sisnet_policy_df, sisnet_policy_section_df, sisnet_mapping_table_df
    )
    updated_scs_section_df = (
        SCSPolicyUpdater.get_updated(
            migrated_policies_df, standard_scs_policy_pdf, scs_section_df
        )
        .select(scs_section_df.columns)
        .distinct()
    )

    output_df = add_key_reserving_scs(
        updated_scs_section_df, policy_df, activity_code_df
    )
    output_df = add_currency_columns(output_df, exchange_rate_df, limit_df)
    output_df = populate_section_product_code(output_df, policy_df)

    return output_df
