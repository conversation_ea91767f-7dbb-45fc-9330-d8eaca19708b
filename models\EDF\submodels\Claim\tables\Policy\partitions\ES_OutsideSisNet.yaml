Country: ES
existCustomTransformation: 'True'

dataSource:
- name: main
  type: SourceSisnetES
  parameters:
    sqlFileName: Claim Policy Manual ES.sql
    querySourceType: SQL_FILE
    selectColumnsFromSchema: False

- name: policy
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Policy
    Table: Policy

ColumnSpecs:
  ClaimsPolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  ClaimsID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyInternSchadenummer
        sep: ':'
  KeyInternSchadenummer:
    locale: en_US.utf8
  PolicyCode:
    locale: en_US.utf8
  PolicyReference:
    locale: en_US.utf8
  PolicyYearOfAccount:
    NotInSource: True
