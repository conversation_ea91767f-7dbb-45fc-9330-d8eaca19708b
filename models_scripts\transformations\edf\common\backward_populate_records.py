import pyspark.sql.functions as F
from pyspark.sql import Column, DataFrame
from pyspark.sql.types import StringType

COL_ID = "KeyIdPolis"
COL_DK = "KeyDekkingsNummer"
COL_COD = "TransactionComponentTypeCode"
COL_FAC = "KeyFactuurnummer"
COL_AMMOUNT = "TransactionComponentAmount"
COL_AMMOUNT_NEW = "TransactionComponentAmount_new"

COLUMNS_TO_MATCH_TO_OTHER_POLICYID = [
    "Deductible",
    "SectionProductCode",
    "SectionProductDescription",
    "SectionReference",
]

COLUMN_TO_NOT_COPY = [COL_FAC]

NEW_TRANSACTION_ID = "2"


def clean_white_spaces(df: DataFrame) -> DataFrame:
    table_schema = df.schema
    table_select_query = {
        column.name: F.col(column.name) for column in table_schema.fields
    }
    for column in table_schema.fields:
        if column.dataType == StringType():
            table_select_query[column.name] = F.trim(F.col(column.name)).alias(
                column.name
            )
    return df.select(*table_select_query.values())


def conform_to_string(df: DataFrame, columns) -> DataFrame:
    table_schema = df.schema
    table_select_query = {
        column.name: F.col(column.name) for column in table_schema.fields
    }
    for column in columns:
        assert (
            column in table_select_query.keys()
        ), f"Column {column} not found in table!"
        if table_schema[column].dataType != StringType():
            table_select_query[column] = F.col(column).cast(StringType()).alias(column)
    return df.select(*table_select_query.values())


def get_existing_dekking_df(main_df: DataFrame) -> DataFrame:
    return main_df.select(COL_ID, COL_DK).distinct().alias("existing_dekking_df")


def filter_new_transactions(
    main_df: DataFrame, extra_records_df: DataFrame
) -> DataFrame:
    main_df = conform_to_string(main_df, (COL_ID, COL_DK))
    extra_records_df = conform_to_string(extra_records_df, (COL_ID, COL_DK))
    extra_records_df = extra_records_df.select(
        COL_ID, COL_DK, "MinKeyIDPolis", "MaxKeyIDPolis"
    ).distinct()

    main_df = clean_white_spaces(main_df)
    extra_records_df = clean_white_spaces(extra_records_df)

    existing_dekking_df = get_existing_dekking_df(main_df)

    extra_records_df = extra_records_df.join(
        existing_dekking_df, on=[COL_ID, COL_DK], how="left_anti"
    )

    # Add KeyFactuurNummer
    extra_records_df = extra_records_df.withColumn(COL_FAC, F.lit(NEW_TRANSACTION_ID))

    return extra_records_df


class PopulateColumnsUsingMatchingPolicyid:
    @staticmethod
    def get_columns_to_match(main_columns: list[str]) -> list[str]:
        return [
            col for col in main_columns if col in COLUMNS_TO_MATCH_TO_OTHER_POLICYID
        ]

    @staticmethod
    def get_to_copy_df(main_df: DataFrame, columns_to_match: list[str]) -> DataFrame:
        return main_df.drop_duplicates([COL_ID, COL_DK]).select(
            *columns_to_match, COL_ID, COL_DK
        )

    @staticmethod
    def join_extra_records_df(
        extra_records_df: DataFrame,
        to_copy_upper_df: DataFrame,
        to_copy_lower_df: DataFrame,
    ) -> DataFrame:
        return extra_records_df.join(
            to_copy_upper_df,
            on=(
                (
                    F.col(f"to_copy_upper_match.{COL_ID}")
                    == F.col("extra_records.MaxKeyIDPolis")
                )
                & (
                    F.col(f"extra_records.{COL_DK}")
                    == F.col(f"to_copy_upper_match.{COL_DK}")
                )
            ),
            how="left",
        ).join(
            to_copy_lower_df,
            on=(
                (
                    F.col(f"to_copy_lower_match.{COL_ID}")
                    == F.col("extra_records.MinKeyIDPolis")
                )
                & (
                    F.col(f"extra_records.{COL_DK}")
                    == F.col(f"to_copy_lower_match.{COL_DK}")
                )
            ),
            how="left",
        )

    @staticmethod
    def get_output_columns(
        extra_record_columns: list[str],
        main_columns: list[str],
        columns_to_match: list[str],
    ) -> dict[str, Column]:
        output_columns = {
            column: F.col(f"extra_records.{column}") for column in extra_record_columns
        }
        for column in main_columns:
            if column in columns_to_match:
                output_columns[column] = (
                    F.when(
                        F.col(f"to_copy_lower_match.{COL_ID}").isNotNull(),
                        F.col(f"to_copy_lower_match.{column}"),
                    )
                    .otherwise(F.col(f"to_copy_upper_match.{column}"))
                    .alias(column)
                )
        return output_columns

    def populate(self, main_df: DataFrame, extra_records_df: DataFrame) -> DataFrame:
        main_columns = main_df.columns
        extra_record_columns = extra_records_df.columns

        columns_to_match = self.get_columns_to_match(main_columns)

        if not columns_to_match:
            return extra_records_df

        to_copy_df = self.get_to_copy_df(main_df, columns_to_match)
        to_copy_upper_df = to_copy_df.alias("to_copy_upper_match")
        to_copy_lower_df = to_copy_df.alias("to_copy_lower_match")
        extra_records_df = extra_records_df.alias("extra_records")

        joined_df = self.join_extra_records_df(
            extra_records_df, to_copy_upper_df, to_copy_lower_df
        )
        output_columns = self.get_output_columns(
            extra_record_columns, main_columns, columns_to_match
        )

        return joined_df.select(*output_columns.values())


def populate_columns_with_same_poliyid(
    main_df: DataFrame, extra_records_df: DataFrame
) -> DataFrame:
    """Populate the columns that are the same for the same policy id"""
    main_columns = main_df.columns
    extra_record_columns = extra_records_df.columns

    columns_to_copy = [
        col
        for col in main_columns
        if col not in COLUMNS_TO_MATCH_TO_OTHER_POLICYID
        and col not in COLUMN_TO_NOT_COPY
        and col not in extra_record_columns
    ]
    to_copy_df = main_df.drop_duplicates(subset=[COL_ID]).alias("to_copy")
    extra_records_df = extra_records_df.alias("extra_records")
    joined_df = extra_records_df.join(to_copy_df, on=[COL_ID], how="left")

    output_columns = {
        column: F.col(f"extra_records.{column}") for column in extra_record_columns
    }
    for column in main_columns:
        if column in columns_to_copy:
            output_columns[column] = F.col(f"to_copy.{column}")

    return joined_df.select(*output_columns.values())


def add_missing_ids(main_df: DataFrame, extra_records_df: DataFrame) -> DataFrame:
    """Add missing records added to the Policy Transaction Component on the other tables.

    The objective of this transformation is to add the ids created on
    TransactionComponent custom transformation (NL) into the Transaction Table.
    """
    extra_transactions_df = filter_new_transactions(main_df, extra_records_df)

    extra_records_populated_same_policyid_df = populate_columns_with_same_poliyid(
        main_df, extra_transactions_df
    )
    extra_records_populated_df = PopulateColumnsUsingMatchingPolicyid().populate(
        main_df, extra_records_populated_same_policyid_df
    )
    return main_df.unionByName(
        extra_records_populated_df.select(main_df.columns)
    ).distinct()
