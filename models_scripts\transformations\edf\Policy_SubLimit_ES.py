from pyspark.sql import DataFrame

from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.currency import (
    PolicySubLimitCurrencyConversor,
)


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    """The objective of this transformation is to garantee that sublimitAGG and sublimitAOC are populated."""
    sublimit_df = df_dict["SubLimit_main"]
    exchange_rate_df = df_dict["SubLimit_exchange_rate"]

    assert "SubLimit" in sublimit_df.columns, "Missing SubLimit column"
    main_df = sublimit_df.withColumnRenamed("SubLimit", "SubLimitAOC")
    output_df = main_df.withColumn("SubLimitAGG", main_df["SubLimitAOC"])
    output_df = PolicySubLimitCurrencyConversor.add_currency_columns(
        output_df, exchange_rate_df
    )

    return output_df
