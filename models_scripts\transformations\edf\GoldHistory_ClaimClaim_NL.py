import pyspark.sql.functions as F
from pyspark.sql import DataFrame

from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.small_change_dimension import (
    apply_scd2_from_long,
)


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    """Apply SCD2 on the dataframe. from long format to SCD type 2 format."""
    main_df = df_dict["Historic_ClaimStatus"]
    main_df = main_df.withColumn("ClaimStatus", F.trim(main_df["ClaimStatus"]))
    main_df = apply_scd2_from_long(main_df, "ClaimsID", "ClaimStatus", "AS_AT_DATE")
    return main_df
