from pyspark.sql import DataFrame
from pyspark.sql import functions as F

from models_scripts.transformations.common.misc import enforce_nulls_type
from models_scripts.transformations.common.traits import business_logic, transformation
from models_scripts.transformations.edf.common.add_columns import (
    get_german_country_using_policy_table,
)
from models_scripts.transformations.edf.common.currency import ClaimCurrencyConversor


@transformation
def _filter_openviva_clashing_records(
    transaction_df: DataFrame,
    transactioncomponent_df: DataFrame,
    filterout_dates: list[str],
) -> tuple[DataFrame, DataFrame]:
    # Get KeySchadeBoekingNummer from transaction_df, that has transactiondate in the given list
    filter_cond = F.col("TransactionDate").isin(filterout_dates)
    filter_out_pk = (
        transaction_df.filter(filter_cond)
        .select(F.col("KeySchadeBoekingsNummer"))
        .distinct()
    )

    # Filter out pk from transactioncomponent_df, that were found above
    output_transactioncomponent_df = transactioncomponent_df.join(
        filter_out_pk, on="KeySchadeBoekingsNummer", how="left_anti"
    )

    # Filter out unnecessary records from transaction_df
    output_transaction_df = transaction_df.filter(~filter_cond)

    return output_transactioncomponent_df, output_transaction_df


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["TransactionComponent_main"]
    policy_df = df_dict["Claim_Policy"]
    transaction_df = df_dict["Claim_Transaction"]
    exchange_rate_df = df_dict["TransactionComponent_exchange_rate"]

    # Filter out '2017-09-30', '2017-10-31', as to not have duplicates when adding de_historic
    dates_to_be_filtered = ["2017-09-30", "2017-10-31"]
    filtered_main_df, filtered_transaction_df = _filter_openviva_clashing_records(
        transaction_df, main_df, dates_to_be_filtered
    )

    component_with_converted_currencies_df = ClaimCurrencyConversor.add_new_columns(
        filtered_main_df, filtered_transaction_df, exchange_rate_df
    )

    output_df = get_german_country_using_policy_table(
        component_with_converted_currencies_df, policy_df, "KeyInternSchadenummer"
    )
    output_df = enforce_nulls_type(output_df)
    return output_df
