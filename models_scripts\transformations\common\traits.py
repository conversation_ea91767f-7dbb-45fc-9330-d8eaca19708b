"""
This module contains decorators and classes that can be used to add behavior to transformation functions.
"""

import os
import threading
from functools import wraps
from logging import Logger
from typing import Any, Callable, Iterable, Optional, overload

import mines2.core.base as base
import mines2.core.constants as const
import pyspark.sql.functions as F
from mines2.core.extensions.io import checkpoint_dataframe
from mines2.core.extensions.misc import is_dataframe
from pyspark.errors import AnalysisException
from pyspark.sql import Column, DataFrame

# Create a thread-local for the logging object
thread_local_logger = threading.local()


def initialize_logger(logger):
    global thread_local_logger
    thread_local_logger.logger = logger


def get_logger():
    return getattr(thread_local_logger, "logger", base.logger)


def _get_first_df(*args, **kwargs) -> DataFrame:
    """Get the first real argument from a tuple of arguments. By real argument, we mean the first argument that is not
    a class. So uniform functions and methods that receive a DataFrame as the first argument can be handled.
    """
    df = None
    if "df" in kwargs:
        df = kwargs["df"]
    elif args:
        if isinstance(args[0], type):
            # If the first argument is a class, the DataFrame is the second argument
            df = args[1]
        else:
            # Assuming DataFrame is the first positional argument
            df = args[0]
    else:
        raise ValueError("No DataFrame argument found.")

    if not is_dataframe(df):
        raise TypeError("The first argument must be a DataFrame.")

    return df


def business_logic(function):
    """Decorator to add business logic to a transformation function.

    Args:
        function (Callable): The original function that will be decorated with the business logic.

    Returns:
        Callable: The decorated function that adds the business logic.
    """

    @wraps(function)
    def wrapper(*args, **kwargs):
        # Add business logic here
        initialize_logger(kwargs.get("logger"))
        return function(*args, **kwargs)

    return wrapper


@overload
def transformation(function: Callable) -> Callable:
    ...
    # The purpose of this function is only to indicate the typing.


@overload
def transformation(*, force_checkpoint: bool = False) -> Callable:
    ...
    # The purpose of this function is only to indicate the typing.


# noinspection PyIncorrectDocstring
def transformation(
    function: Optional[Callable] = None, *, force_checkpoint: bool = False
) -> Callable:
    """Decorator for transformations functions.

    The added behavior is:
    - If the environment variable MINES_CHECKPOINT_RULES_DATAFRAMES is set to True, or
      if the force_checkpoint flag is set to True, the function will checkpoint the
      dataframe after execution.

    Args:
        force_checkpoint (bool, optional): Flag to force checkpointing the dataframe.
            Defaults to False.

    Returns:
        function: Decorated function.
    """

    def wrapper(*args: Any, func: Optional[Callable] = None, **kwargs: Any):
        if func is None:
            func = function

        logger = kwargs.get("logger")
        logger = logger or get_logger() or base.logger

        is_env_variable_set_true = (
            os.environ.get("MINES_CHECKPOINT_TRANSFORMATIONS", "False").lower()
            == "true"
        )
        logger.info(
            f"Starting transformation function, {func.__module__=} {func.__qualname__=}"
        )
        try:
            df = func(*args, **kwargs)
            if is_env_variable_set_true or force_checkpoint:
                df = checkpoint_dataframe(df)
        except Exception as e:
            logger.error(
                f"An error occurred while executing the transformation function, {func.__module__=} {func.__qualname__=}"
            )
            raise e
        logger.info(
            f"Successful finish transformation function, {func.__module__=} {func.__qualname__=}"
        )
        return df

    # Without arguments, `function` is passed directly to the decorator.
    if function is not None:
        assert callable(
            function
        ), "The function is not callable. Make sure you are using a keyword argument."
        return wraps(function)(wrapper)

    # With arguments, we need to return a function that accepts the function.
    # noinspection PyShadowingNames
    def decorator(function: Callable) -> Callable:
        def wrapped_function(*args, **kwargs):
            return wrapper(*args, func=function, **kwargs)

        return wraps(function)(wrapped_function)

    return decorator


def mark_records(function: Callable) -> Callable:
    """Decorator to mark records affected by transformations or validations in a DataFrame.

    Args:
        function (Callable): The original function that will be decorated with the mark_records functionality.
        The function must have a key parameter called records_marker of type RecordsMarker. eg:
        >>@mark_records
        >>def my_function(df: DataFrame, records_marker: RecordsMarker) -> DataFrame:
        >>    records_marker.mark_records(df, df['column'] > 10, "reason 1")

    Returns:
        Callable: The decorated function that adds the mark_records functionality.
    """

    def wrapper(*args, logger=None, **kwargs):
        special_cases_marker = RecordsMarker(function.__name__, logger)
        df = function(*args, **kwargs, records_marker=special_cases_marker)
        assert special_cases_marker.is_function_called, (
            f"The function {function.__name__} must call the method mark_records of the "
            "records_marker object. If no records are affected, call the method "
            "no_records_affected. To explicitly indicate the absence of affected records."
        )
        assert const.MARKED_RECORD_COLUMN_NAME in df.columns, (
            f"The function {function.__name__} must return a DataFrame with the column "
            f"{const.MARKED_RECORD_COLUMN_NAME}."
        )
        return df

    return wrapper


class RecordsMarker:
    """
    Class: RecordsMarker

    This class is used to mark records affected by transformations or validations in a DataFrame.

    Attributes:
    - function_name (str): The name of the function or process that is marking the records.
    - logger (Logger): The logger object used for logging information.

    Methods:
    - __init__(function_name: str, logger: Logger): Initializes a new instance of the RecordsMarker class.
      - function_name (str): The name of the function or process that is marking the records.
      - logger (Logger): The logger object used for logging information.

    - mark_records(df: DataFrame, cond: Column, reason: str = "") -> DataFrame:
      Mark the records affected by the transformation/validation.
      - df (DataFrame): The DataFrame on which the records will be marked.
      - cond (Column): The condition to check for each record.
      - reason (str, optional): An optional reason for the marking. Default is an empty string.
      - col_val_to_mark (str, optional): An optional column for marking/saving its value. Default is an empty string.
      - Returns: The DataFrame with the marked records.

    - no_records_affected(): Explicitly state the absence of affected records.

    - _generate_reason_col():

    - _generate_columns(df: DataFrame, reason_value: str) -> Tuple[Column, Column]:
      Generate the columns for marking the records.
      - df (DataFrame): The DataFrame on which the records will be marked.
      - reason_value (str): The reason value for marking the records.
      - Returns: A tuple containing the affected_reason_col and not_affected_reason_col columns.

    Example usage:
    # Initialize a RecordsMarker object
    marker = RecordsMarker("my_function", logger)

    # Explicitly state the absence of affected records
    marker.no_records_affected()
    """

    def __init__(self, function_name: str, logger: Logger):
        self.is_function_called = False
        self.function_name = function_name
        self.logger = logger or base.logger

    def mark_records(
        self, df: DataFrame, cond: Column, reason: str = "", col_val_to_mark: str = ""
    ) -> DataFrame:
        """Mark the records affected by the transformation/validation.
        - df (DataFrame): The DataFrame on which the records will be marked.
        - cond (Column): The condition to check for each record.
        - reason (str, optional): An optional reason for the marking. Default is an empty string.
        - col_val_to_mark (str, optional): An optional column for marking/saving its value. Default is an empty string.
        - Returns: The DataFrame with the marked records.

        Example usage:
        # Mark records in a DataFrame based on a condition, with a optional reason and optional column name to mark.
        # Will add a new column with the name defined in const.MARKED_RECORD_COLUMN_NAME.
        # The values within the new column change depending if the optional parameters are given or not.
        marked_df = marker.mark_records(df, df['column'] > 10)
        > "self.function_name"

        marked_df = marker.mark_records(df, df['column'] > 10, "reason_1")
        > "self.function_name:reason_1"

        marked_df = marker.mark_records(df, df['column'] > 10, "", "column_name_1")
        > "self.function_name:column_name_1=column_name_1.value"

        marked_df = marker.mark_records(df, df['column'] > 10, "reason 3", "column_name_2")
        > "self.function_name:reason_3:column_name_2=column_name_2.value"

        # Will append the mark text, if a previous mark is already there
        marked_df = marker.mark_records(df, df['column'] > 10, "reason_4a")
        marked_df = marker.mark_records(df, df['column'] > 10, "reason_4b")
        > "self.function_name:reason_4a && self.function_name:reason_4b"
        """
        self.is_function_called = True

        log_message = f"Marking records affected by {self.function_name}, reason: {reason or 'Not provided'}, keeping values from column: {col_val_to_mark or 'Not provided'}"
        self.logger.info(log_message)

        reason_col = self._generate_reason_col(reason, col_val_to_mark)

        affected_reason_col, not_affected_reason_col = self._generate_columns(
            df, reason_col
        )

        marked_df = df.withColumn(
            const.MARKED_RECORD_COLUMN_NAME,
            F.when(cond, affected_reason_col).otherwise(not_affected_reason_col),
        )
        return marked_df

    def no_records_affected(self) -> None:
        """Explicit the absence of affected records."""
        self.logger.info(f"No records affected by {self.function_name}")
        self.is_function_called = True

    def _generate_reason_col(self, reason: str, col_val_to_mark: str = None) -> Column:
        """Generate the columns for marking the records."""

        reason_list = [F.lit(self.function_name)]

        if reason:
            reason_list.append(F.lit(reason))
        if col_val_to_mark:
            reason_list.append(
                F.concat(F.lit(col_val_to_mark), F.lit("="), F.col(col_val_to_mark))
            )

        reason_col = F.concat_ws(":", *reason_list)
        return reason_col

    @staticmethod
    def _generate_columns(df: DataFrame, reason_col: Column) -> tuple[Column, Column]:
        """Generate the columns for marking the records."""

        if const.MARKED_RECORD_COLUMN_NAME not in df.columns:
            affected_reason_col = reason_col
            not_affected_reason_col = F.lit(None).cast("string")
        else:
            affected_reason_col = F.concat_ws(
                " && ",
                F.coalesce(F.col(const.MARKED_RECORD_COLUMN_NAME), F.lit("")),
                reason_col,
            )
            not_affected_reason_col = F.col(const.MARKED_RECORD_COLUMN_NAME)

            regex_pattern = r"(^\s*&&\s*)"
            affected_reason_col = F.regexp_replace(
                affected_reason_col, regex_pattern, ""
            )

        return affected_reason_col, not_affected_reason_col


def requires(columns: tuple | list | dict) -> Callable:
    """Decorator to set the required columns for a transformation function."""

    def decorator(func):
        def _validate_required_cols(df: DataFrame, required: list) -> None:
            missing_cols = set(required) - set(df.columns)
            assert not missing_cols, f"Missing required columns: {missing_cols}"

        def wrapper(*args, **kwargs):
            if isinstance(columns, dict):
                for dataframe_name, required_columns in columns.items():
                    assert dataframe_name in kwargs, (
                        f"Missing dataframe with required columns: {dataframe_name}, "
                        "should be passed as keyword argument."
                    )
                    _validate_required_cols(kwargs[dataframe_name], required_columns)
            else:
                if args:
                    # Handles both list and tuple by treating them as list
                    df = _get_first_df(*args, **kwargs)
                    _validate_required_cols(df, list(columns))

            return func(*args, **kwargs)

        if not isinstance(columns, (Iterable, dict)):
            raise ValueError(
                "The columns argument should be a iterable of strings or a dictionary."
            )
        return wrapper

    return decorator


def keep_original_cols(func: Callable) -> Callable:
    """Decorator to keep the original columns of a DataFrame after applying a transformation function.
    This is a guarantee that the original columns will be preserved after the transformation. without
    removal or addition of columns."""

    @wraps(func)
    def wrapper(*args, **kwargs) -> DataFrame:
        df = _get_first_df(*args, **kwargs)

        original_cols = df.columns

        result = func(*args, **kwargs)
        if not is_dataframe(result):
            raise TypeError("The transformation function must return a DataFrame.")

        # Attempt to select the original columns from the result.
        # If some columns are missing, this will raise an AnalysisException.
        try:
            return result.select(*original_cols)
        except AnalysisException as e:
            raise RuntimeError(
                "An error occurred while selecting original columns. "
                "The function might have modified the DataFrame schema in a way that "
                "original columns cannot be preserved."
            ) from e

    return wrapper
