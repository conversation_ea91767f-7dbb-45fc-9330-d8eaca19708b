from pyspark.sql import DataFrame
from pyspark.sql import functions as F

from models_scripts.transformations.common.add_columns import (
    add_column_from_match_table,
)
from models_scripts.transformations.common.misc import (
    remove_whitespace,
    replace_column_values_to_lowercase,
)


def fix_renewal_values_de(df: DataFrame) -> DataFrame:
    """Rules to define between New and Renew based on version_number.

    Fill the RenewalPolicyIndicator column:
    - if StatusCode != "lebend" then it is "cancel";
    - elif the version_number < 2 then it is "new";
    - otherwise it is "renew".
    """
    status_code_null = F.col("StatusCode").isNull()
    status_code_not_lebend = (F.col("StatusCode") != "lebend") | status_code_null
    version_number = F.element_at(F.split(F.col("KeyIdPolis"), "/"), -1).cast("int")
    df = df.withColumn(
        "RenewalPolicyIndicator",
        (
            F.when(status_code_not_lebend, "cancel")
            .when(version_number < 2, "new")
            .otherwise("renew")
        ),
    )
    return df


def fix_renewal_values_nl(df: DataFrame) -> DataFrame:
    """Fix the RenewalPolicyIndicator column values."""
    df = remove_whitespace(df, "RenewalPolicyIndicator")
    df = df.withColumn(
        "RenewalPolicyIndicator",
        F.when(F.col("RenewalPolicyIndicator") == "New", "new").otherwise("renew"),
    )
    return df


def fix_renewal_values_es(df: DataFrame) -> DataFrame:
    """Fix the RenewalPolicyIndicator column values."""
    df = df.withColumn(
        "RenewalPolicyIndicator",
        F.when(F.col("StatusCode") == "RENO", "renew").otherwise("new"),
    )
    return df


def manual_fix_renewal_values_es(df: DataFrame, df_new_renewal: DataFrame) -> DataFrame:
    """Fix the RenewalPolicyIndicator column values with the values in df_new_renewal."""
    renewal_col = "RenewalPolicyIndicator"
    renewal_col_fix = "RenewalPolicyIndicator_fix"
    df_new_renewal = df_new_renewal.select(
        F.col("KeyIdPolis"), F.col(renewal_col).alias(renewal_col_fix)
    )
    df_new_renewal = replace_column_values_to_lowercase(df_new_renewal, renewal_col_fix)
    # Replace renewal to renew:
    df_new_renewal = df_new_renewal.withColumn(
        renewal_col_fix,
        F.when(F.col(renewal_col_fix) == "renewal", "renew").otherwise(
            F.col(renewal_col_fix)
        ),
    )

    rows_number = df.count()

    # Join:
    df = add_column_from_match_table(
        df, df_new_renewal, "KeyIdPolis", {renewal_col_fix: None}
    )

    # Check if the join was one-to-one:
    assert df.count() == rows_number, (
        "The join was not one-to-one. "
        "Please check that the primary key is unique in both dataframes."
    )

    # Make coalesce:
    df = df.withColumn(
        renewal_col, F.coalesce(df[renewal_col_fix], df[renewal_col])
    ).drop(renewal_col_fix)

    return df
