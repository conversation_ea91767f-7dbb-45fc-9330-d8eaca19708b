Country: custom
existCustomTransformation: 'True'

dataSource:
- name: main
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: dev_europeandatalake_01
      uat: uat_europeandatalake_01
      prod: prod_europeandatalake_01
    schema: DE_Direct
    table: Claim_ClaimArrayTransaction
    sourceSystem: SPARK_EDF_EXPORT

- name: policy
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Claim
    Table: Policy

ColumnSpecs:
  RateOfExchange:
    locale: en_US.utf8
  ClaimsID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: DE
        - source: COLUMN
          name: KeyInternSchadenummer
        sep: ':'
  ClaimsSectionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: DE
        - source: COLUMN
          name: KeyInternSchadenummer
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  ClaimsSectionTransID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: DE
        - source: COLUMN
          name: KeyInternSchadenummer
        - source: COLUMN
          name: KeyDekkingsNummer
        - source: COLUMN
          name: KeySchadeBoekingsNummer
        sep: ':'
  TransactionAuthorisationDate:
    dateTimeFormat: ISO
  TransactionDate:
    dateTimeFormat: ISO
  TransactionNarrative:
    mapType: calculated
    calculatedValue: null
