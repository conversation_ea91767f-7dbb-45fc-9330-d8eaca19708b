Country: ES
existCustomTransformation: 'True'

dataSource:
- name: main
  type: SourceSisnetES
  parameters:
    sqlFileName: Section.sql
    querySourceType: SQL_FILE
    selectColumnsFromSchema: False

- name: policy_policy
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Policy
    Table: Policy

- name: activity_code
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Support
    Table: ActivityCodeES

- name: exchange_rate
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: test_mintdatalake_02
      uat: test_mintdatalake_02
      prod: prod_mintdatalake_02
    schema: rdm_dbo
    table: mint_mapping_roe
    sourceSystem: MINT_RAW_DATALAKE

- name: policy_limit
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Policy
    Table: Limit

ColumnSpecs:
  ClaimsSectionPolicyID:  # The same content of PolicySectionID;
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  PolicySectionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  SectionEffectiveFromDate:
    dateTimeFormat: ISO
  SectionEffectiveToDate:
    dateTimeFormat: ISO
  KeyReserving:
    NotInSource: True
