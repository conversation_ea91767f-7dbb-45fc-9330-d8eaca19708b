Country: DE
existCustomTransformation: 'False'
Incremental: 'True'

dataSource:
- name: main
  type: SourceWinsureRateAdequacy
  parameters:
    sqlFileName: RateAdequacy.sql
    querySourceType: SQL_FILE
    selectColumnsFromSchema: False

ColumnSpecs:
  POLICY_NR:
    locale: en_US.utf8
  CONTRACT_NR:
    locale: en_US.utf8
  RISK_NR:
    locale: en_US.utf8
  YEAROFACCOUNT:
    locale: en_US.utf8
  MinPrem:
    sourceName: MINPREM_ACTIV
  PRM_NO_FEES:
    locale: en_US.utf8
  RISK_DISCOUNT_OR_SURCHARGE:
    locale: en_US.utf8
  RATE_SURCHARGE:
    locale: en_US.utf8
  ADMIN_COST:
    locale: en_US.utf8
  PRM_ADJ:
    locale: en_US.utf8
  final_net_prem:
    locale: en_US.utf8
  final_tech_prem:
    locale: en_US.utf8
  tech_ratio:
    locale: en_US.utf8
