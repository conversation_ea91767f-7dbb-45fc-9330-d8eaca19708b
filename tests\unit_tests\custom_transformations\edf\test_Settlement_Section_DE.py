from mines2.core.extensions.misc import assert_dataframe_equality
from pyspark.sql import Row

from models_scripts.transformations.common.misc import TEMP_COUNTRY_COLUMN
from models_scripts.transformations.edf.Settlement_Section_DE import (
    custom_transformation,
)


def test_custom_transformation(spark):
    """End to end test for custom_transformation function."""
    # Create test data
    policy_data = [
        Row(KeyIdPolis=1, PolicyProductCode="ABC", Country="DE"),
        Row(KeyIdPolis=2, PolicyProductCode="DEF", Country="CH"),
    ]
    policy_df = spark.createDataFrame(policy_data)

    policy_array_section_data = [
        Row(KeyIdPolis=1, SectionProductCode="123"),
        Row(KeyIdPolis=2, SectionProductCode="456"),
    ]
    policy_array_section_df = spark.createDataFrame(policy_array_section_data)

    # Call the function being tested
    input_data = {
        "Policy_Policy": policy_df,
        "Section_main": policy_array_section_df,
    }
    output_df = custom_transformation(input_data)

    # Assert the output is as expected
    expected_df = spark.createDataFrame(
        [
            (1, "123", "DE", "DE:ABC:123"),
            (2, "456", "CH", "DE:DEF:456"),
        ],
        [
            "KeyIdPolis",
            "SectionProductCode",
            TEMP_COUNTRY_COLUMN,
            "KeyReserving",
        ],
    )

    assert_dataframe_equality(output_df, expected_df)
