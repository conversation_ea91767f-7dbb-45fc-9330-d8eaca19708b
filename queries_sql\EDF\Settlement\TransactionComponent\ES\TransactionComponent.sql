SELECT
    A<PERSON>POLICODE AS 'KeyIdPolis',
    B<PERSON>SECTREFE AS 'KeyDekkingsNummer',
    <PERSON><PERSON>TRANREFE AS 'KeyFactuurnummer',
    <PERSON><PERSON><PERSON>RCOADI<PERSON> AS 'TransactionComponentAdditionsDeductionsIndicator',
    <PERSON><PERSON><PERSON> AS 'TransactionComponentAmount',
    <PERSON><PERSON> AS 'TransactionComponentTypeCode',
    <PERSON><PERSON>T<PERSON> AS 'TransactionComponentTypeDescription',
    D.TRACOMPE AS 'TransactionComponentPercentage',
    D.TRACOMTE AS 'TransactionComponentTerritory'
FROM
    NTJDWHMRK..MEDFRECI A,
    NTJDWHMRK..MEDFRESE B,
    NTJDWHMRK..MEDFRETR C,
    NTJDWHMRK..MEDFRETC D
WHERE
    A.ID_MEDFRECI = B.ID_MEDFRECI_FK
    AND B.ID_MEDFRESE = C.ID_MEDFRESE_FK
    AND C.ID_MEDFRETR = D.ID_MEDFRETR_FK
    AND C.TRASDATE >= CONVERT(datetime,'01/01/2000',103)
