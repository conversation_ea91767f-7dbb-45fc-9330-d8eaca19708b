Country: ES
existCustomTransformation: 'True'

dataSource:
- name: main
  type: SourceSisnetES
  parameters:
    sqlFileName: Policy.sql
    querySourceType: SQL_FILE

- name: re_underwriting
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Reference
    Table: ReUnderwriting

- name: new_renewal
  type: SourceSisnetES
  parameters:
    sqlFileName: Policy_NewRenewalSISNET_query.sql
    querySourceType: SQL_FILE
    enforceSourceSchemaOnStandard: 'False'
    selectColumnsFromSchema: False

- name: exchange_rate
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: test_mintdatalake_02
      uat: test_mintdatalake_02
      prod: prod_mintdatalake_02
    schema: rdm_dbo
    table: mint_mapping_roe
    sourceSystem: MINT_RAW_DATALAKE

ColumnSpecs:
  ClaimsPolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  BrokerCodeID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: BrokerCode
        sep: ':'
  BrokerPostcode:
    sourceName: BrokerPostCode
  ExpiryDate:
    dateTimeFormat: ISO
  InceptionDate:
    dateTimeFormat: ISO
  PeerReview1Date:
    dateTimeFormat: ISO
  PeerReview2Date:
    dateTimeFormat: ISO
  PolicyLastModifiedDate:
    dateTimeFormat: ISO
  ReinsuredPostcode:
    sourceName: ReinsuredPostCode
  UnderwriterCodeID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: UnderwriterCode
        sep: ':'
  WrittenDate:
    dateTimeFormat: ISO
  ReUnderwritingExercise:
    NotInSource: True
  BusinessClassification:
    NotInSource: True
  PolicySequenceNumber:
    NotInSource: True
  IPTLiable:
    NotInSource: True
  AssuredAnnualTurnoverRounded:
    NotInSource: True
  AssuredAnnualTurnoverGBP:
    NotInSource: True
  AssuredAnnualTurnoverEUR:
    NotInSource: True
  AssuredAnnualTurnoverUSD:
    NotInSource: True
  AssuredAnnualTurnoverCAD:
    NotInSource: True
  AssuredAnnualTurnoverRoundedGBP:
    NotInSource: True
  AssuredAnnualTurnoverRoundedEUR:
    NotInSource: True
  AssuredAnnualTurnoverRoundedUSD:
    NotInSource: True
  AssuredAnnualTurnoverRoundedCAD:
    NotInSource: True
