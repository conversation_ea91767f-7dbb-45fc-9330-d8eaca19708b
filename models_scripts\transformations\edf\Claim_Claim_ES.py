import mines2.core.constants as const
import pyspark.sql.functions as F
from pyspark.sql import DataFrame

from models_scripts.transformations.common.add_columns import (
    add_column_from_match_table,
)
from models_scripts.transformations.common.traits import business_logic, requires
from models_scripts.transformations.edf.common.add_attritional_large_flag import (
    AttritionalLargeFlagCalculator,
)


@requires(
    {
        "main_df": ["PreviousClaimReference"],
        "adjustments_reference_df": [
            "PreviousClaimReference", "CorrectPreviousClaimReference"
        ]
    }
)
def fix_claim_ref(
    main_df: DataFrame, adjustments_reference_df: DataFrame
) -> DataFrame:
    """Fix the PreviousClaimReference for some claims."""
    df = add_column_from_match_table(
        main_df,
        adjustments_reference_df,
        "PreviousClaimReference",
        {"CorrectPreviousClaimReference": "CorrectPreviousClaimReference"},
    )

    df = df.withColumn(
        "PreviousClaimReference",
        F<PERSON>coalesce(df["CorrectPreviousClaimReference"], df["PreviousClaimReference"])
    )
    df = df.drop("CorrectPreviousClaimReference")

    return df


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    """Add new Claims from mapping SCS SISNET table and fix YOA and
    PreviousClaimReference for some claims.
    """
    main_df = df_dict["Claim_main"].drop(const.SOURCE_SYSTEM)
    sisnet_mapping_table_df = df_dict["Support_SisnetSCSMappingTableES"].drop(
        const.SOURCE_SYSTEM
    )
    adjustments_reference_df = df_dict["Reference_ManualAdjustmentESClaimReference"]

    policy_df = df_dict["Claim_Policy"].drop(const.SOURCE_SYSTEM)
    claim_section_df = df_dict["Claim_Section"].drop(const.SOURCE_SYSTEM)
    claim_transaction_df = df_dict["Claim_Transaction"].drop(const.SOURCE_SYSTEM)
    claim_transactioncomponent_df = df_dict["Claim_TransactionComponent"].drop(
        const.SOURCE_SYSTEM
    )
    policy_section_df = df_dict["Policy_Section"].drop(const.SOURCE_SYSTEM)
    reservingclass_df = df_dict["Reference_ReservingClasses"].drop(const.SOURCE_SYSTEM)

    sisnet_df = add_column_from_match_table(
        main_df,
        sisnet_mapping_table_df,
        "KeyInternSchadenummer",
        {"SCS_KeyInternSchadenummer": "KeyInternSchadenummer"},
        coalesce_existing=True,
        how="right",
    )

    sisnet_and_scs_fixed_claim_ref_df = fix_claim_ref(
        main_df=sisnet_df, adjustments_reference_df=adjustments_reference_df
    )

    enhanced_claim_df = add_column_from_match_table(
        sisnet_and_scs_fixed_claim_ref_df,
        policy_df,
        "KeyInternSchadenummer",
        {"PolicyYearOfAccount": "PolicyYearOfAccount"},
    )

    output_df = AttritionalLargeFlagCalculator(
        enhanced_claim_df,
        claim_section_df,
        claim_transaction_df,
        claim_transactioncomponent_df,
        policy_section_df,
        reservingclass_df,
        partition="ES",
    ).add_flag()

    output_df = output_df.withColumn(
        const.SOURCE_SYSTEM, F.lit("SISNET_TEMP_SOURCE_TO_BE_REPLACED")
    )

    return output_df
