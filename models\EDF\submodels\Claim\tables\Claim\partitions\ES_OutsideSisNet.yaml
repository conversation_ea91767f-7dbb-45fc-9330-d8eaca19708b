Country: ES
existCustomTransformation: 'True'

dataSource:
- name: main
  type: SourceSisnetES
  parameters:
    sqlFileName: Claim Manual.sql
    querySourceType: SQL_FILE

- name: claim_policy
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Claim
    Table: Policy

- name: claim_section
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Claim
    Table: Section

- name: claim_transaction
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Claim
    Table: Transaction

- name: claim_transaction_component
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Claim
    Table: TransactionComponent

- name: policy_section
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Policy
    Table: Section

- name: reference_reservingclasses
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Reference
    Table: ReservingClasses
    Partitions:
      - ES

- name: exchange_rate
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: test_mintdatalake_02
      uat: test_mintdatalake_02
      prod: prod_mintdatalake_02
    schema: rdm_dbo
    table: mint_mapping_roe
    sourceSystem: MINT_RAW_DATALAKE

ColumnSpecs:
  ClaimsID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyInternSchadenummer
        sep: ':'
  KeyInternSchadenummer:
    locale: en_US.utf8
  BackgroundNarrative:
    locale: en_US.utf8
  CatastropheCode:
    locale: en_US.utf8
  CatastropheDescription:
    locale: en_US.utf8
  ClaimCode:
    locale: en_US.utf8
  ClaimCountry:
    locale: en_US.utf8
  ClaimDeniedIndicator:
    locale: en_US.utf8
  ClaimDescription:
    locale: en_US.utf8
  ClaimDiaryDate:
    dateTimeFormat: ISO
    locale: en_US.utf8
  ClaimEventCode:
    locale: en_US.utf8
  ClaimEventDescription:
    locale: en_US.utf8
  ClaimHandler:
    locale: en_US.utf8
  ClaimHandlerCode:
    locale: en_US.utf8
  ClaimInsured:
    locale: en_US.utf8
  ClaimLastModifiedDate:
    dateTimeFormat: ISO
    locale: en_US.utf8
  ClaimLeadIndicator:
    locale: en_US.utf8
  ClaimLocationState:
    locale: en_US.utf8
  ClaimOpenDate:
    dateTimeFormat: ISO
    locale: en_US.utf8
  ClaimReference:
    locale: en_US.utf8
  ClaimReportDate:
    dateTimeFormat: ISO
    locale: en_US.utf8
  ClaimStatus:
    locale: en_US.utf8
  ClaimYearOfAccount:
    locale: en_US.utf8
  CloseDate:
    dateTimeFormat: ISO
    locale: en_US.utf8
  CoverageNarrative:
    locale: en_US.utf8
  CoverholderWithClaimsAuthority:
    locale: en_US.utf8
  DateOfDeclinature:
    dateTimeFormat: ISO
    locale: en_US.utf8
  DateOfLoss:
    dateTimeFormat: ISO
    locale: en_US.utf8
  GeographicalOriginOfTheClaim:
    locale: en_US.utf8
  LineageReference:
    locale: en_US.utf8
  LitigationCode:
    locale: en_US.utf8
  LitigationDescription:
    locale: en_US.utf8
  MaximumPotentialLossCurrency:
    locale: en_US.utf8
  MaximumPotentialLossPercentage:
    locale: en_US.utf8
  OriginalCurrencyCode:
    locale: en_US.utf8
  PreviousClaimReference:
    locale: en_US.utf8
  PreviousSourceSystem:
    locale: en_US.utf8
  PreviousSourceSystemDescription:
    locale: en_US.utf8
  ReasonDeclined:
    locale: en_US.utf8
  ReserveNarrative:
    locale: en_US.utf8
  ServiceProviderReference:
    locale: en_US.utf8
  SettlementCurrencyCode:
    locale: en_US.utf8
  SubrogationSalvageIndicator:
    locale: en_US.utf8
  TacticsNarrative:
    locale: en_US.utf8
  TPAHandleIndicator:
    locale: en_US.utf8
  TriageCode:
    locale: en_US.utf8
  XCSClaimRef:
    locale: en_US.utf8
  XCSClaimCode:
    locale: en_US.utf8
  PolicyYearOfAccount:
    NotInSource: True
  AttritionalLargeFlag:
    NotInSource: True
  MaximumPotentialLossRounded:
    NotInSource: True
  MaximumPotentialLossGBP:
    NotInSource: True
  MaximumPotentialLossEUR:
    NotInSource: True
  MaximumPotentialLossUSD:
    NotInSource: True
  MaximumPotentialLossCAD:
    NotInSource: True
  MaximumPotentialLossRoundedGBP:
    NotInSource: True
  MaximumPotentialLossRoundedEUR:
    NotInSource: True
  MaximumPotentialLossRoundedUSD:
    NotInSource: True
  MaximumPotentialLossRoundedCAD:
    NotInSource: True
