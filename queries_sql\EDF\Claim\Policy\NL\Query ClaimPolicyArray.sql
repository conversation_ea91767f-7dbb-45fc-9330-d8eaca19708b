select s.internschadenummer                                           as "KeyInternSchadenummer"
, h.idpolis                                                           as "KeyIdPolis"
, h.internpolisnummer + '/' + ltrim(to_char(year(ifnull(h.histhoofdpremievervaldatum, h.ingangsdatum)))) as "PolicyCode"
, h.polisnummer                                                       as "PolicyReference"
from pub.schade s
inner join pub.histpolisversie h on h.bedrijfinternschadenummer = s.bedrijfinternschadenummer
left outer join (
select a.bedrijfinternschadenummer, max(a.boekdatum) as boekdatum
from pub.schadeboeking a
  group by a.bedrijfinternschadenummer) b
  on b.bedrijfinternschadenummer = s.bedrijfinternschadenummer
left outer join (
select x.bedrijfinternschadenummer, max(x.boekdatum) as boekdatum
from pub.schadereserve x
  group by x.bedrijfinternschadenummer) m
  on m.bedrijfinternschadenummer = s.bedrijfinternschadenummer
