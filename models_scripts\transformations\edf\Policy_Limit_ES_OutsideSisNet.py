from pyspark.sql import DataFrame

from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.add_columns import (
    get_spanish_country_using_policy_table,
)
from models_scripts.transformations.edf.common.currency import (
    PolicyLimitCurrencyConversor,
    conform_euro_currency,
)
from models_scripts.transformations.edf.common.handle_limit_agg_aoc import (
    calculate_limits,
    handle_null_limits_spark,
)


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["Limit_main"]
    policy_df = df_dict["Policy_Policy"]
    exchange_rate_df = df_dict["Limit_exchange_rate"]

    main_df = conform_euro_currency(main_df, "LimitCurrencyCode")
    main_df = calculate_limits(main_df)
    main_df = handle_null_limits_spark(main_df)
    main_df = get_spanish_country_using_policy_table(main_df, policy_df)
    main_df = PolicyLimitCurrencyConversor.add_currency_columns(
        main_df, exchange_rate_df
    )

    return main_df
