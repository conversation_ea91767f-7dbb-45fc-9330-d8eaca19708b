# This is a Databricks asset bundle definition for pricingtool_databricks.
# See https://docs.databricks.com/dev-tools/bundles/index.html for documentation.
bundle:
  name: edf_databricks

# All variables that are used in the bundle need to be defined first.
# Including the variables in the targets.
variables:
  # Generic Variable Definitions (Redefined in DevOps pipeline via --var flag)
  environment_code:
    description: Environment code
    default: DEV
  edl_catalog_name:
    description: European data lake catalog name
    default: dev_europeandatalake_01
  service_principal_id:
    description: Service principal name
    default: 57c04c7a-86e9-434c-a2cc-760d5fa76c7c #mint-dev-europeandatalake-001
  spark_version:
    description: Spark version of the cluster
    default: 16.2.x-scala2.12
  jfrog_pip_artifacts_path:
    description: Path to the pip artifacts sh script in the workspace
    default: /Workspace/MINES/pip_artifacts.sh
  admin_group:
    description: Admin group for permissions management
    default: SG NMDA MINT-EUWE-DEV-EDL Data Engineers
  teams_webhook_url:
    description: Teams webhook URL
    default: 3eb9d04f-81d7-469e-a640-0917324b6992

  # Redefined in targets
  mines_root_path:
    description: Root path of the mines framework
    default: ${bundle.git.branch}
  quartz_cron_expression_ingestion: # Default is: At 00:00:00am, on the 1st day, in January, in 2099
    description: Quartz cron expression for the schedule
    default: 0 0 0 1 1 ? 2099

  ## databricks bundle deploy command is difficult to use with complex types
 # notification_emails:
 #   description: Email addresses to send notifications to
 #   type: complex
 #   default:
 #     on_failure:
 #       - <EMAIL>
 #       - <EMAIL>
 #       - <EMAIL>
 #     on_duration_warning_threshold_exceeded:
 #       - <EMAIL>
 #       - <EMAIL>

include:
  - resources/**/*.yml
  - targets/*.yml
