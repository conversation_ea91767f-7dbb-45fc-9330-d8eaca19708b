import os

import mines2.core.base as base
import mines2.core.constants as const
import pandas as pd
import pyspark.sql.functions as F
import pyspark.sql.types as T
from pyspark.sql import Column, DataFrame

# CONSTANTS
TEMP_COLUMN_PREFIX = const.TEMP_COLUMN_PREFIX
COUNTRY_COLUMN_NAME = const.COUNTRY_COLUMN_NAME
TEMP_COUNTRY_COLUMN = TEMP_COLUMN_PREFIX + COUNTRY_COLUMN_NAME

base.spark.conf.set("spark.sql.legacy.timeParserPolicy", "CORRECTED")


def reformat_datetime_col(column_name: str, patterns: list[str]) -> Column:
    """Reformat a datetime column to a specific format.

    Args:
        column_name (str): The name of the date column to be reformatted.
        patterns (list[str]): A list of datetime formats. The priority of
            the patterns is from the first to the last element in the list.

    Returns:
        Column: The reformatted date column with standardized datetime format.

    Example:
        patterns = [
            "yyyy-MM-dd h:mm:ss a",  # e.g., 2023-05-25 2:58:51 PM, 2023-06-13 10:21:27 AM
            "yyyy-MM-dd HH:mm:ss",  # e.g., 2023-09-16 00:00:00
            "yyyy-MM-dd",  # e.g., 2023-12-31
            "dd/MM/yyyy",  # e.g., 31/12/2023
            "MM/dd/yyyy",  # e.g., 12/31/2023
            "ddMMMyyyy",  # e.g., 01AUG2023
        ]
        reformatted_column = reformat_date_col("date_column", patterns)
    """
    column = F.trim(F.col(column_name))
    possible_converted_cols = [
        F.to_timestamp(column, pattern).cast("string") for pattern in patterns
    ]
    # Include the original column:
    # (if all the patterns fail, the original column will be returned)
    possible_converted_cols.append(column)
    output_col = F.coalesce(*possible_converted_cols)
    return output_col


def cast_to_int_and_to_string(df: DataFrame, column_name: str) -> DataFrame:
    """Cast the column to int (to remove decimal points) and then to string."""
    df = df.withColumn(column_name, df[column_name].cast("int").cast("string"))
    return df


def replace_column_values_to_uppercase(df: DataFrame, column_name: str) -> DataFrame:
    """Replace the column values to uppercase."""
    df = df.withColumn(column_name, F.upper(F.col(column_name)))
    return df


def replace_column_values_to_lowercase(df: DataFrame, column_name: str) -> DataFrame:
    """Replace the column values to lowercase."""
    df = df.withColumn(column_name, F.lower(F.col(column_name)))
    return df


def remove_whitespace(df: DataFrame, column_name: str | list) -> DataFrame:
    """Remove whitespace from a column."""
    if isinstance(column_name, str):
        column_name = [column_name]
    for col in column_name:
        if df.schema[col].dataType == T.StringType():
            df = df.withColumn(col, F.trim(F.col(col)))
        else:
            print(f"Column {col} is not string type, skipping...")
    return df


def filter_by_max_value(df: DataFrame, column_to_filter: str) -> DataFrame:
    """Filter data by the maximum value of a specific column."""
    max_value_df = df.agg(F.max(F.col(column_to_filter)).alias("max_value"))
    df = df.join(
        F.broadcast(max_value_df), df[column_to_filter] == max_value_df["max_value"]
    )
    return df.drop("max_value")


def get_spark_schema_from_pandas(pdf: pd.DataFrame) -> T.StructType:
    """Get the spark schema from a pandas dataframe."""
    pandas_spark_types_map = {
        "object": T.StringType(),
        "int64": T.LongType(),
        "int32": T.IntegerType(),
        "int16": T.ShortType(),
        "int8": T.ByteType(),
        "float64": T.DoubleType(),
        "float32": T.FloatType(),
        "bool": T.BooleanType(),
        "datetime64[ns]": T.TimestampType(),
        "datetime64[ms]": T.TimestampType(),
        "np.byte": T.ByteType(),
        "np.int8": T.ByteType(),
        "np.unint8": T.ByteType(),
        "np.unicode_": T.StringType(),
    }
    schema = T.StructType()
    for name, dtype in pdf.dtypes.items():
        if dtype in pandas_spark_types_map.keys():
            schema.add(str(name), pandas_spark_types_map.get(dtype, T.StringType()))
        else:
            schema.add(str(name), T.StringType())

    return schema


def enforce_nulls_type(df: DataFrame) -> DataFrame:
    selected_cols = []
    for field in df.schema:
        if field.dataType == T.NullType():
            selected_cols.append(F.lit(None).alias(field.name).cast(T.StringType()))
        else:
            selected_cols.append(F.col(field.name))
    df = df.select(selected_cols)
    return df


def pandas_to_spark(pdf: pd.DataFrame) -> DataFrame:
    """Convert a pandas dataframe to a spark dataframe."""
    schema = get_spark_schema_from_pandas(pdf)
    if len(pdf) == 0:
        df = base.spark.createDataFrame([], schema)
    else:
        df = base.spark.createDataFrame(pdf)  # Will infer schema

    return enforce_nulls_type(df)


def spark_to_pandas(df: DataFrame) -> pd.DataFrame:
    return df.toPandas()


def collect_values_from_column(df: DataFrame, column_name: str) -> list:
    """Collect the values from a column and return a list."""
    rows = df.select(column_name).collect()
    values = [row[column_name] for row in rows]
    return values


def conform_dbfs_path_for_python(path: str) -> str:
    """Conform a dbfs path to a datalake path using the FUSE mount point.

    Examples (if is in databricks):
    >>> conform_dbfs_path_for_python("dbfs:/mnt/...") # doctest: +SKIP
    '/dbfs/mnt/...'
    >>> conform_dbfs_path_for_python("/mnt/...") # doctest: +SKIP
    '/dbfs/mnt/...'
    >>> conform_dbfs_path_for_python("/dbfs/mnt/...") # doctest: +SKIP
    '/dbfs/mnt/...'
    >>> conform_dbfs_path_for_python("mnt/...") # doctest: +SKIP
    '/dbfs/mnt/...'
    """

    if base.is_databricks:
        if path.startswith("dbfs:"):
            path = path.replace("dbfs:", "/dbfs")
        elif path.startswith("/dbfs"):
            path = path
        elif path.startswith("/"):
            path = os.path.join("/dbfs", path[1:])
        else:
            path = os.path.join("/dbfs", path)
    return path
