Country: NL
existCustomTransformation: 'True'
dataSource:
- name: main
  type: SourceProgressNL
  parameters:
    sqlFileName: Query ClaimTransactionArray.sql
    querySourceType: SQL_FILE
- name: section
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Claim
    Table: Section
ColumnSpecs:
  ClaimsID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: KeyInternSchadenummer
        sep: ':'
  ClaimsSectionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: KeyInternSchadenummer
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  ClaimsSectionTransID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: KeyInternSchadenummer
        - source: COLUMN
          name: KeyDekkingsNummer
        - source: COLUMN
          name: KeySchadeBoekingsNummer
        sep: ':'
  KeyInternSchadenummer:
    locale: en_US.utf8
  KeyIdPolis:
    locale: en_US.utf8
  KeyDekkingsNummer:
    locale: en_US.utf8
  KeySchadeBoekingsNummer:
    locale: en_US.utf8
  Payee:
    locale: en_US.utf8
  RateOfExchange:
    locale: en_US.utf8
  TransactionAuthorisationDate:
    dateTimeFormat: ISO
    locale: en_US.utf8
  TransactionCurrencyCode:
    locale: en_US.utf8
  TransactionDate:
    dateTimeFormat: ISO
    locale: en_US.utf8
  TransactionNarrative:
    locale: en_US.utf8
  TransactionReference:
    locale: en_US.utf8
  TransactionSequenceNumber:
    locale: en_US.utf8
  TransactionTypeCode:
    locale: en_US.utf8
  TransactionTypeDescription:
    locale: en_US.utf8
