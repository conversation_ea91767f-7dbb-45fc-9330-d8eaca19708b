calculatedColumns:
  EarliestPolicyFlag:
      Type: LatestOrEarliestRule
      subLayer: crosspartition
      Args:
        sort_by:
          - PolicySequenceNumber
        group_by:
          - Country
          - PolicyReference
          - YearOfAccount
        mode: earliest
  LatestPolicyFlag:
      Type: LatestOrEarliestRule
      subLayer: crosspartition
      Args:
        sort_by:
          - PolicySequenceNumber
        group_by:
          - Country
          - PolicyReference
          - YearOfAccount
  LatestPolicyAssuredAnnualTurnoverFlag:
      Type: LatestOrEarliestRule
      subLayer: crosspartition
      Args:
        sort_by:
          - PolicySequenceNumber
        group_by:
          - Country
          - PolicyReference
          - YearOfAccount
        context:  AssuredAnnualTurnover > 0
  IsCurrentFlag:
    Type: LatestOrEarliestRule
    subLayer: crosspartition
    Args:
      sort_by:
        - YearOfAccount
        - PolicySequenceNumber
      group_by:
        - Country
        - PolicyReference