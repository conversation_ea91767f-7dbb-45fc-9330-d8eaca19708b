import mines2.core.constants as const
import pyspark.sql.functions as F
from pyspark.sql import DataFrame

from models_scripts.transformations.common.add_columns import (
    add_column_from_match_table,
)
from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.add_attritional_large_flag import (
    AttritionalLargeFlagCalculator,
)


def conform_datatypes(claim_df: DataFrame) -> DataFrame:
    original_cols = claim_df.columns
    update_map = {
        "ClaimLastModifiedDate": F.to_timestamp("ClaimLastModifiedDate", "dd/MM/yyyy"),
        "ClaimOpenDate": F.to_timestamp("ClaimOpenDate", "dd/MM/yyyy"),
        "ClaimReportDate": F.to_timestamp("ClaimReportDate", "dd/MM/yyyy"),
        "CloseDate": F.to_timestamp("CloseDate", "dd/MM/yyyy"),
        "ClaimLocationState": F.col("ClaimLocationState").cast("string"),
        "LitigationDescription": F.col("LitigationDescription").cast("string"),
    }
    select_query = []
    for col in original_cols:
        if col in update_map.keys():
            select_query.append(update_map[col].alias(col))
        else:
            select_query.append(F.col(col))

    return claim_df.select(select_query)


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    """
    Add new Claims from mapping SCS SISNET table and fix YOA and PreviousClaimReference for some claims
    """
    claim_df = df_dict["Claim_main"].drop(const.SOURCE_SYSTEM)
    sisnet_mapping_table_df = df_dict["Support_SisnetSCSMappingTableES"].drop(
        const.SOURCE_SYSTEM
    )
    claim_policy_df = df_dict["Claim_Policy"].drop(const.SOURCE_SYSTEM)
    claim_section_df = df_dict["Claim_Section"].drop(const.SOURCE_SYSTEM)
    claim_transaction_df = df_dict["Claim_Transaction"].drop(const.SOURCE_SYSTEM)
    claim_transactioncomponent_df = df_dict["Claim_TransactionComponent"].drop(
        const.SOURCE_SYSTEM
    )
    claim_claim_sisnet_df = df_dict["Claim_Claim_main_ES"].drop(const.SOURCE_SYSTEM)
    policy_section_df = df_dict["Policy_Section"].drop(const.SOURCE_SYSTEM)
    reservingclass_df = df_dict["Reference_ReservingClasses"].drop(const.SOURCE_SYSTEM)

    claim_conformed_df = conform_datatypes(claim_df)

    migrated_claim_df = (
        sisnet_mapping_table_df.join(
            claim_claim_sisnet_df, "KeyInternSchadenummer", "left"
        )
        .filter(F.col("SCS_KeyInternSchadenummer").isNotNull())
        .withColumn("KeyInternSchadenummer", F.col("SCS_KeyInternSchadenummer"))
    )

    migrated_claim_df = migrated_claim_df

    claim_updated_df = add_column_from_match_table(
        claim_conformed_df,
        migrated_claim_df,
        "KeyInternSchadenummer",
        {
            "ClaimLastModifiedDate": "ClaimLastModifiedDate",
            "ClaimReportDate": "ClaimReportDate",
            "LitigationCode": "LitigationCode",
            "ClaimLocationState": "ClaimLocationState",
            "LitigationDescription": "LitigationDescription",
            "ClaimStatus": "ClaimStatus",
            "CloseDate": "CloseDate",
        },
        keep_original_if_not_match=True,
    )

    enhanced_claim_df = add_column_from_match_table(
        claim_updated_df,
        claim_policy_df,
        "KeyInternSchadenummer",
        {"PolicyYearOfAccount": "PolicyYearOfAccount"},
    )

    output_df = AttritionalLargeFlagCalculator(
        enhanced_claim_df,
        claim_section_df,
        claim_transaction_df,
        claim_transactioncomponent_df,
        policy_section_df,
        reservingclass_df,
        partition="ES_historic",
    ).add_flag()

    output_df = output_df.withColumn(
        const.SOURCE_SYSTEM, F.lit("SISNET_SCS_TEMP_SOURCE_TO_BE_REPLACED")
    )

    return output_df
