
calc_prem<- function(policy_data){
policy_data$SectionId <- as.integer(policy_data$SectionId)
policy_data$Cancelled <- FALSE


policy_test<-policy_data[, list(UWYr,SectionId,IdPolis)]

# 0.1.1 Form table with all possible combinations of UWYr, Section Id and IdPolis (i.e in theory if no sections were ever cancelled)

can_pol <- policy_data %>%

  group_by(UWYr) %>%

  summarise(xy = list(purrr::cross_df(list(SectionId = SectionId , IdPolis = IdPolis)))) %>%

  tidyr::unnest(xy) %>% data.table(.) %>% unique()




#  can_pol <- CJ(policy_data$UWYr
#                , policy_data$SectionId
#                , policy_data$IdPolis
#                , unique=TRUE)
#
# colnames(can_pol) <- c("UWYr", "SectionId", "IdPolis")

# 0.1.2 Form table with actual policy versions (i.e. what was actually written)
unq_idp <- unique(policy_data[, .(UWYr, SectionId, IdPolis)])

# 0.1.3 Compare the two tables and extract those without a match (i.e. where a SectionId no longer exists for a given UWYr and IdPolis)
can_pol_sel <- can_pol[!unq_idp,, on=.(UWYr,SectionId,IdPolis)]

# 0.1.4 Add back in the Mutatiedatum for the given IdPolis
can_pol_sel_merge <- merge(can_pol_sel, unique(policy_data[, .(IdPolis, Mutatiedatum)]), by="IdPolis", all.x = TRUE)

# can_pol[policy_data, c("valid_section"):=list(.N)
#         , on = .(UWYr, SectionId)
#         , by = .EACHI]

# Flag if Section created for the first time (so if the section exists for the first time, the can delete any earlier entries)
##efficient by joining only the policy with equal YOA and Section
t2<- now()

supp_table<-policy_data[,list(IdPolis,UWYr,
                          SectionId)][can_pol_sel_merge[,list(IdPolis,UWYr,
                                                              SectionId)],
                                      on= .(UWYr, SectionId),allow=T]

can_pol_sel_merge_DDM<-merge(supp_table[IdPolis<i.IdPolis,
                                        .SD[1], list(i.IdPolis,UWYr,
                                                     SectionId)],
                             can_pol_sel_merge,
                             by.x = c("i.IdPolis","UWYr","SectionId"),
                             by.y=c("IdPolis","UWYr","SectionId"), all.y = T)

setcolorder(can_pol_sel_merge_DDM, c(1,2,3,5,4))

colnames(can_pol_sel_merge_DDM) <- c("IdPolis","UWYr","SectionId","Mutatiedatum","fp_IdPolis")
now()-t2

###inefficient
#
#t <- now()
#can_pol_sel_merge[, c("fp_IdPolis"):=find_pv(policy_data, "First",UWYr, IdPolis, SectionId)[1,1], by = seq_len(nrow(can_pol_sel_merge))]
#print(now() - t)


t3<- now()
###extract only the first date
supp_table<-policy_data[, .SD[which.min(Mutatiedatum)],
                    by = .(UWYr, SectionId)][can_pol_sel_merge_DDM,
                                             on= .(UWYr, SectionId),allow=T]

can_pol_sel_merge_DDM_KOD<-merge(supp_table[Mutatiedatum<=i.Mutatiedatum & IdPolis < i.IdPolis][, .SD[.N],
                                                                         list(i.IdPolis,UWYr,
                                                                              SectionId)][,list(i.IdPolis,
                                                                                                UWYr,
                                                                                                SectionId,
                                                                                                IdPolis)],
                                 can_pol_sel_merge_DDM,
                                 by.x = c("i.IdPolis","UWYr","SectionId"),
                                 by.y=c("IdPolis","UWYr","SectionId"),
                                 all.y = T)

setcolorder(can_pol_sel_merge_DDM_KOD, c(1,2,3,5,6,4))
colnames(can_pol_sel_merge_DDM_KOD) <- c("IdPolis","UWYr","SectionId","Mutatiedatum","fp_IdPolis", "kod_IdPolis")
now()-t3

# can_pol_sel_merge_DDM_KOD$sel_kod <- ifelse(is.na(can_pol_sel_merge_DDM_KOD$fp_IdPolis) &
#                                               is.na(can_pol_sel_merge_DDM_KOD$kod_IdPolis), NA,
#                                             ifelse(is.na(can_pol_sel_merge_DDM_KOD$fp_IdPolis),
#                                                    can_pol_sel_merge_DDM_KOD$kod_IdPolis,
#                                                    can_pol_sel_merge_DDM_KOD$fp_IdPolis))
#


# can_pol_sel_merge_DDM_KOD[, c("np_IdPolis"):=find_npv(policy_data, UWYr, IdPolis, Mutatiedatum, SectionId)[1,1], by = seq_len(nrow(can_pol_sel_merge_DDM_KOD))]

# 0.1.5 Only keep records where valid, e.g. remove records from before a new section was first added to a policy
#can_pol_sel_merge_DDM_KOD <- can_pol_sel_merge_DDM_KOD[!is.na(fp_IdPolis)]

#------ test new solution
can_pol_sel_merge_DDM_KOD <- can_pol_sel_merge_DDM_KOD[!is.na(kod_IdPolis) | !is.na(fp_IdPolis)]

#------------------






# can_pol_sel_merge_DDM_KOD <- can_pol_sel_merge_DDM_KOD[!(is.na(fp_IdPolis) & is.na(np_IdPolis))]
can_pol_sel_merge_DDM_KOD$Mutatiedatum <- NULL
can_pol_sel_merge_DDM_KOD$fp_IdPolis <- NULL
# can_pol_sel_merge_DDM_KOD$np_IdPolis <- NULL

#------ test new solution
can_pol_sel_merge_DDM_KOD$kod_IdPolis <- NULL

###excluded as combination now have been created only within same year of account
#can_pol_sel_merge_DDM_KOD <- unique(can_pol_sel_merge_DDM_KOD[, .SD[which.max(IdPolis)], by = .(UWYr, SectionId)])


#can_pol_sel_merge_DDM_KOD$sel_kod <- NULL




# 0.1.6 Complete the dataset for cancelled sections so that it can be added back into the main dataset policy_data
can_pol_sel_merge_DDM_KOD_2 <- merge(can_pol_sel_merge_DDM_KOD
  #can_pol_sel_merge_DDM_KOD
                 , unique(policy_data[, .(IdPolis, InternPolisnummer, Polisnummer, UWDt, Expiratiedatum, Expiry, Source
                                      , CreatieDatum, Mutatiedatum, Mutatiereden, Termijn, SoortPolis)])
                 , by="IdPolis", all.x = TRUE)


can_pol_sel_merge_DDM_KOD_2$Dekkingsnummer <- substr(can_pol_sel_merge_DDM_KOD_2$SectionId,
                                                     1,
                                                     nchar(can_pol_sel_merge_DDM_KOD_2$SectionId)-6)
can_pol_sel_merge_DDM_KOD_2$Productcode <- substr(can_pol_sel_merge_DDM_KOD_2$SectionId,
                                                  nchar(can_pol_sel_merge_DDM_KOD_2$SectionId)-5,
                                                  nchar(can_pol_sel_merge_DDM_KOD_2$SectionId)-3)
can_pol_sel_merge_DDM_KOD_2$Dekkingscode <- substr(can_pol_sel_merge_DDM_KOD_2$SectionId,
                                                   nchar(can_pol_sel_merge_DDM_KOD_2$SectionId)-2,
                                                   nchar(can_pol_sel_merge_DDM_KOD_2$SectionId))
can_pol_sel_merge_DDM_KOD_2$Cancelled <- TRUE

# Set amounts to zero - these will be updated later depending on the step number
can_pol_sel_merge_DDM_KOD_2[, c("GPRM", "BKR", "NPRM", "IPT"):=list(0,0,0,0)]
setcolorder(can_pol_sel_merge_DDM_KOD_2, neworder = c("IdPolis", "InternPolisnummer", "Polisnummer", "UWYr", "UWDt", "Expiratiedatum", "Expiry", "Source", "CreatieDatum", "Mutatiedatum"
                                                      , "Mutatiereden", "Termijn", "SoortPolis", "Productcode", "Dekkingscode", "Dekkingsnummer", "SectionId", "GPRM", "BKR", "NPRM"
                                                      , "IPT", "Cancelled"))

# Work out which records are definitely not valid, i.e.
# those combinations of UW Year and IdPolis that have never existed for any section
can_pol_sel_merge_DDM_KOD_2[policy_data, c("valid"):=list(.N)
                            , on = .(UWYr, IdPolis)
                            , by = .EACHI
]

if (! "valid" %in% colnames(can_pol_sel_merge_DDM_KOD_2)){ can_pol_sel_merge_DDM_KOD_2$valid <- NA}
  can_pol_sel_merge_DDM_KOD_2 <- can_pol_sel_merge_DDM_KOD_2[!is.na(valid)]
can_pol_sel_merge_DDM_KOD_2$valid <- NULL



###define first and last IDPOLIS for each section YOA before adding the cancelled one.

Min_max_sec<-policy_data[, list("min_IdPolis_sec"=min(IdPolis),
                                                            "max_IdPolis_sec"=max(IdPolis)),
                             by=.(Polisnummer, SectionId,UWYr)]


# 0.1.7 Append dummy cancelled policy versions back to policy_data
all_pol_new <- rbind(policy_data, can_pol_sel_merge_DDM_KOD_2)[order(Polisnummer, UWYr
                                         , Mutatiedatum
                                         , IdPolis)]


# ### workout if the cancelled policy should be there
# supp_table_Uw<-all_pol_new[all_pol_new[Cancelled==T,list(IdPolis,UWYr,
#                                                          SectionId, Mutatiedatum)],
#                                            on= .(UWYr),allow=T]
#
# all_pol_new<-merge(unique(supp_table_Uw[ Mutatiedatum >= i.Mutatiedatum ,
#                             list (i.IdPolis,
#                                   SectionId,
#                                   can_fut_IdPolis=IdPolis,
#                                   UWYr,
#                                   can_fut_Mutatiedatum=Mutatiedatum)])[, .SD[head(tail(seq_len(.N), 2),1)],
#                                                                        list(i.IdPolis,SectionId)],
#                    all_pol_new,
#       by.x = c("i.IdPolis","UWYr", "SectionId"),
#       by.y=c("IdPolis","UWYr", "SectionId"), all.y = T )
#
#
# all_pol_new<-all_pol_new[!(Cancelled==TRUE & Mutatiedatum > can_fut_Mutatiedatum)]
# colnames(all_pol_new)[1]<-"IdPolis"
# all_pol_new[, `:=`(can_fut_Mutatiedatum=NULL, can_fut_IdPolis=NULL)]

####calculate the correct premium for tacit or not tacit

###assign mon day and daycal for all as
all_pol_new[, `:=`(mon=-((interval(Expiry, UWDt) %/% months(1))),
            dd_mon=ifelse(day(UWDt)>day(Expiry),
                          30 - day(UWDt) + day(Expiry) ,
                          day(Expiry) - day(UWDt)))][, totdd_yoa:=pmin(360,(mon*30+dd_mon))]



all_pol_new[!SoortPolis %in% c(2,3) & Expiry <= (UWDt %m+% months(12)),  `:=`(NPRM_rev  = ((NPRM / Termijn) *mon)+  ((NPRM / Termijn)* (dd_mon/30)),
                                                 totdd=totdd_yoa,
                                        BKR_rev    = ((BKR / Termijn) *mon)+  ((BKR / Termijn)* (dd_mon/30)),
                                        IPT_rev    = ((IPT / Termijn) *mon)+  ((IPT / Termijn)* (dd_mon/30)))]

all_pol_new[!SoortPolis %in% c(2,3) & Expiry > (UWDt %m+% months(12)),  `:=`(NPRM_rev  = (NPRM / Termijn) *12,
                                          totdd=totdd_yoa,
                                          BKR_rev    = (BKR / Termijn) *12,
                                          IPT_rev    = (IPT / Termijn) *12)]

all_pol_new[is.na(NPRM_rev),`:=`(NPRM_rev=NPRM, totdd=totdd_yoa, BKR_rev=BKR,IPT_rev=IPT)]


all_pol_new[Mutatiereden >= 4000 & Mutatiereden <= 4999, `:=`(NPRM_rev  =0,
                                                              BKR_rev    =0,
                                                              IPT_rev =0)]



## 0.2 Lookup Other Policy Versions ---------------------------------------------------------------------------------------------------------------------

# Add first and last IdPolis by Policy and Underwriting Year
all_pol_new <- all_pol_new[, c("min_IdPolis", "max_IdPolis"):=list(min(IdPolis),
                                                                   max(IdPolis)),
                           by=.(Polisnummer, UWYr)]

# # Add first and last IdPolis by Policy and Underwriting Year
# all_pol_new <- all_pol_new[, c("min_IdPolis_sec", "max_IdPolis_sec"):=list(min(IdPolis),
#                                                                    max(IdPolis)),
#                            by=.(Polisnummer, SectionId,UWYr)]


# Add Year, Mutatiedatum, IdPolis, SectionId for the first version in the insurance year
# all_pol <- merge(all_pol
#                  , all_pol[IdPolis %in% unique(all_pol$min_IdPolis), .(IdPolis, Mutatiedatum, UWYr, SectionId)]
#                  , by.x = c("min_IdPolis", "SectionId"), by.y = c("IdPolis", "SectionId")
#                  , all.x = TRUE
#                  )


all_pol_new <- merge(all_pol_new
                 , unique(all_pol_new[IdPolis %in% unique(all_pol_new$min_IdPolis),
                                      .(IdPolis,SectionId, Mutatiedatum, UWYr,Ers_PRM=NPRM_rev)])
                 , by.x = c("min_IdPolis", "UWYr", "SectionId"),
                 by.y = c("IdPolis", "UWYr", "SectionId")
                 , all.x = TRUE
)


setnames(all_pol_new, c("Mutatiedatum.x"), c("Mutatiedatum"))
setnames(all_pol_new, c("Mutatiedatum.y"), c("min_Mutatiedatum"))
setcolorder(all_pol_new, neworder = c("IdPolis", "InternPolisnummer", "Polisnummer", "UWYr", "UWDt",
                                      "Expiratiedatum", "Expiry", "Source", "CreatieDatum", "Mutatiedatum"
                                  , "Mutatiereden", "Termijn", "SoortPolis", "Productcode", "Dekkingscode",
                                  "Dekkingsnummer", "SectionId", "GPRM", "BKR", "NPRM",
                                  "NPRM_rev","BKR_rev","IPT_rev", "mon", "dd_mon","totdd", "totdd_yoa",
                                  "IPT", "Cancelled", "min_IdPolis", "max_IdPolis", "min_Mutatiedatum", "Ers_PRM"))




supp_table<-all_pol_new[all_pol_new[,list(IdPolis,UWYr,
                                          SectionId, Expiry)],
                        on= .(UWYr, SectionId),allow=T] [order(IdPolis, Mutatiedatum)]

res<-merge(supp_table[IdPolis<i.IdPolis,
                                        .SD[1], list(i.IdPolis,UWYr,
                                                     SectionId)][, list (i.IdPolis,
                                                                         fp_IdPolis = IdPolis,
                                                                         UWYr,
                                                                         SectionId,
                                                                         fp_Mutatiedatum=Mutatiedatum,
                                                                         fp_Mutatiereden=Mutatiereden,
                                                                         fp_NPRM=NPRM_rev,
                                                                         fp_IPT=IPT_rev,
                                                                         fp_BKR=BKR_rev)],
           all_pol_new,
                             by.x = c("i.IdPolis","UWYr","SectionId"),
                             by.y=c("IdPolis","UWYr","SectionId"), all.y = T)



res1<-merge(supp_table[IdPolis<i.IdPolis,
                 .SD[.N], list(i.IdPolis,UWYr,
                              SectionId)][, list (i.IdPolis,
                                                  lp_IdPolis=IdPolis,
                                                  UWYr,
                                                  SectionId,
                                                  lp_Mutatiedatum=Mutatiedatum,
                                                  lp_Mutatiereden=Mutatiereden,
                                                  lp_expirydatum=Expiry,
                                                  lp_NPRM=NPRM_rev ,
                                                  lp_IPT=IPT_rev,
                                                  lp_BKR=BKR_rev)],
      res,
      by.x = c("i.IdPolis","UWYr","SectionId"),
      by.y=c("i.IdPolis","UWYr","SectionId"), all.y = T, )


supp_table_md<-all_pol_new[all_pol_new[,list(IdPolis,UWYr,
                                          SectionId, Mutatiedatum)],
                        on= .(UWYr, SectionId),allow=T][order(IdPolis, Mutatiedatum)]

# 2.  Same UW Year, lower IdPolis, higher Mutatiedatum ---------------------------------
#     --  target closest (highest) prior version, hence the [1] in the code below

res2<-merge(supp_table_md[IdPolis<i.IdPolis & Mutatiedatum > i.Mutatiedatum,
                       .SD[1], list(i.IdPolis,UWYr,
                                     SectionId)][, list (i.IdPolis,
                                                         hp_IdPolis=IdPolis,
                                                         UWYr,
                                                         SectionId,
                                                         hp_Mutatiedatum=Mutatiedatum,
                                                         hp_Mutatiereden=Mutatiereden,
                                                         hp_NPRM=NPRM_rev,
                                                         hp_IPT=IPT_rev,
                                                         hp_BKR=BKR_rev)],
            res1,
            by.x = c("i.IdPolis","UWYr","SectionId"),
            by.y=c("i.IdPolis","UWYr","SectionId"), all.y = T )



# 3.  Same UW Year, lower IdPolis, higher or equal Mutatiedatum ---------------------------------
#order by mutati and idpolis

supp_table_md<-supp_table_md[order(Mutatiedatum, - IdPolis)]


res3<-merge(supp_table_md[IdPolis<i.IdPolis & Mutatiedatum <= i.Mutatiedatum,
                          .SD[1], list(i.IdPolis,UWYr,
                                       SectionId)][, list (i.IdPolis,
                                                           ip_IdPolis=IdPolis,
                                                           UWYr,
                                                           SectionId,
                                                           ip_Mutatiedatum=Mutatiedatum,
                                                           ip_Mutatiereden=Mutatiereden,
                                                           ip_NPRM=NPRM_rev,
                                                           ip_IPT=IPT_rev,
                                                           ip_BKR=BKR_rev)],
            res2,
            by.x = c("i.IdPolis","UWYr","SectionId"),
            by.y=c("i.IdPolis","UWYr","SectionId"), all.y = T )



#### to conform step 5 pick  Same UW Year, lower IdPolis, with same of lower Mutatiedatum (hsp)---------------------------------
###index by MT datum
supp_table_md<-supp_table_md[order(Mutatiedatum, IdPolis)]


res4<-merge(supp_table_md[IdPolis < i.IdPolis &  Mutatiedatum <= i.Mutatiedatum,
                             .SD[.N], list(i.IdPolis,UWYr,
                                           SectionId)][, list (i.IdPolis,
                                                               hsp_IdPolis=IdPolis,
                                                               UWYr,
                                                               SectionId,
                                                               hsp_Mutatiedatum=Mutatiedatum,
                                                               hsp_Mutatiereden=Mutatiereden,
                                                               hsp_NPRM=NPRM_rev,
                                                               hsp_IPT=IPT_rev,
                                                               hsp_BKR=BKR_rev)],
            res3,
               by.x = c("i.IdPolis","UWYr","SectionId"),
               by.y=c("i.IdPolis","UWYr","SectionId"), all.y = T )



res5<-merge(supp_table_md[IdPolis < i.IdPolis &  Mutatiedatum < i.Mutatiedatum,
                          .SD[.N], list(i.IdPolis,UWYr,
                                        SectionId)][, list (i.IdPolis,
                                                            lsp_IdPolis=IdPolis,
                                                            UWYr,
                                                            SectionId,
                                                            lsp_Mutatiedatum=Mutatiedatum,
                                                            lsp_Mutatiereden=Mutatiereden,
                                                            lsp_NPRM=NPRM_rev,
                                                            lsp_IPT=IPT_rev,
                                                            lsp_BKR=BKR_rev)],
            res4,
            by.x = c("i.IdPolis","UWYr","SectionId"),
            by.y=c("i.IdPolis","UWYr","SectionId"), all.y = T )





colnames(res5)[1]<-"IdPolis"


##create the next inception date year column

res5[, next_inception_date:= res5$UWDt %m+% months(12)]
res5[, next_year_step7:= fifelse(is.na(Expiry   ), next_inception_date, pmin(Expiry ,next_inception_date))]
res5[, prev_year_step5:= fifelse (year(hp_Mutatiedatum)!=UWYr,pmin(next_inception_date,hp_Mutatiedatum),
                                  hp_Mutatiedatum)]



res5$step <- ifelse(res5$Cancelled==FALSE
                       , ifelse(res5$next_inception_date < res5$Mutatiedatum, 'Stap1'
                                , ifelse(res5$IdPolis == res5$min_IdPolis, 'Stap2'
                                         , ifelse(!is.na(res5$lp_IdPolis)
                                                  & res5$lp_IdPolis < res5$IdPolis
                                                  # Maybe this needs a separate if else statement?
                                                  & res5$lp_Mutatiereden >= 4000
                                                  & res5$lp_Mutatiereden <= 4999
                                                  & res5$lp_Mutatiedatum <= res5$Mutatiedatum
                                                  , 'Stap2a'
                                                  , ifelse(res5$Mutatiereden >= 4000 & res5$Mutatiereden <= 4999
                                                           , 'Stap3'
                                                           , ifelse(!is.na(res5$hp_IdPolis)
                                                                    , ifelse(res5$hp_IdPolis==res5$min_IdPolis
                                                                             , 'Stap4'
                                                                             , ifelse(!is.na(res5$ip_IdPolis) &
                                                                               res5$ip_Mutatiedatum <= res5$Mutatiedatum
                                                                                      & res5$ip_IdPolis < res5$IdPolis
                                                                                      & res5$totdd_yoa!=0
                                                                                      , 'Stap5'
                                                                                      , 'Stap6')
                                                                    )
                                                                    , ifelse(!is.na(res5$ip_IdPolis)
                                                                             , 'Stap7'
                                                                             , 'Stap8')
                                                           )
                                                  )
                                         )
                                )
                       )
                       , ifelse(res5$next_inception_date < res5$Mutatiedatum, 'Stap1V'
                                , ifelse(!is.na(res5$fp_IdPolis)
                                         , ifelse(!is.na(res5$ip_IdPolis )&
                                           res5$ip_Mutatiereden >= 4000 & res5$ip_Mutatiereden <= 4999

                                                  & res5$ip_Mutatiedatum > res5$Mutatiedatum
                                                  & res5$ip_IdPolis < res5$IdPolis
                                                  & res5$totdd_yoa!=0
                                                  , 'Stap3V'
                                                  , ifelse(!is.na(res5$hp_IdPolis)
                                                           ,ifelse(!is.na(res5$hsp_IdPolis),
                                                            'Stap5V',
                                                           'Stap6V')
                                                           , ifelse(!is.na(res5$ip_IdPolis)
                                                                    , 'Stap7V'
                                                                    , 'Stap8V')
                                                  )
                                         ), 'Stap8V'
                                )
                       )
)


####review the day and the months for the calculation of premium at each steps and add column with pickupID for edL





res5[ step=="Stap2a" , `:=`(pickupID=IdPolis , mon=-(interval(next_inception_date,Mutatiedatum) %/% months(1)),
                            dd_mon=ifelse(day(Mutatiedatum)>day(next_inception_date),
                                          30 - day(Mutatiedatum) + day(next_inception_date) ,
                                          day(next_inception_date) - day(Mutatiedatum)))][, totdd:=(mon*30+dd_mon)]


###to conform with line 832-834 of progress code need to calculate the total day between the cancelled policy and the expire of last policy

res5[step=="Stap3" & is.na(lsp_IdPolis), `:=`(mon=-(interval(lp_expirydatum,UWDt ) %/% months(1)),
                                              dd_mon=ifelse(day(UWDt  )>day(lp_expirydatum ),
                                                            30 - day(UWDt  ) + day(lp_expirydatum   ) ,
                                                            day(lp_expirydatum  ) - day(UWDt   )))][, totdd:=(mon*30+dd_mon)][, totdd_yoa:=pmin(360,(mon*30+dd_mon))]

## and then recalculate the mon, day, as for line 951-962

res5[step=="Stap3", # res5$lsp_IdPolis != res5$min_IdPolis & !is.na(lsp_IdPolis)
     `:=`(pickupID=IdPolis,
          mon=-(interval(Mutatiedatum,hsp_Mutatiedatum   ) %/% months(1)),
          dd_mon=ifelse(day(hsp_Mutatiedatum   )>day(Mutatiedatum ),
                        30 - day(hsp_Mutatiedatum   ) + day(Mutatiedatum ) ,
                        day(Mutatiedatum ) - day(hsp_Mutatiedatum   )))][, totdd:=(mon*30+dd_mon)]




res5[step=="Stap5" | step=="Stap5V", `:=`(pickupID=hp_IdPolis,
                                          mon=-(interval(prev_year_step5,Mutatiedatum) %/% months(1)),
                         dd_mon=ifelse(day(Mutatiedatum )>day( prev_year_step5),
                                       30 - day(Mutatiedatum ) + day(prev_year_step5 ) ,
                                       day(prev_year_step5) - day(Mutatiedatum  )))][, totdd:=(mon*30+dd_mon)]



res5[step=="Stap6" , `:=`(pickupID=IdPolis,
                          mon=-(interval(prev_year_step5,Mutatiedatum) %/% months(1)),
                          dd_mon=ifelse(day(Mutatiedatum )>day( prev_year_step5),
                                        30 - day(Mutatiedatum ) + day(prev_year_step5 ) ,
                                        day(prev_year_step5) - day(Mutatiedatum  )))][, totdd:=(mon*30+dd_mon)]


res5[step=="Stap7" | step=="Stap7V"   ,
     `:=`(pickupID=hsp_IdPolis,
          mon=-(interval(next_year_step7,Mutatiedatum  ) %/% months(1)),
          dd_mon=ifelse(day(Mutatiedatum )>day( next_year_step7),
                        30 - day(Mutatiedatum ) + day(next_year_step7 ) ,
                        day(next_year_step7) - day(Mutatiedatum  )))][, totdd:=(mon*30+dd_mon)]



res5[step=="Stap8" ,
     `:=`(pickupID=IdPolis,
          mon=-(interval(next_inception_date,Mutatiedatum) %/% months(1)),
          dd_mon=ifelse(day(Mutatiedatum )>day( next_inception_date),
                        30 - day(Mutatiedatum ) + day(next_inception_date ) ,
                        day(next_inception_date) - day(Mutatiedatum  )))][, totdd:=(mon*30+dd_mon)]


res5[step=="Stap3V" ,
     mon:= ifelse(year(hp_Mutatiedatum)!=UWYr,-(interval(prev_year_step5,Mutatiedatum  ) %/% months(1)),
          -(interval(next_year_step7,Mutatiedatum ) %/% months(1)))]

res5[step=="Stap3V" ,
    dd_mon:=ifelse(year(hp_Mutatiedatum)!=UWYr ,ifelse( day(Mutatiedatum )>day( prev_year_step5),
                  30 - day(Mutatiedatum ) + day(prev_year_step5 ) ,
                  day(prev_year_step5) - day(Mutatiedatum  )),ifelse(day(Mutatiedatum )>day( next_year_step7),
                        30 - day(Mutatiedatum ) + day(next_year_step7 ) ,
                        day(next_year_step7) - day(Mutatiedatum  ))) ][, totdd:=(mon*30+dd_mon)]
res5[step=="Stap3V" ,pickupID:=fp_IdPolis]

######## calculate the premium

res5[step=="Stap1" | step=="Stap1V" , `:=`(deNPREM  = 0, deBKR    = 0,   deIPT    = 0)]

res5 [step=="Stap2", `:=`(deNPREM  = NPRM_rev, deBKR    = BKR_rev,   deIPT    = IPT_rev)]


res5[step=="Stap2a" , `:=`(deNPREM = ((NPRM_rev / 12) * mon) + ((NPRM_rev / 12) * (dd_mon / 30) ),
                           deBKR   = ( (BKR_rev   / 12) * mon ) + ( (BKR_rev   / 12) * (dd_mon / 30) ),
                           deIPT   = ( (IPT_rev   / 12) * mon ) + ( (IPT_rev   / 12) * (dd_mon / 30) ))]





res5[ step=="Stap4", `:=`(deNPREM  = 0   , deBKR    = 0,   deIPT    = 0)]

res5[ step=="Stap5" | step=="Stap5V", `:=`(deNPREM = ((NPRM_rev - hsp_NPRM ) *  (totdd / totdd_yoa )) ,
                          deBKR = (BKR_rev - hsp_BKR ) *  (totdd / totdd_yoa ),
                          deIPT = (IPT_rev - hsp_IPT ) *  (totdd / totdd_yoa ))]


res5[step=="Stap6" , `:=`(deNPREM = ((NPRM_rev / 12) * mon) + ((NPRM_rev / 12) * (dd_mon / 30) ),
                           deBKR   = ( (BKR_rev   / 12) * mon ) + ( (BKR_rev   / 12) * (dd_mon / 30) ),
                           deIPT   = ( (IPT_rev   / 12) * mon ) + ( (IPT_rev   / 12) * (dd_mon / 30) ))]



res5[(step=="Stap5" | step=="Stap5V") & !is.finite(deNPREM)  ,  `:=`(deNPREM = 0,
                                                                 deBKR   = 0,
                                                                 deIPT   = 0)]

res5[ step=="Stap6V" , `:=`(deNPREM  = NPRM_rev, deBKR    = BKR_rev,   deIPT    = IPT_rev)]

res5[step=="Stap7" | step=="Stap7V"   ,  `:=`(deNPREM = ((NPRM_rev - hsp_NPRM  ) *  (totdd / totdd_yoa ) ),
                                              deBKR   =((BKR_rev - hsp_BKR  ) *  (totdd / totdd_yoa ) ),
                                              deIPT   = ((IPT_rev - hsp_IPT  ) *  (totdd / totdd_yoa ) ))]

res5[(step=="Stap7" | step=="Stap7V") & !is.finite(deNPREM)  ,  `:=`(deNPREM = 0,
                                              deBKR   = 0,
                                              deIPT   = 0)]


res5[step=="Stap8" ,  `:=`(deNPREM = ((NPRM_rev / 12) * mon) + ((NPRM_rev / 12) * (dd_mon / 30) ),
                           deBKR   = ( (BKR_rev   / 12) * mon ) + ( (BKR_rev   / 12) * (dd_mon / 30) ),
                           deIPT   = ( (IPT_rev   / 12) * mon ) + ( (IPT_rev   / 12) * (dd_mon / 30) ))]


####step to calculate 3 as: check same UWyr all policy with Mutatiedatum <= step3 mutatiedatum
#### cumsum of the newly calculated DeNPReM (delta values for each policyid sectionid)
#### multiply the cumulative sum * -1 and add the premium paid until the mutatie datum of the cancelled policy

supp_table<-res5[res5[,list(IdPolis,UWYr,
                            SectionId, Mutatiedatum, deNPREM, deBKR, deIPT,step)],
                 on= .(UWYr, SectionId),allow=T] [step=="Stap3" &
                                                    i.IdPolis<  IdPolis
                                                    #& i.Mutatiedatum<= Mutatiedatum
                                                    ]

double_canc<- merge(supp_table[is.na(i.deNPREM)], supp_table[, list(repl_prm=sum(i.deNPREM, na.rm = T),
                                                                    repl_bkr=sum(i.deBKR, na.rm = T),
                                                                    repl_ipt=sum(i.deIPT, na.rm = T)),
                                                             by=list(repl_pol=IdPolis,SectionId,UWYr) ],
      by.x=c("i.IdPolis", "SectionId","UWYr"),
      by.y=c("repl_pol", "SectionId","UWYr"))
double_canc[,`:=`(i.deNPREM = -repl_prm,
                   i.deBKR = -repl_bkr,
                   i.deIPT = -repl_ipt)][,`:=`(repl_prm=NULL,
                                              repl_bkr=NULL,
                                              repl_ipt=NULL)]
supp_table <-  rbind(supp_table[!is.na(i.deNPREM)], double_canc) [,list(uptonow_prem =sum(i.deNPREM, na.rm = T),
                                                                        uptonow_bkr =sum(i.deBKR, na.rm = T),
                                                                        uptonow_ipt =sum(i.deIPT, na.rm = T)),
                                                                  list(IdPolis,SectionId,UWYr)  ]





step3_calc<-res5[supp_table,on=.(IdPolis,SectionId,UWYr)]

step3_calc[step=="Stap3", `:=`(deNPREM= (-(uptonow_prem*totdd/totdd_yoa)+hsp_NPRM),
                               deBKR=(-(uptonow_bkr*totdd/totdd_yoa)+hsp_BKR),
                               deIPT=(-(uptonow_ipt*totdd/totdd_yoa)+hsp_IPT))]




###now get the next inception date of previous policy
step3_calc_lsp<-step3_calc[!is.na(lsp_IdPolis)]
if(nrow(step3_calc_lsp)>0){
step3_calc_lsp<-merge(step3_calc_lsp,res5[,list(IdPolis, next_inception_date, SectionId, UWYr)],
                  by.x=c("lsp_IdPolis","SectionId", "UWYr"), by.y=c("IdPolis","SectionId", "UWYr"),
                  suffixes = c("", ".y"))

step3_calc_lsp[,`:=`(mon=-(interval(next_inception_date.y,Mutatiedatum) %/% months(1)),
                dd_mon=ifelse(day(Mutatiedatum )>day( next_inception_date.y),
                              30 - day(Mutatiedatum ) + day(next_inception_date.y ) ,
                              day(next_inception_date.y) - day(Mutatiedatum  )))][, totdd:=(mon*30+dd_mon)]

step3_calc_lsp[step=="Stap3" & lsp_IdPolis !=min_IdPolis, `:=`(deNPREM =  uptonow_prem *totdd/totdd_yoa ,
                                                               deBKR= uptonow_bkr*totdd/totdd_yoa ,
                                                               deIPT=uptonow_ipt*totdd/totdd_yoa )]
}


step3_calc<-rbind(step3_calc[is.na(lsp_IdPolis)], step3_calc_lsp, fill=T)
step3_calc$deNPREM<-step3_calc$deNPREM*-1
step3_calc$deBKR <-step3_calc$deBKR *-1
step3_calc$deIPT <-step3_calc$deIPT *-1

if(nrow(step3_calc)==0){res5[step=="Stap3",`:=`(deNPREM = 0,
                                                deBKR   = 0,
                                                deIPT   = 0)]} else {
res5<-rbind(res5[step!="Stap3"], step3_calc[,`:=`(uptonow_prem=NULL,uptonow_bkr=NULL,
                                                  uptonow_ipt=NULL,next_inception_date.y=NULL)])}

fin_res<-left_join(res5,
                   Min_max_sec[, list(SectionId, UWYr, min_IdPolis_sec, max_IdPolis_sec)],
                   by=c("UWYr","SectionId"))

return(fin_res)
}
