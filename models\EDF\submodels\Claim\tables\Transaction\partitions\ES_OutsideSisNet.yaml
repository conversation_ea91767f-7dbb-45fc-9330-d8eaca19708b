Country: ES
existCustomTransformation: 'False'

dataSource:
- name: main
  type: SourceSisnetES
  parameters:
    sqlFileName: Claim Transaction ES Manual.sql
    querySourceType: SQL_FILE
    selectColumnsFromSchema: False

ColumnSpecs:
  ClaimsID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyInternSchadenummer
        sep: ':'
  ClaimsSectionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyInternSchadenummer
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  ClaimsSectionTransID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyInternSchadenummer
        - source: COLUMN
          name: KeyDekkingsNummer
        - source: COLUMN
          name: KeySchadeBoekingsNummer
        sep: ':'
  KeyInternSchadenummer:
    locale: en_US.utf8
  KeyIdPolis:
    locale: en_US.utf8
  KeyDekkingsNummer:
    locale: en_US.utf8
  KeySchadeBoekingsNummer:
    locale: en_US.utf8
  Payee:
    locale: en_US.utf8
  RateOfExchange:
    locale: en_US.utf8
  TransactionAuthorisationDate:
    dateTimeFormat: ISO
    locale: en_US.utf8
  TransactionCurrencyCode:
    locale: en_US.utf8
  TransactionDate:
    dateTimeFormat: ISO
    locale: en_US.utf8
  TransactionNarrative:
    mapType: calculated
    calculatedValue: null
  TransactionReference:
    locale: en_US.utf8
  TransactionSequenceNumber:
    locale: en_US.utf8
  TransactionTypeCode:
    locale: en_US.utf8
  TransactionTypeDescription:
    locale: en_US.utf8
