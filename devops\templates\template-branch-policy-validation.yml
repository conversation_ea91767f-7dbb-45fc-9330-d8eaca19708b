parameters:
- name: allowedSourceBranchName
  displayName: Allowed Source branch name
  type: string
  default: "refs/heads/main"

stages:
- stage: branch_policy_build_validation
  displayName: "Branch Policy Build Validation"
  jobs:
  - job: source_branch_validation
    displayName: Source Branch Validation
    steps:
      - pwsh: |
          if ('$(System.PullRequest.SourceBranch)' -ieq '${{ parameters.allowedSourceBranchName}}' -or '$(System.PullRequest.SourceBranch)' -ieq 'refs/heads/${{ parameters.allowedSourceBranchName}}') {
            Write-Host "Source branch '$(System.PullRequest.SourceBranch)' is allowed"
          } else {
            Throw "Source branch '$(System.PullRequest.SourceBranch)' is not allowed. Only the '${{ parameters.allowedSourceBranchName}}' branch is allowed."
            exit 1
          }
        displayName: "Check Build Source Branch"
        errorActionPreference: Stop
