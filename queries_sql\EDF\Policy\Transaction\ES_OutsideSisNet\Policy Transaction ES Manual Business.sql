/* QUERY POLICY_TRANSACTION MANUAL DEF */

SELECT DISTINCT
    CASE WHEN B.Certificate_number is not null THEN B.KeyIdPolis ELSE A.KeyIdPolis END AS 'KeyIdPolis',
    CASE WHEN B.Certificate_number is not null THEN CONCAT(<PERSON><PERSON>Nummer,RIGHT(<PERSON><PERSON>,4)) ELSE A.KeyDekkingsNummer END AS 'KeyDekkingsNummer',
    <PERSON><PERSON>  AS 'KeyFactuurnummer',
    A.OriginalCurrencyCode  AS 'OriginalCurrencyCode',
    A.RateOfExchange  AS 'RateOfExchange',
    A.SettlementCurrencyCode  AS 'SettlementCurrencyCode',
    A.TransactionDate  AS 'TransactionDate',
    A.TransactionReference AS 'TransactionReference',
    A.TransactionTypeCode  AS 'TransactionTypeCode',
    A.TransactionTypeDescription  AS 'TransactionTypeDescription',
    A.TransactionSequenceNumber AS 'TransactionSequenceNumber'

FROM  ( /* MANUAL DATA */
    SELECT DISTINCT
        REPLACE(CASE WHEN A.NPOLICY = '' THEN TRIM(A.POLIORIG) ELSE TRIM(A.NPOLICY) END,' ','') AS Certificate_number,
        B.YEAREFEC,
        CASE
            WHEN (CASE WHEN A.NPOLICY = '' THEN A.POLIORIG ELSE A.NPOLICY END) IS NOT NULL THEN
                CONCAT(
                    A.CODPROD
                    ,' '
                    ,(SELECT MIN(CAST(Z.YEAREFEC AS float)) FROM NTJDWHMRK..MEDLMAPS Z WHERE B.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK)
                    ,'/'
                    ,CASE WHEN A.NPOLICY = '' THEN A.POLIORIG ELSE A.NPOLICY END
                    ,'_'
                    ,(CAST(B.YEAREFEC AS float) - (SELECT MIN(CAST(Z.YEAREFEC AS float)) FROM NTJDWHMRK..MEDLMAPS Z WHERE B.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK) + 1)
                )
            WHEN (CASE WHEN A.NPOLICY = '' THEN A.POLIORIG ELSE A.NPOLICY END) IS NULL THEN
                (
                    SELECT DISTINCT POLICODE
                    FROM NTJDWHMRK..MEDFPOLI X
                    WHERE REPLACE(CASE WHEN A.NPOLICY = '' THEN TRIM(A.POLIORIG) ELSE TRIM(A.NPOLICY) END,' ','') = (LEFT(X.POLIREFE, CHARINDEX('_',X.POLIREFE)-1))
                    AND B.YEAREFEC = X.YEAOFACC
                )
        END AS 'KeyIdPolis',
        CONCAT(
            TRIM(CASE WHEN A.NPOLICY = '' THEN A.POLIORIG ELSE A.NPOLICY END),
            CASE
                WHEN A.SECREGRU = '1. Construction Professional Risks' THEN '/MA1'
                WHEN A.SECREGRU = '2. Financial Services Professional Risks' THEN '/MA2'
                WHEN A.SECREGRU = '3. Miscellaneous Professional Risks' THEN '/MA3'
                WHEN A.SECREGRU = '4. Professions Professional Risks' THEN '/MA4'
                when A.SECREGRU = '6. D and O' THEN '/MA5'
                WHEN A.SECREGRU = '11. Liability' THEN '/MA6'
                WHEN A.SECREGRU = 'Surety' THEN '/MA7'
                WHEN A.SECREGRU = 'Personal Accident' THEN '/MA8'
                WHEN A.SECREGRU = 'Private Clinics' THEN '/MA9'
                WHEN A.SECREGRU = 'Energy' THEN '/MA10'
                WHEN A.SECREGRU = 'Terror' THEN '/MA11'
            ELSE '/MAN' END
        ) AS 'KeyDekkingsNummer',
        B.ID_MEDLMAPS AS 'KeyFactuurnummer',
        CASE WHEN B.MONEDA = '' THEN 'EUR' ELSE B.MONEDA END AS 'OriginalCurrencyCode',
        '1' AS 'RateOfExchange',
        'EURO' AS 'SettlementCurrencyCode',
        B.FECHEMIS AS 'TransactionDate',
        'None' AS 'TransactionReference',
        CASE
            WHEN TIPOMOVI = 'Extorno' THEN 'EXTO'
            WHEN TIPOMOVI = 'Suplemento' THEN 'SUPL'
            WHEN TIPOMOVI = 'RenovaciÃ³n' THEN 'RENO'  /*Renovación*/
            WHEN TIPOMOVI = 'AnulaciÃ³n' THEN 'ANUL'  /*Anulación*/
            WHEN TIPOMOVI = 'RegularizaciÃ³n' THEN 'REGU'  /*Regularización*/
            WHEN TIPOMOVI = 'EmisiÃ³n' THEN 'EMIT'  /*Emisión*/
        ELSE 'None' END AS 'TransactionTypeCode',
        B.TIPOMOVI AS 'TransactionTypeDescription',
        B.ID_MEDLMAPS AS 'TransactionSequenceNumber'
    FROM NTJDWHMRK..MEDLMAPO A, NTJDWHMRK..MEDLMAPS B
    WHERE A.ID_MEDLMAPO = B.ID_MEDLMAPO_FK
) A

LEFT JOIN (
    /* SISNET DATA */
    SELECT DISTINCT
        TRIM(LEFT(B.POLIREFE, CHARINDEX('_',B.POLIREFE) - 1)) AS Certificate_number,
        B.YEAOFACC,
        B.POLICODE AS 'KeyIdPolis',
        TRIM(LEFT(C.SECTREFE, CHARINDEX('/',C.SECTREFE) - 1)) AS KeyDekkingsNummer,
        ROW_NUMBER () OVER (PARTITION BY B.POLICODE ORDER BY B.YEAOFACC DESC) AS ORDEN
        --C.SECTREFE AS 'KeyDekkingsNummer',
        --A.ID_MEDFPOTR AS 'KeyFactuurnummer',
        --A.ORICURCO AS 'OriginalCurrencyCode',
        --A.RATEOFEX AS 'RateOfExchange',
        --A.SETCURCO AS 'SettlementCurrencyCode',
        --A.TRASDATE AS 'TransactionDate',
        --A.TRANREFE AS 'TransactionReference',
        --A.TRATYPCO AS 'TransactionTypeCode',
        --A.TRATYPDE AS 'TransactionTypeDescription',
        --A.TRASEQNU AS 'TransactionSequenceNumber'
    FROM NTJDWHMRK..MEDFPOTR A, NTJDWHMRK..MEDFPOLI B, NTJDWHMRK..MEDFPOSE C
    WHERE
        B.ID_MEDFPOLI = C.ID_MEDFPOLI_FK
        AND C.ID_MEDFPOSE = A.ID_MEDFPOSE_FK
        AND B.FECHALTA >= CONVERT(datetime, '01/01/2000', 103)
) B ON TRIM(A.Certificate_number) = TRIM(B.Certificate_number) AND CAST(A.YEAREFEC AS FLOAT) = CAST(B.YEAOFACC AS FLOAT) AND B.ORDEN = 1
