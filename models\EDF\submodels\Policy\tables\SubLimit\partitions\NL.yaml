Country: NL
existCustomTransformation: 'True'

dataSource:
- name: main
  type: SourceProgressNL
  parameters:
    sqlFileName: EDF PolicyArraySubLimit Full.sql
    querySourceType: SQL_FILE
    selectColumnsFromSchema: False

- name: extra_records
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Support
    Table: NewTransactionComponents

- name: exchange_rate
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: test_mintdatalake_02
      uat: test_mintdatalake_02
      prod: prod_mintdatalake_02
    schema: rdm_dbo
    table: mint_mapping_roe
    sourceSystem: MINT_RAW_DATALAKE

ColumnSpecs:
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
          - source: CONSTANT
            value: NL
          - source: COLUMN
            name: KeyIdPolis
        sep: ":"
  PolicySubLimitID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
          - source: CONSTANT
            value: NL
          - source: COLUMN
            name: KeyIdPolis
          - source: COLUMN
            name: KeyDekkingsNummer
          - source: COLUMN
            name: SubLimitBasisCode
        sep: ":"
  PolicySectionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
          - source: CONSTANT
            value: NL
          - source: COLUMN
            name: KeyIdPolis
          - source: COLUMN
            name: KeyDekkingsNummer
        sep: ":"
  KeyIdPolis:
    locale: en_US.utf8
  KeyDekkingsNummer:
    locale: en_US.utf8
  SubLimitCurrencyCode:
    locale: en_US.utf8
  SubLimitAGG:
    NotInSource: True
  SubLimitAOC:
    NotInSource: True
