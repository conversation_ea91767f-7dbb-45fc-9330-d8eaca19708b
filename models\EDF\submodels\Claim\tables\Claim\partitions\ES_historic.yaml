Country: ES
existCustomTransformation: 'True'

dataSource:
- name: main
  type: SourceCSV
  parameters:
    fileName: EDF Claim.csv
    Separator: ','
    Encoding: UTF-8
    sourceSystem: SCS

- name: mapping_table
  type: EuropeanDatalake
  parameters:
      Layer: clean
      subModel: Support
      Table: SisnetSCSMappingTableES
      Partitions:
        - ES

- name: claim_policy
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Claim
    Table: Policy

- name: claim_section
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Claim
    Table: Section
    Partitions:
      - ES
      - ES_historic

- name: claim_claim_es
  type: EuropeanDatalake
  parameters:
    Layer: standard
    subModel: Claim
    Table: Claim_main
    Partitions:
      - ES

- name: claim_transaction
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Claim
    Table: Transaction
    Partitions:
      - ES
      - ES_historic

- name: claim_transaction_component
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Claim
    Table: TransactionComponent
    Partitions:
      - ES
      - ES_historic

- name: policy_section
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Policy
    Table: Section
    Partitions:
      - ES
      - ES_historic

- name: reference_reservingclasses
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Reference
    Table: ReservingClasses
    Partitions:
      - ES
      - ES_historic

- name: exchange_rate
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: test_mintdatalake_02
      uat: test_mintdatalake_02
      prod: prod_mintdatalake_02
    schema: rdm_dbo
    table: mint_mapping_roe
    sourceSystem: MINT_RAW_DATALAKE

ColumnSpecs:
  ClaimsID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyInternSchadenummer
        sep: ':'
  ClaimDiaryDate:
    dateTimeFormat: dd-MMM-yy
  ClaimReportDate:
    dateTimeFormat: ISO
  DateOfDeclinature:
    dateTimeFormat: dd-MMM-yy
  DateOfLoss:
    dateTimeFormat: ISO
  MaximumPotentialLoss:
    locale: en_US.utf8
  MaximumPotentialLossPercentage:
    locale: en_US.utf8
  PolicyYearOfAccount:
    NotInSource: True
  AttritionalLargeFlag:
    NotInSource: True
