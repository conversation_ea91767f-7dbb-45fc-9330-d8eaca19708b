from mines2.core.extensions.misc import assert_dataframe_equality

import models_scripts.transformations.edf.Support_ActivityCodeES_ES as transform


def test_set_policy_id(spark):
    # Create the input DataFrames
    ref_df = spark.createDataFrame(
        [
            ("001RCP", "A"),
            ("002RCP", "B"),
            ("003RCP", "C"),
        ],
        ["CertificateNumber", "ActivityCode"],
    )
    policy_df = spark.createDataFrame(
        [
            ("1", "001RCP_1"),
            ("2", "001RCP_21"),
            ("3", "002RCP_2"),
            ("4", "004RCP_1"),
        ],
        ["PolicyId", "PolicyReference"],
    )

    # Call the function to get the output DataFrame
    output_df = transform.set_policy_id(ref_df, policy_df)

    # Define the expected output DataFrame
    expected_df = spark.createDataFrame(
        [
            ("1", "001RCP_1", "001RCP", "A"),
            ("2", "001RCP_21", "001RCP", "A"),
            ("3", "002RCP_2", "002RCP", "B"),
            ("4", "004RCP_1", None, None),
        ],
        ["PolicyId", "PolicyReference", "CertificateNumber", "ActivityCode"],
    )

    # Assert that the output DataFrame is equal to the expected DataFrame
    output_df = output_df.select(expected_df.columns)
    assert_dataframe_equality(output_df, expected_df)
