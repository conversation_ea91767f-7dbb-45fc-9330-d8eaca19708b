from pyspark.sql import DataFrame

from models_scripts.transformations.common.misc import enforce_nulls_type
from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.currency import (
    PolicySubLimitCurrencyConversor,
)
from models_scripts.transformations.edf.common.sisnet_scs_migration import (
    exclude_matched_policies_in_scs_and_sisnet,
)


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    """The objective of this transformation is to garantee that sublimitAGG and sublimitAOC are populated."""
    main_df = df_dict["SubLimit_main"]
    exchange_rate_df = df_dict["SubLimit_exchange_rate"]
    mapping_df = df_dict["scs_sisnet_compare"]

    assert "SubLimit" in main_df.columns, "Missing SubLimit column"
    main_df = exclude_matched_policies_in_scs_and_sisnet(main_df, mapping_df)
    main_df = main_df.withColumnRenamed("SubLimit", "SubLimitAOC")
    main_df = main_df.withColumn("SubLimitAGG", main_df["SubLimitAOC"])
    output_df = enforce_nulls_type(main_df)
    output_df = PolicySubLimitCurrencyConversor.add_currency_columns(
        output_df, exchange_rate_df
    )

    return output_df
