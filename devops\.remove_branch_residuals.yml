trigger:
  branches:
    exclude:
    - '*'

pool:
  vmImage: ubuntu-24.04

variables:
  BranchName: $[variables['System.PullRequest.SourceBranch']]

jobs:
- job: CleanUp
  displayName: 'CleanUp'
  condition:  and(succeeded(), eq(variables['Build.Reason'], 'PullRequest'),eq(variables['System.PullRequest.TargetBranch'], 'refs/heads/develop'))
  steps:
  - task: UsePythonVersion@0
    inputs:
      versionSpec: '3.12'
  - bash: pip install databricks-sdk
    displayName: 'Install libraries'
  - task: AzureKeyVault@2
    inputs:
      azureSubscription: "mint-weu-np-paas-FullyManagedDataCenter-01 (705a45a7-fa04-4db8-9bc0-85faf7762b7f)-version2"
      KeyVaultName: 'minteuwedevkveudl01'
      SecretsFilter: 'databricks-admin-token'
      RunAsPreJob: false
  - task: PythonScript@0
    inputs:
      scriptSource: 'inline'
      script: |
        token = '$(databricks-admin-token)'
        job_id = 701300902803488
        host = "https://adb-8799184396184460.0.azuredatabricks.net/"
        branch_name = '$(BranchName)'.replace("refs/heads/","")

        from databricks.sdk import WorkspaceClient
        w = WorkspaceClient(host=host, token=token)
        run_id = w.jobs.run_now(job_id=job_id,notebook_params={"Branch":branch_name}).result()
      displayName: 'Trigger Databricks Job'