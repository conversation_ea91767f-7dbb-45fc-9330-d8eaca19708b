import pyspark.sql.functions as F
from pyspark.sql import DataFrame, Window

from models_scripts.transformations.common.add_columns import (
    add_column_from_match_table,
)
from models_scripts.transformations.common.misc import (
    enforce_nulls_type,
    pandas_to_spark,
    spark_to_pandas,
)
from models_scripts.transformations.common.traits import (
    business_logic,
    requires,
    transformation,
)
from models_scripts.transformations.edf.common.add_columns import (
    add_business_classification,
)
from models_scripts.transformations.edf.common.currency import (
    PolicyPolicyCurrencyConversor,
)
from models_scripts.transformations.edf.common.fix_renewal_values import (
    fix_renewal_values_nl,
)
from models_scripts.transformations.edf.common.re_underwriting import (
    add_re_underwriting_col,
)


def add_policy_sequence(df: DataFrame) -> DataFrame:
    window = Window.partitionBy("PolicyReference").orderBy(
        F.to_timestamp(F.col("PolicyLastModifiedDate")),
        F.col("KeyIdPolis").cast("long"),
    )
    df = df.withColumn("PolicySequenceNumber", F.row_number().over(window))
    return df


@transformation
@requires({"df": ["BrokerCode"], "broker_df": ["BrokerNumber", "IPTLiable"]})
def add_ipt_liable_col(df: DataFrame, broker_df: DataFrame) -> DataFrame:
    """Add IPT liable column to the dataframe, it comes from the NL_Direct Broker table."""
    broker_selected_df = broker_df.selectExpr("BrokerNumber AS BrokerCode", "IPTLiable")
    return add_column_from_match_table(
        df, broker_selected_df, "BrokerCode", {"IPTLiable": "IPTLiable"}
    )


def add_currency_columns(df: DataFrame, exchange_rate_df: DataFrame) -> DataFrame:
    """Adds currency columns to the DataFrame using the exchange rate DataFrame."""
    df = df.withColumnsRenamed(
        {
            "AssAnnTo": "AssuredAnnualTurnover",
            "AssAnnToCur": "AssuredAnnualTurnoverCurrency",
        }
    )
    df = PolicyPolicyCurrencyConversor.add_currency_columns(df, exchange_rate_df)
    df = df.withColumnsRenamed(
        {
            "AssuredAnnualTurnover": "AssAnnTo",
            "AssuredAnnualTurnoverCurrency": "AssAnnToCur",
        }
    )
    return df


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_pdf = spark_to_pandas(df_dict["Policy_main"])
    re_underwriting_pdf = spark_to_pandas(df_dict["Reference_ReUnderwriting"])
    broker_df = df_dict["SupportNL_Broker"]
    exchange_rate_df = df_dict["Policy_exchange_rate"]

    output_pdf = add_re_underwriting_col(main_pdf, re_underwriting_pdf)

    main_df = pandas_to_spark(output_pdf)
    main_renewal_fix_df = fix_renewal_values_nl(main_df)
    main_business_clarification_rule_df = add_business_classification(
        main_renewal_fix_df
    )
    main_policy_sequence_df = add_policy_sequence(main_business_clarification_rule_df)
    main_null_enforced_df = enforce_nulls_type(main_policy_sequence_df)

    main_ipt_liable_df = add_ipt_liable_col(
        df=main_null_enforced_df, broker_df=broker_df
    )
    output_df = add_currency_columns(main_ipt_liable_df, exchange_rate_df)

    return output_df
