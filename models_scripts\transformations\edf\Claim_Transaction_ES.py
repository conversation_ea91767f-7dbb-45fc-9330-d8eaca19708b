import mines2.core.constants as const
import pyspark.sql.functions as F
from mines2.core.constants import SOURCE_SYSTEM
from pyspark.sql import DataFrame, Window

from models_scripts.transformations.common.add_columns import (
    add_column_from_match_table,
)
from models_scripts.transformations.common.traits import business_logic, transformation


class ExtraColumnsClaimTransactionMerger:
    """Merge extra columns to main transaction df. Will Copy or calculate missing columns."""

    expected_existing_cols = (
        "KeyInternSchadenummer",
        "KeyDekkingsNummer",
        "TransactionDate",
        "TransactionTypeCode",
        "TransactionTypeDescription",
        "TransactionReference",
        "KeySchadeBoekingsNummer",
    )
    copy_cols = (
        "KeyIdPolis",
        "Payee",
        "RateOfExchange",
        "TransactionCurrencyCode",
    )
    to_calculate_cols = ("TransactionSequenceNumber",)
    to_null_cols = ("TransactionAuthorisationDate",)
    join_cols = ("KeyInternSchadenummer", "KeyDekkingsNummer")
    extra_cols = (SOURCE_SYSTEM,)

    def __init__(self, main_df: DataFrame, extra_records_df: DataFrame):
        self.main_df = main_df
        self.extra_records_df = extra_records_df
        self.main_df_columns = set(main_df.columns)
        self.extra_records_df_columns = set(extra_records_df.columns)

        self._validate_columns()

    def _validate_columns(self):
        expected_main_cols_tuple = (
            self.expected_existing_cols
            + self.copy_cols
            + self.to_calculate_cols
            + self.to_null_cols
            + self.extra_cols
        )
        expected_main_cols_set = set(expected_main_cols_tuple)

        assert len(expected_main_cols_tuple) == len(expected_main_cols_set), (
            "Duplicate columns in " "expected_main_cols_tuple"
        )
        assert (
            self.main_df_columns == expected_main_cols_set
        ), f"Expected: {expected_main_cols_set=}.\n Actual: {self.main_df_columns=}"
        assert (
            set(self.expected_existing_cols) <= self.extra_records_df_columns
        ), f"Expected: {self.expected_existing_cols=}. \nActual: {self.extra_records_df_columns=}"

    @transformation
    def append_new_records_to_main_df(self) -> DataFrame:
        """Append new records to main df."""
        with_copied_cols_df = self._copy_cols()
        with_calculated_cols_df = self._calculate_cols(with_copied_cols_df)
        with_nulls_df = self._null_cols(with_calculated_cols_df)
        union_df = self._union_dfs(with_nulls_df)
        return union_df

    def _copy_cols(self) -> DataFrame:
        """Copy columns from extra_records_df to df. Will deduplicate using window to guarantee the deterministic
        result."""
        not_in_join_cols = self.main_df_columns - set(self.join_cols)
        deduplication_window = Window.partitionBy(*self.join_cols).orderBy(
            *not_in_join_cols
        )
        deduplicated_df = (
            self.main_df.withColumn(
                "row_number", F.row_number().over(deduplication_window)
            )
            .filter("row_number = 1")
            .drop("row_number")
        )
        preprocessed_df = deduplicated_df.select(*self.join_cols, *self.copy_cols)

        joined_df = self.extra_records_df.join(
            preprocessed_df, on=list(self.join_cols), how="left"
        )
        return joined_df

    def _calculate_cols(self, extra_df: DataFrame) -> DataFrame:
        """Calculate columns that are missing in extra_records_df."""
        # Will raise warning because all the data will be on a single partition. But the dataframe is small so it's ok.
        sequential_col = F.row_number().over(Window.orderBy(*extra_df.columns))

        max_sequence_number = self.main_df.select(
            F.max("TransactionSequenceNumber")
        ).collect()[0][0]
        calc_sequence_number = F.lit(max_sequence_number) + sequential_col

        sequence_number_col = calc_sequence_number.alias("TransactionSequenceNumber")

        return extra_df.select(
            *extra_df.columns,
            sequence_number_col,
        )

    def _null_cols(self, extra_df: DataFrame) -> DataFrame:
        """Null columns that are missing in extra_records_df."""
        null_columns = [F.lit(None).alias(col) for col in self.to_null_cols]
        return extra_df.select(*extra_df.columns, *null_columns)

    def _union_dfs(self, extra_df: DataFrame) -> DataFrame:
        """Union df and extra_records_df."""
        return self.main_df.unionByName(extra_df.select(*self.main_df_columns))


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    """
    Custom transformation for Claim_Transaction_ES.
    """
    main_df = df_dict["Transaction_main"]
    sisnet_mapping_table_df = df_dict["Support_SisnetSCSMappingTableES"]
    extra_records_df = df_dict["Support_ExtraTransactionsAdjustmentsES"]

    merger = ExtraColumnsClaimTransactionMerger(main_df, extra_records_df)
    main_with_new_records_df = merger.append_new_records_to_main_df()

    # Add new Claims from mapping SCS SISNET table
    new_claims_df = add_column_from_match_table(
        main_with_new_records_df,
        sisnet_mapping_table_df,
        ["KeyInternSchadenummer", "KeyIdPolis"],
        {
            "SCS_KeyInternSchadenummer": "KeyInternSchadenummer",
            "SCS_KeyIdPolis": "KeyIdPolis",
            "SCS_KeyDekkingsNummer": "KeyDekkingsNummer",
        },
        coalesce_existing=True,
        how="right",
    )

    return new_claims_df.withColumn(
        const.SOURCE_SYSTEM, F.lit("SISNET_SCS_TEMP_SOURCE_TO_BE_REPLACED")
    )
