SELECT DISTINCT
    A.POLIZTRA CertificateNumber
   ,CASE
        WHEN A.POLIZTRA = '021S01206RCP' THEN 'GRCP02'
        WHEN A.PRODUCTO = 'AC03' AND A.POLIZTRA <> '021S01206RCP' THEN ACT_CONV.GRUPO
        WHEN A.PRODUCTO IN ('AC01','AC02','AC05') AND A.POLIZTRA <> '021S01206RCP' THEN ACT_NO_CONV.GRUPO
        ELSE ACT.GRUPO
    END AS 'GrupoActividad'

   ,CASE
        WHEN A.POLIZTRA = '021S01206RCP' THEN 'ACTP0214'
        WHEN A.PRODUCTO = 'AC03' AND A.POLIZTRA <> '021S01206RCP' THEN ACT_CONV.ACTIVI
        WHEN A.PRODUCTO IN ('AC01','AC02','AC05') AND A.POLIZTRA <> '021S01206RCP' THEN ACT_NO_CONV.ACTIVI
        ELSE ACT.ACTIVI
    END AS 'CodActividad'

   ,CASE
        WHEN A.POLIZTRA = '021S01206RCP' THEN 'Consultores financieros (no asesores financieros)'
        WHEN A.PRODUCTO = 'AC03' AND A.POLIZTRA <> '021S01206RCP' THEN ACT_CONV.DESCRIPCION
        WHEN A.PRODUCTO IN ('AC01','AC02','AC05') AND A.POLIZTRA <> '021S01206RCP' THEN ACT_NO_CONV.DESCRIPCION
        ELSE ACT.DESCRIPCION
    END AS 'DescActividad'

FROM DPOLIZAS A
    LEFT JOIN DPOLSCON B ON A.ID_DPOLIZAS = B.ID_DPOLIZAS_FK
    LEFT JOIN DPOLPER C ON B.DPOLPER_SCON = C.ID_DPOLSCON_FK
    LEFT JOIN (
        SELECT
            A.POLIZTRA
            ,D.VALOR GRUPO
            ,E.VALOR ACTIVI
            ,F.DESCRIPCION
            ,ROW_NUMBER() over (PARTITION BY POLIZTRA ORDER BY E.VALOR DESC) as RowNo
        FROM DPOLIZAS A
            LEFT JOIN DPOLSCON B ON B.ID_DPOLSCON = A.ID_DPOLSCON_FK
            LEFT JOIN DPOLRIES C ON C.ID_DPOLSCON_FK = B.DPOLRIES_SCON
            LEFT JOIN DPOLRIDA D ON D.ID_DPOLRIES_FK = C.ID_DPOLRIES AND D.NOMBDATO = 'COMBICOD'
            LEFT JOIN DPOLRIDA E ON E.ID_DPOLRIES_FK = C.ID_DPOLRIES AND E.NOMBDATO = 'CODIACTI'
            LEFT JOIN DPROCOME F ON F.CODIGO = E.VALOR
        ) ACT ON ACT.POLIZTRA = A.POLIZTRA AND ACT.RowNo=1
    LEFT JOIN (
        SELECT
            A.POLIZTRA
            ,D.VALOR GRUPO
            ,E.VALOR ACTIVI
            ,F.DESCRIPCION
            ,ROW_NUMBER() over (PARTITION BY POLIZTRA ORDER BY E.VALOR DESC) as RowNo
        FROM DPOLIZAS A
            LEFT JOIN DPOLSCON B ON B.ID_DPOLSCON = A.ID_DPOLSCON_FK
            LEFT JOIN DPOLPECO C ON C.ID_DPOLSCON_FK = B.DPOLPECO_SCON AND RELACION != 'TOMA'
            LEFT JOIN DPOLPEDA D ON D.ID_DPOLPECO_FK = C.ID_DPOLPECO AND D.NOMBDATO = 'COMBICOD'
            LEFT JOIN DPOLPEDA E ON E.ID_DPOLPECO_FK = C.ID_DPOLPECO AND E.NOMBDATO = 'CODICONV'
            LEFT JOIN DPROCOME F ON F.CODIGO = E.VALOR
        WHERE A.PRODUCTO = 'AC03'
        ) ACT_CONV ON ACT_CONV.POLIZTRA = A.POLIZTRA AND ACT_CONV.RowNo=1
    LEFT JOIN (
        SELECT
            A.POLIZTRA
            ,D.VALOR GRUPO
            ,CONCAT(E.VALOR,'-',F.VALOR) ACTIVI
            ,G.VALOR DESCRIPCION
            ,ROW_NUMBER() over (PARTITION BY POLIZTRA ORDER BY E.VALOR DESC) as RowNo
        FROM DPOLIZAS A
            LEFT JOIN DPOLSCON B ON B.ID_DPOLSCON = A.ID_DPOLSCON_FK
            LEFT JOIN DPOLPECO C ON C.ID_DPOLSCON_FK = B.DPOLPECO_SCON AND RELACION != 'TOMA'
            LEFT JOIN DPOLPEDA D ON D.ID_DPOLPECO_FK = C.ID_DPOLPECO AND D.NOMBDATO = 'COMBICOD'
            LEFT JOIN DPOLPEDA E ON E.ID_DPOLPECO_FK = C.ID_DPOLPECO AND E.NOMBDATO = 'GRUPOCUP'
            LEFT JOIN DPOLPEDA F ON F.ID_DPOLPECO_FK = C.ID_DPOLPECO AND F.NOMBDATO = 'SUBGRUOC'
            LEFT JOIN DPOLPEDA G ON G.ID_DPOLPECO_FK = C.ID_DPOLPECO AND G.NOMBDATO = 'NOMCOMOC'
        WHERE A.PRODUCTO IN ('AC01','AC02','AC05')
        ) ACT_NO_CONV ON ACT_NO_CONV.POLIZTRA = A.POLIZTRA AND ACT_NO_CONV.RowNo=1
