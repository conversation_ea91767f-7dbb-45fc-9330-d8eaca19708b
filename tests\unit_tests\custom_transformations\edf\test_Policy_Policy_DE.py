from datetime import date

from mines2.core.extensions.misc import assert_dataframe_equality
from pyspark.sql import Row

import models_scripts.transformations.edf.Policy_Policy_DE as transform


def test_fix_renewal_values_de(spark):
    main_df = spark.createDataFrame(
        [
            Row(
                PolicyID=1,
                StatusCode="storniert",
                WrittenDate="2018-01-01",
                InceptionDate="2022-01-01",
                KeyIdPolis="1/1",
            ),
            Row(
                PolicyID=2,
                StatusCode="lebend",
                WrittenDate="2018-01-01",
                InceptionDate="2018-01-01",
                KeyIdPolis="2/1",
            ),
            Row(
                PolicyID=3,
                StatusCode="lebend",
                WrittenDate="2018-01-01",
                InceptionDate="2022-01-01",
                KeyIdPolis="3/3",
            ),
            Row(
                PolicyID=4,
                StatusCode=None,
                WrittenDate="2018-01-01",
                InceptionDate="2018-01-01",
                KeyIdPolis="4/1",
            ),
            Row(
                PolicyID=5,
                StatusCode=None,
                WrittenDate="2018-01-01",
                InceptionDate="2019-01-01",
                KeyIdPolis="5/2",
            ),
            Row(
                PolicyID=6,
                StatusCode="lebend",
                WrittenDate="2018-01-01",
                InceptionDate="2018-01-01",
                KeyIdPolis="6/6/1",
            ),
            Row(
                PolicyID=7,
                StatusCode="lebend",
                WrittenDate="2018-01-01",
                InceptionDate="2018-01-01",
                KeyIdPolis=None,
            ),
            Row(
                PolicyID=8,
                StatusCode="lebend",
                WrittenDate="2018-01-01",
                InceptionDate="2018-01-01",
                KeyIdPolis="",
            ),
        ]
    )
    expected_df = spark.createDataFrame(
        [
            Row(
                PolicyID=1,
                StatusCode="storniert",
                WrittenDate="2018-01-01",
                InceptionDate="2022-01-01",
                KeyIdPolis="1/1",
                RenewalPolicyIndicator="cancel",
            ),
            Row(
                PolicyID=2,
                StatusCode="lebend",
                WrittenDate="2018-01-01",
                InceptionDate="2018-01-01",
                KeyIdPolis="2/1",
                RenewalPolicyIndicator="new",
            ),
            Row(
                PolicyID=3,
                StatusCode="lebend",
                WrittenDate="2018-01-01",
                InceptionDate="2022-01-01",
                KeyIdPolis="3/3",
                RenewalPolicyIndicator="renew",
            ),
            Row(
                PolicyID=4,
                StatusCode=None,
                WrittenDate="2018-01-01",
                InceptionDate="2018-01-01",
                KeyIdPolis="4/1",
                RenewalPolicyIndicator="cancel",
            ),
            Row(
                PolicyID=5,
                StatusCode=None,
                WrittenDate="2018-01-01",
                InceptionDate="2019-01-01",
                KeyIdPolis="5/2",
                RenewalPolicyIndicator="cancel",
            ),
            Row(
                PolicyID=6,
                StatusCode="lebend",
                WrittenDate="2018-01-01",
                InceptionDate="2018-01-01",
                KeyIdPolis="6/6/1",
                RenewalPolicyIndicator="new",
            ),
            Row(
                PolicyID=7,
                StatusCode="lebend",
                WrittenDate="2018-01-01",
                InceptionDate="2018-01-01",
                KeyIdPolis=None,
                RenewalPolicyIndicator="renew",
            ),
            Row(
                PolicyID=8,
                StatusCode="lebend",
                WrittenDate="2018-01-01",
                InceptionDate="2018-01-01",
                KeyIdPolis="",
                RenewalPolicyIndicator="renew",
            ),
        ]
    )
    output_df = transform.fix_renewal_values_de(main_df)
    assert_dataframe_equality(output_df, expected_df)


def test_policysequencenumber_de(spark):
    main_df = spark.createDataFrame(
        [
            Row(
                PolicyReference=1,
                KeyIdPolis=10,
                PolicyLastModifiedDate=date(2018, 1, 1),
            ),
            Row(
                PolicyReference=1,
                KeyIdPolis=20,
                PolicyLastModifiedDate=date(2019, 1, 1),
            ),
            Row(
                PolicyReference=1,
                KeyIdPolis=40,
                PolicyLastModifiedDate=date(2021, 1, 1),
            ),
            Row(
                PolicyReference=1,
                KeyIdPolis=30,
                PolicyLastModifiedDate=date(2020, 1, 1),
            ),
            Row(
                PolicyReference=2,
                KeyIdPolis=1,
                PolicyLastModifiedDate=date(2020, 1, 1),
            ),
            Row(
                PolicyReference=2,
                KeyIdPolis=2,
                PolicyLastModifiedDate=date(2022, 1, 1),
            ),
            Row(
                PolicyReference=2,
                KeyIdPolis=3,
                PolicyLastModifiedDate=date(2024, 1, 1),
            ),
            Row(
                PolicyReference=2,
                KeyIdPolis=4,
                PolicyLastModifiedDate=date(2025, 1, 1),
            ),
        ]
    )
    expected_df = spark.createDataFrame(
        [
            Row(
                PolicyReference=1,
                KeyIdPolis=10,
                PolicyLastModifiedDate=date(2018, 1, 1),
                PolicySequenceNumber=1,
            ),
            Row(
                PolicyReference=1,
                KeyIdPolis=20,
                PolicyLastModifiedDate=date(2019, 1, 1),
                PolicySequenceNumber=2,
            ),
            Row(
                PolicyReference=1,
                KeyIdPolis=40,
                PolicyLastModifiedDate=date(2021, 1, 1),
                PolicySequenceNumber=4,
            ),
            Row(
                PolicyReference=1,
                KeyIdPolis=30,
                PolicyLastModifiedDate=date(2020, 1, 1),
                PolicySequenceNumber=3,
            ),
            Row(
                PolicyReference=2,
                KeyIdPolis=1,
                PolicyLastModifiedDate=date(2020, 1, 1),
                PolicySequenceNumber=1,
            ),
            Row(
                PolicyReference=2,
                KeyIdPolis=2,
                PolicyLastModifiedDate=date(2022, 1, 1),
                PolicySequenceNumber=2,
            ),
            Row(
                PolicyReference=2,
                KeyIdPolis=3,
                PolicyLastModifiedDate=date(2024, 1, 1),
                PolicySequenceNumber=3,
            ),
            Row(
                PolicyReference=2,
                KeyIdPolis=4,
                PolicyLastModifiedDate=date(2025, 1, 1),
                PolicySequenceNumber=4,
            ),
        ]
    )
    output_df = transform.add_policy_sequence(main_df)
    assert_dataframe_equality(output_df, expected_df)
