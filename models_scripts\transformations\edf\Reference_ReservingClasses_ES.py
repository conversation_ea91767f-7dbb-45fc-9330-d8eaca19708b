import pyspark.sql.functions as F
from pyspark.sql import DataFrame

from models_scripts.transformations.common.traits import business_logic


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["ReservingClasses_main"]
    output_df = main_df.withColumn(
        "KeyReserving",
        F.when(
            (F.col("PolicyProductCode") == "RC02")
            & (F.col("ActivityCode") == "ACTP0741"),
            F.concat_ws(
                ":",
                F.lit("ES"),
                F.coalesce(F.col("PolicyProductCode"), F.lit("")),
                <PERSON>.coalesce(F.col("AssuredMainActivityCode"), F.lit("")),
                F.coalesce(F.col("ActivityCode"), F.lit("")),
            ),
        )
        .when(
            F.col("PolicyProductCode") == "RC02",
            F.concat_ws(
                ":",
                F.lit("ES"),
                F<PERSON>coalesce(F.col("PolicyProductCode"), F.lit("")),
                <PERSON>.coalesce(F.col("AssuredMainActivityCode"), F.lit("")),
            ),
        )
        .otherwise(
            F.concat_ws(
                ":", F.lit("ES"), F.coalesce(F.col("PolicyProductCode"), F.lit(""))
            )
        ),
    )
    return output_df
