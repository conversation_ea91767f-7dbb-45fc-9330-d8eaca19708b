import uuid

import mines2.core.base as base
import mines2.core.constants as const
import pytest

from models_scripts.validations.EDF import validate_by_year, validation


@pytest.fixture(scope="session", autouse=True)
def set_catalog_name():
    """Set catalog name to test"""
    base.spark.sql(f"USE CATALOG {const.CATALOG_NAME}")


@pytest.fixture
def published_schema_name(base_root_path):
    root_table = base_root_path.replace("-", "_").replace("/", "_")
    schema_name = f"{const.CATALOG_NAME}.{root_table}_EDF_published"
    base.spark.sql(f"CREATE SCHEMA IF NOT EXISTS {schema_name}")
    yield schema_name
    base.spark.sql(f"DROP SCHEMA IF EXISTS {schema_name} CASCADE")


@pytest.fixture
def crosstable_schema_name(base_root_path):
    root_table = base_root_path.replace("-", "_").replace("/", "_")
    schema_name = f"{const.CATALOG_NAME}.{root_table}_EDF_clean_crosstable"
    base.spark.sql(f"CREATE SCHEMA IF NOT EXISTS {schema_name}")
    yield schema_name
    base.spark.sql(f"DROP SCHEMA IF EXISTS {schema_name} CASCADE")


@pytest.fixture
def publish_policy_tables(published_schema_name, spark):
    # create test data
    published_policy_transactioncomponent_df = spark.createDataFrame(
        [
            (1, 100, "DE", "DE"),
            (1, 200, "DE", "DE"),
            (2, -500, "DE", "DE"),
            (3, 200, "DE", "DE"),
            (4, 300, "DE", "DE"),
            (5, 400, "DE", "DE"),
        ],
        [
            "PolicySectionTransactionID",
            "TransactionComponentAmount",
            "Country",
            "Partition",
        ],
    )
    published_policy_transaction_df = spark.createDataFrame(
        [(1, 1), (2, 1), (3, 2), (4, 3), (5, 4)],
        ["PolicySectionTransactionID", "PolicySectionID"],
    )
    published_policy_section_df = spark.createDataFrame(
        [(1, 1), (2, 1), (3, 2), (4, 3)], ["PolicySectionID", "PolicyId"]
    )
    published_policy_policy_df = spark.createDataFrame(
        [(1, 2019), (2, 2019), (3, 2020)], ["PolicyId", "YearOfAccount"]
    )

    # register test data as tables
    published_policy_transactioncomponent_df.write.saveAsTable(
        f"{published_schema_name}.Policy_TransactionComponent"
    )
    published_policy_transaction_df.write.saveAsTable(
        f"{published_schema_name}.Policy_Transaction"
    )
    published_policy_section_df.write.saveAsTable(
        f"{published_schema_name}.Policy_Section"
    )
    published_policy_policy_df.write.saveAsTable(
        f"{published_schema_name}.Policy_Policy"
    )


@pytest.fixture
def valid_crosstable_policy_tables(crosstable_schema_name, spark):
    # create test data
    published_policy_transactioncomponent_df = spark.createDataFrame(
        [
            (1, 100, "DE", "DE"),
            (1, 200, "DE", "DE"),
            (2, -500, "DE", "DE"),
            (3, 200, "DE", "DE"),
            (4, 300, "DE", "DE"),
            (5, 400, "DE", "DE"),
        ],
        [
            "PolicySectionTransactionID",
            "TransactionComponentAmount",
            "Country",
            "Partition",
        ],
    )
    published_policy_transaction_df = spark.createDataFrame(
        [(1, 1), (2, 1), (3, 2), (4, 3), (5, 4)],
        ["PolicySectionTransactionID", "PolicySectionID"],
    )
    published_policy_section_df = spark.createDataFrame(
        [(1, 1), (2, 1), (3, 2), (4, 3)], ["PolicySectionID", "PolicyId"]
    )
    published_policy_policy_df = spark.createDataFrame(
        [(1, 2019), (2, 2019), (3, 2020)], ["PolicyId", "YearOfAccount"]
    )

    # register test data as tables
    published_policy_transactioncomponent_df.write.saveAsTable(
        f"{crosstable_schema_name}.Policy_TransactionComponent"
    )
    published_policy_transaction_df.write.saveAsTable(
        f"{crosstable_schema_name}.Policy_Transaction"
    )
    published_policy_section_df.write.saveAsTable(
        f"{crosstable_schema_name}.Policy_Section"
    )
    published_policy_policy_df.write.saveAsTable(
        f"{crosstable_schema_name}.Policy_Policy"
    )


@pytest.fixture
def invalid_crosstable_policy_tables(crosstable_schema_name, spark):
    # create test data
    published_policy_transactioncomponent_df = spark.createDataFrame(
        [
            (1, -100, "DE", "DE"),
            (1, 200, "DE", "DE"),
            (2, -700, "DE", "DE"),
            (3, 500, "DE", "DE"),
            (4, 1000, "DE", "DE"),
            (5, 450, "DE", "DE"),
        ],
        [
            "PolicySectionTransactionID",
            "TransactionComponentAmount",
            "Country",
            "Partition",
        ],
    )
    published_policy_transaction_df = spark.createDataFrame(
        [(1, 1), (2, 1), (3, 2), (4, 3), (5, 4)],
        ["PolicySectionTransactionID", "PolicySectionID"],
    )
    published_policy_section_df = spark.createDataFrame(
        [(1, 1), (2, 1), (3, 2), (4, 3)], ["PolicySectionID", "PolicyId"]
    )
    published_policy_policy_df = spark.createDataFrame(
        [(1, 2019), (2, 2019), (3, 2020)], ["PolicyId", "YearOfAccount"]
    )

    # register test data as tables
    published_policy_transactioncomponent_df.write.saveAsTable(
        f"{crosstable_schema_name}.Policy_TransactionComponent"
    )
    published_policy_transaction_df.write.saveAsTable(
        f"{crosstable_schema_name}.Policy_Transaction"
    )
    published_policy_section_df.write.saveAsTable(
        f"{crosstable_schema_name}.Policy_Section"
    )
    published_policy_policy_df.write.saveAsTable(
        f"{crosstable_schema_name}.Policy_Policy"
    )


@pytest.fixture
def valid_crosstable_claim_tables(crosstable_schema_name):
    # create test data
    new_claim_transactioncomponent_df = base.spark.createDataFrame(
        [
            (1, 100, "DE", "DE"),
            (1, 210, "DE", "DE"),
            (2, -504, "DE", "DE"),
            (3, 205, "DE", "DE"),
            (4, 304, "DE", "DE"),
            (5, 403, "DE", "DE"),
        ],
        ["ClaimsSectionTransID", "TransactionComponentAmount", "Country", "Partition"],
    )
    new_claim_transaction_df = base.spark.createDataFrame(
        [(1, 1), (2, 1), (3, 2), (4, 3), (5, 4)],
        ["ClaimsSectionTransID", "ClaimsSectionID"],
    )
    new_claim_section_df = base.spark.createDataFrame(
        [(1, 1), (2, 1), (3, 2), (4, 3)], ["ClaimsSectionID", "ClaimsID"]
    )
    new_claim_claim_df = base.spark.createDataFrame(
        [(1, 2019), (2, 2019), (3, 2020)], ["ClaimsID", "PolicyYearOfAccount"]
    )

    # register test data as tables
    new_claim_transactioncomponent_df.write.saveAsTable(
        f"{crosstable_schema_name}.Claim_TransactionComponent"
    )
    new_claim_transaction_df.write.saveAsTable(
        f"{crosstable_schema_name}.Claim_Transaction"
    )
    new_claim_section_df.write.saveAsTable(f"{crosstable_schema_name}.Claim_Section")
    new_claim_claim_df.write.saveAsTable(f"{crosstable_schema_name}.Claim_Claim")


@pytest.fixture
def invalid_crosstable_claim_tables(crosstable_schema_name, spark):
    # create test data
    new_claim_transactioncomponent_df = base.spark.createDataFrame(
        [
            (1, 200, "DE", "DE"),
            (1, 310, "DE", "DE"),
            (2, -504, "DE", "DE"),
            (3, 405, "DE", "DE"),
            (4, 604, "DE", "DE"),
            (5, 903, "DE", "DE"),
        ],
        ["ClaimsSectionTransID", "TransactionComponentAmount", "Country", "Partition"],
    )
    new_claim_transaction_df = base.spark.createDataFrame(
        [(1, 1), (2, 1), (3, 2), (4, 3), (5, 4)],
        ["ClaimsSectionTransID", "ClaimsSectionID"],
    )
    new_claim_section_df = base.spark.createDataFrame(
        [(1, 1), (2, 1), (3, 2), (4, 3)], ["ClaimsSectionID", "ClaimsID"]
    )
    new_claim_claim_df = base.spark.createDataFrame(
        [(1, 2019), (2, 2019), (3, 2020)], ["ClaimsID", "PolicyYearOfAccount"]
    )

    # register test data as tables
    new_claim_transactioncomponent_df.write.saveAsTable(
        f"{crosstable_schema_name}.Claim_TransactionComponent"
    )
    new_claim_transaction_df.write.saveAsTable(
        f"{crosstable_schema_name}.Claim_Transaction"
    )
    new_claim_section_df.write.saveAsTable(f"{crosstable_schema_name}.Claim_Section")
    new_claim_claim_df.write.saveAsTable(f"{crosstable_schema_name}.Claim_Claim")


@pytest.fixture
def publish_claim_tables(published_schema_name, spark):
    # create test data
    new_claim_transactioncomponent_df = spark.createDataFrame(
        [
            (1, 100, "DE", "DE"),
            (1, 210, "DE", "DE"),
            (2, -500, "DE", "DE"),
            (3, 205, "DE", "DE"),
            (4, 304, "DE", "DE"),
            (5, 403, "DE", "DE"),
        ],
        ["ClaimsSectionTransID", "TransactionComponentAmount", "Country", "Partition"],
    )
    new_claim_transaction_df = spark.createDataFrame(
        [(1, 1), (2, 1), (3, 2), (4, 3), (5, 4)],
        ["ClaimsSectionTransID", "ClaimsSectionID"],
    )
    new_claim_section_df = spark.createDataFrame(
        [(1, 1), (2, 1), (3, 2), (4, 3)], ["ClaimsSectionID", "ClaimsID"]
    )
    new_claim_claim_df = spark.createDataFrame(
        [(1, 2019), (2, 2019), (3, 2020)], ["ClaimsID", "PolicyYearOfAccount"]
    )

    # register test data as tables
    new_claim_transactioncomponent_df.write.saveAsTable(
        f"{published_schema_name}.Claim_TransactionComponent"
    )
    new_claim_transaction_df.write.saveAsTable(
        f"{published_schema_name}.Claim_Transaction"
    )
    new_claim_section_df.write.saveAsTable(f"{published_schema_name}.Claim_Section")
    new_claim_claim_df.write.saveAsTable(f"{published_schema_name}.Claim_Claim")


def test_policy_gwp_by_year_fail_validation(
    spark,
    invalid_crosstable_policy_tables,
    publish_policy_tables,
    valid_crosstable_claim_tables,
    publish_claim_tables,
    base_root_path,
):
    """Test that the policy_gwp_by_year_validation function fail when expected"""

    # run validation function
    with pytest.raises(AssertionError):
        validation(base_root_path)


def test_policy_gwp_by_year_validation(
    spark, publish_policy_tables, valid_crosstable_policy_tables, base_root_path
):
    """Test that the policy_gwp_by_year_validation function works as expected"""

    # run validation function
    validate_by_year("Policy", base_root_path)


def test_claim_gwc_validation(
    valid_crosstable_policy_tables,
    publish_policy_tables,
    valid_crosstable_claim_tables,
    publish_claim_tables,
    base_root_path,
):
    """Test that the claim_gwc_by_year_validation function works as expected"""
    # create test data

    # run validation function
    validate_by_year("Claim", base_root_path)


def test_claim_gwc_validation_raise(
    valid_crosstable_policy_tables,
    publish_policy_tables,
    invalid_crosstable_claim_tables,
    publish_claim_tables,
    base_root_path,
):
    """Test that the claim_gwc_by_year_validation function raise when expected"""
    # create test data
    # run validation function
    with pytest.raises(AssertionError):
        validation(base_root_path)
