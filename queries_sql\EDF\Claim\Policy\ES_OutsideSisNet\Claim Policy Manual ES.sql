SELECT DISTINCT
    A<PERSON>NUMESINI AS 'KeyInternSchadenummer'
    ,CASE
        WHEN (CASE WHEN B.NPOLICY = '' THEN B.POLIORIG ELSE B.NPOLICY END) IS NOT NULL
            THEN
                CONCAT(
                    B.CODPROD
                    ,' '
                    ,(SELECT MIN(CAST(Z.YEAREFEC AS float)) FROM NTJDWHMRK..MEDLMAPS Z WHERE D.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK)
                    ,'/'
                    ,CASE WHEN B.NPOLICY = '' THEN B.POLIORIG ELSE B.NPOLICY END
                    ,'_'
                    ,(CAST(D.YEAREFEC AS float) - (SELECT MIN(CAST(Z.YEAREFEC AS float)) FROM NTJDWHMRK..MEDLMAPS Z WHERE D.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK) + 1)
                )
        WHEN (CASE WHEN B.NPOLICY = '' THEN B.POLIORIG ELSE B.NPOLICY END) IS NULL THEN C.POLICODE
    END AS 'PolicyCode'

    ,CASE
        WHEN (CASE WHEN B.NPOLICY = '' THEN B.POLIORIG ELSE B.NPOLICY END) IS NOT NULL
            THEN
                CONCAT(
                    CASE WHEN B.NPOLICY = '' THEN B.POLIORIG ELSE B.NPOLICY END
                    ,'_'
                    ,(CAST(D.YEAREFEC AS float) - (SELECT MIN(CAST(Z.YEAREFEC AS float)) FROM NTJDWHMRK..MEDLMAPS Z WHERE D.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK) + 1)
                )
        WHEN (CASE WHEN B.NPOLICY = '' THEN B.POLIORIG ELSE B.NPOLICY END) IS NULL THEN C.POLIREFE
    END AS 'PolicyReference'

    ,CASE
        WHEN (CASE WHEN B.NPOLICY = '' THEN B.POLIORIG ELSE B.NPOLICY END) IS NOT NULL
            THEN
                CONCAT(
                    B.CODPROD
                    ,' '
                    ,(SELECT MIN(CAST(Z.YEAREFEC AS float)) FROM NTJDWHMRK..MEDLMAPS Z WHERE D.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK)
                    ,'/'
                    ,CASE WHEN B.NPOLICY = '' THEN B.POLIORIG ELSE B.NPOLICY END
                    ,'_'
                    ,(CAST(D.YEAREFEC AS float) - (SELECT MIN(CAST(Z.YEAREFEC AS float)) FROM NTJDWHMRK..MEDLMAPS Z WHERE D.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK) + 1)
                )
        WHEN (CASE WHEN B.NPOLICY = '' THEN B.POLIORIG ELSE B.NPOLICY END) IS NULL THEN C.POLICODE
    END AS 'KeyIdPolis'
FROM NTJDWHMRK..MEDLMASI A
LEFT JOIN NTJDWHMRK..MEDLMAPO B ON A.NUMEPOLI = (CASE WHEN B.NPOLICY = '' THEN B.POLIORIG ELSE B.NPOLICY END)
LEFT JOIN NTJDWHMRK..MEDLMAPS D ON B.ID_MEDLMAPO = D.ID_MEDLMAPO_FK AND CAST(D.YEAREFEC AS FLOAT) = CAST(A.YEAREFEC AS FLOAT)
LEFT JOIN NTJDWHMRK..MEDFPOLI C ON A.NUMEPOLI = LEFT(C.POLIREFE, CHARINDEX('_',C.POLIREFE) - 1) AND A.YEAREFEC = C.YEAOFACC
