import pyspark.sql.functions as F
from pyspark.sql import DataFrame

from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.add_columns import AddUSGAAPDateColumn
from models_scripts.transformations.edf.common.backward_populate_records import (
    add_missing_ids,
)


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["Transaction_main"]
    component_df = df_dict["Support_NewTransactionComponents"]
    gaap_df = df_dict["Reference_DatesGAAP"]
    policy_df = df_dict["Policy_Policy"]

    # Add the ids created on TransactionComponent custom transformation into the Transaction table:
    output_df = add_missing_ids(main_df, component_df)

    output_df = output_df.withColumn(
        "TransactionDate", F.to_date(F.col("TransactionDate"))
    )
    output_df = AddUSGAAPDateColumn(output_df, gaap_df, policy_df).add_usgaap_date()

    return output_df
