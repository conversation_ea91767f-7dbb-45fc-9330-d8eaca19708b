Country: ES
existCustomTransformation: 'True'

dataSource:
- name: main
  type: DatabricksCatalogSQLQuery
  parameters:
    sourceSystem: SCS
    catalogName:
      mintdatalake:
        dev: test_mintdatalake_02
        uat: test_mintdatalake_02
        prod: prod_mintdatalake_02
    sqlFileName: section.sql

- name: scs_policy_policy
  type: EuropeanDatalake
  parameters:
    Layer: standard
    subModel: Policy
    Table: Policy_main

- name: sisnet_policy_policy
  type: EuropeanDatalake
  parameters:
    Layer: standard
    subModel: Policy
    Table: Policy_main
    Partitions:
      - ES

- name: sisnet_policy_section
  type: EuropeanDatalake
  parameters:
    Layer: standard
    subModel: Policy
    Table: Section_main
    Partitions:
      - ES

- name: policy
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Policy
    Table: Policy

- name: mapping_table
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Support
    Table: SisnetSCSMappingTableES
    Partitions:
      - ES

- name: activity_code
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Support
    Table: ActivityCodeES
    Partitions:
      - ES

- name: exchange_rate
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: test_mintdatalake_02
      uat: test_mintdatalake_02
      prod: prod_mintdatalake_02
    schema: rdm_dbo
    table: mint_mapping_roe
    sourceSystem: MINT_RAW_DATALAKE

- name: policy_limit
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Policy
    Table: Limit

ColumnSpecs:
  ClaimsSectionPolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  PolicySectionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  SectionEffectiveFromDate:
    dateTimeFormat: dd-MMM-yy
  SectionEffectiveToDate:
    dateTimeFormat: dd-MMM-yy
  EstSignedDown:
    locale: en_US.utf8
  InsurerCarrierPercentage:
    locale: en_US.utf8
  ProfitCommission:
    locale: en_US.utf8
  SignedLine:
    locale: en_US.utf8
  SignedOrder:
    locale: en_US.utf8
  WrittenLine:
    locale: en_US.utf8
  WrittenOrder:
    locale: en_US.utf8
  KeyReserving:
    NotInSource: True