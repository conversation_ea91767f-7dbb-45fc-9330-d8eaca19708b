select distinct p.idpolis                                      					as "KeyIdPolis"
, d.dekkingsnummer                                                      as "KeyDekkingsNummer"
, ltrim(d.rubriek3)                                                    as "SubLimit"
, 'ZEK'                                                                 as "SubLimitBasisCode"
, 'ZEKERHEDEN'                                                          as "SubLimitBasisDescription"
, 'EURO'                                                                as "SubLimitCurrencyCode"
from pub.dekkingvrij d
inner join pub.polisversie p on p.internpolisnummer = d.internpolisnummer
where d.rubriek3 <> '' and ltrim(d.rubriek3) <> '0'
union all
select distinct p.idpolis                                      					as "KeyIdPolis"
, d.dekkingsnummer                                                      as "KeyDekkingsNummer"
, ltrim(d.rubriek4)                                                    as "SubLimit"
, 'REH'                                                                 as "SubLimitBasisCode"
, 'REHABILITATIEKOSTEN'                                                 as "SubLimitBasisDescription"
, 'EURO'                                                                as "SubLimitCurrencyCode"
from pub.dekkingvrij d
inner join pub.polisversie p on p.internpolisnummer = d.internpolisnummer
where d.rubriek4 <> '' and ltrim(d.rubriek4) <> '0'
union all
select distinct p.idpolis                                      					as "KeyIdPolis"
, d.dekkingsnummer                                                      as "KeyDekkingsNummer"
, ltrim(d.rubriek3)                                                    as "SubLimit"
, 'ZEK'                                                                 as "SubLimitBasisCode"
, 'ZEKERHEDEN'                                                          as "SubLimitBasisDescription"
, 'EURO'                                                                as "SubLimitCurrencyCode"
from pub.histdekkingvrij d
inner join pub.histpolisversie p on p.idpolis = d.idpolis
where d.rubriek3 <> '' and ltrim(d.rubriek3) <> '0'
  and p.internschadenummer = 0
union all
select distinct p.idpolis                                      					as "KeyIdPolis"
, d.dekkingsnummer                                                      as "KeyDekkingsNummer"
, ltrim(d.rubriek4)                                                    as "SubLimit"
, 'REH'                                                                 as "SubLimitBasisCode"
, 'REHABILITATIEKOSTEN'                                                 as "SubLimitBasisDescription"
, 'EURO'                                                                as "SubLimitCurrencyCode"
from pub.histdekkingvrij d
inner join pub.histpolisversie p on p.idpolis = d.idpolis
where d.rubriek4 <> '' and ltrim(d.rubriek4) <> '0'
  and p.internschadenummer = 0