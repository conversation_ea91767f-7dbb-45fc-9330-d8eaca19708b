select distinct p.idpolis                                      					as "KeyIdPolis"
, d.dekkingsnummer                                                      as "KeyDekkingsNummer"
, 1                                                                     as "KeyFactuurnummer"
, 'EURO'                                                                as "OriginalCurrencyCode"
, '1'                                                                   as "RateOfExchange"
, 'EURO'                                                                as "SettlementCurrencyCode"
, p.mutatiedatum                                                        as "TransactionDate"
, p.idpolis                                                             as "TransactionReference"
, p.mutatiereden                                                        as "TransactionTypeCode"
, tm.omschrijving                                                       as "TransactionTypeDescription"
, d.dekkingsnummer                                                      as "TransactionSequenceNumber"
from pub.dekking d
inner join pub.polisversie p on p.internpolisnummer = d.internpolisnummer
left outer join pub.tabelmutatieredenpolis tm on tm.mutatiereden = p.mutatiereden
union all
select distinct p.idpolis                                      					as "KeyIdPolis"
, d.dekkingsnummer                                                      as "KeyDekkingsNummer"
, 1                                                                     as "KeyFactuurnummer"
, 'EURO'                                                                as "OriginalCurrencyCode"
, '1'                                                                   as "RateOfExchange"
, 'EURO'                                                                as "SettlementCurrencyCode"
, p.mutatiedatum                                                        as "TransactionDate"
, p.idpolis                                                             as "TransactionReference"
, p.mutatiereden                                                        as "TransactionTypeCode"
, tm.omschrijving                                                       as "TransactionTypeDescription"
, d.dekkingsnummer                                                      as "TransactionSequenceNumber"
from pub.histdekking d
inner join pub.histpolisversie p on p.idpolis = d.idpolis and p.internschadenummer = 0
left outer join pub.tabelmutatieredenpolis tm on tm.mutatiereden = p.mutatiereden
where p.internschadenummer = 0
