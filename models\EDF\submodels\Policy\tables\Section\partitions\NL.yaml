Country: NL
existCustomTransformation: 'True'

dataSource:
- name: main
  type: SourceProgressNL
  parameters:
    sqlFileName: EDF PolicyArraySection Full.sql
    querySourceType: SQL_FILE

- name: extra_records
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Support
    Table: NewTransactionComponents

- name: exchange_rate
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: test_mintdatalake_02
      uat: test_mintdatalake_02
      prod: prod_mintdatalake_02
    schema: rdm_dbo
    table: mint_mapping_roe
    sourceSystem: MINT_RAW_DATALAKE

- name: policy_limit
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Policy
    Table: Limit

ColumnSpecs:
  ClaimsSectionPolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  PolicySectionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  KeyIdPolis:
    locale: en_US.utf8
  KeyDekkingsNummer:
    locale: en_US.utf8
  CostBasisCode:
    locale: en_US.utf8
  CostBasisDescription:
    locale: en_US.utf8
  CoverageType:
    locale: en_US.utf8
  Deductible:
    locale: en_US.utf8
  DeductibleRounded:
    NotInSource: True
  DeductibleGBP:
    NotInSource: True
  DeductibleEUR:
    NotInSource: True
  DeductibleUSD:
    NotInSource: True
  DeductibleCAD:
    NotInSource: True
  DeductibleRoundedGBP:
    NotInSource: True
  DeductibleRoundedEUR:
    NotInSource: True
  DeductibleRoundedUSD:
    NotInSource: True
  DeductibleRoundedCAD:
    NotInSource: True
  EstSignedDown:
    locale: en_US.utf8
  IBCCoverageCode:
    locale: en_US.utf8
  InsurerCarrierCode:
    locale: en_US.utf8
  InsurerCarrierDescription:
    locale: en_US.utf8
  InsurerCarrierPercentage:
    locale: en_US.utf8
  Jurisdiction:
    locale: en_US.utf8
  NoClaimsBonus:
    locale: en_US.utf8
  OperatingTerritory:
    locale: en_US.utf8
  PremiumBasisCode:
    locale: en_US.utf8
  ProfitCommission:
    locale: en_US.utf8
  RateOnExposure:
    locale: en_US.utf8
  SectionEffectiveFromDate:
    dateTimeFormat: ISO
    locale: en_US.utf8
  SectionEffectiveToDate:
    dateTimeFormat: ISO
    locale: en_US.utf8
  SectionProductCode:
    locale: en_US.utf8
  SectionProductDescription:
    locale: en_US.utf8
  SectionReference:
    locale: en_US.utf8
  SectionStatusCode:
    locale: en_US.utf8
  SignedLine:
    locale: en_US.utf8
  SignedOrder:
    locale: en_US.utf8
  SubLimitsIndicator:
    locale: en_US.utf8
  TerritorialScope:
    locale: en_US.utf8
  WrittenLine:
    locale: en_US.utf8
  WrittenOrder:
    locale: en_US.utf8
  KeyReserving:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: SectionProductCode
        sep: ':'
