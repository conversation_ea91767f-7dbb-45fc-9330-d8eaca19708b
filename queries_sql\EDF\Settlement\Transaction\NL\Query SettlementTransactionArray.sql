select distinct pb.idpolis as 'KeyIdPolis'
, ltrim(to_char(pbv.dekkingsnummer)) + ltrim(to_char(pb.productcode)) + ltrim(to_char(pbv.dekkingscode)) as 'KeyDekkingsnummer'
, pb.factuurnummer                                                      as 'KeyFactuurnummer'
, 'PREMIEBOEKING'                                                       as 'Soort'
, ''                                                                    as 'PreviousSystem'
, ''                                                                    as 'PreviousSystemDescription'
, ''                                                                    as 'PreviousTransactionReference'
, 'EURO'                                                                as 'OriginalCurrencyCode'
, '1'                                                                   as 'RateOfExchange'
, 'EURO'                                                                as 'SettlementCurrencyCode'
, pb.notadatum                                                          as 'TransactionDate'
, pb.factuurnummer                                                      as 'TransactionReference'
, pb.mutatiereden                                                       as 'TransactionTypeCode'
, tm.omschrijving                                                       as 'TransactionTypeDescription'
, pb.factuurnummer                                                      as 'TransactionSequenceNumber'
from pub.premieboekingverdeling pbv
inner join pub.premieboeking pb on pb.bedrijffactuurnummer = pbv.bedrijffactuurnummer
left outer join pub.tabelmutatieredenpolis tm on tm.mutatiereden = pb.mutatiereden
left outer join pub.polisversie pyoa on pyoa.idpolis = pb.idpolis
left outer join pub.histpolisversie hyoa on hyoa.idpolis = pb.idpolis

union all

select distinct pb.idpolis as 'KeyIdPolis'
, ltrim(to_char(pbv.dekkingsnummer)) + ltrim(to_char(pb.productcode)) + ltrim(to_char(pbv.dekkingscode)) as 'KeyDekkingsnummer'
, pb.idpremieboekingtp                                                  as 'KeyFactuurnummer'
, 'TUSSENPERSOONBOEKING'                                                as 'Soort'
, ''                                                                    as 'PreviousSystem'
, ''                                                                    as 'PreviousSystemDescription'
, ''                                                                    as 'PreviousTransactionReference'
, 'EURO'                                                                as 'OriginalCurrencyCode'
, '1'                                                                   as 'RateOfExchange'
, 'EURO'                                                                as 'SettlementCurrencyCode'
, pb.notadatum                                                          as 'TransactionDate'
, pb.idpremieboekingtp                                                  as 'TransactionReference'
, pb.mutatiereden                                                       as 'TransactionTypeCode'
, tm.omschrijving                                                       as 'TransactionTypeDescription'
, pb.idpremieboekingtp                                                  as 'TransactionSequenceNumber'
from pub.premieboekingtpverdeling pbv
inner join pub.premieboekingtp pb on pb.idpremieboekingtp = pbv.idpremieboekingtp
left outer join pub.tabelmutatieredenpolis tm on tm.mutatiereden = pb.mutatiereden
left outer join pub.polisversie pyoa on pyoa.idpolis = pb.idpolis
left outer join pub.histpolisversie hyoa on hyoa.idpolis = pb.idpolis