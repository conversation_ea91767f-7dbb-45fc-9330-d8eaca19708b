Country: ES
existCustomTransformation: 'False'

dataSource:
- name: main
  type: SourceCSV
  parameters:
    fileName: EDF ClaimArrayTransaction.csv
    Separator: ','
    Encoding: UTF-8
    sourceSystem: SCS

ColumnSpecs:
  ClaimsID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyInternSchadenummer
        sep: ':'
  ClaimsSectionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyInternSchadenummer
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  ClaimsSectionTransID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyInternSchadenummer
        - source: COLUMN
          name: KeyDekkingsNummer
        - source: COLUMN
          name: KeySchadeBoekingsNummer
        sep: ':'
  TransactionAuthorisationDate:
    dateTimeFormat: dd/MM/yyyy
  TransactionDate:
    dateTimeFormat: dd-MMM-yy
  TransactionNarrative:
    mapType: calculated
    calculatedValue: null
  RateOfExchange:
    locale: en_US.utf8
