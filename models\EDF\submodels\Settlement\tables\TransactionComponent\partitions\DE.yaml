Country: custom
existCustomTransformation: 'True'

dataSource:
- name: main
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: dev_europeandatalake_01
      uat: uat_europeandatalake_01
      prod: prod_europeandatalake_01
    schema: DE_Direct
    table: Settlement_SettlementArrayTransactionComponent
    sourceSystem: SPARK_EDF_EXPORT

- name: exchange_rate
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: test_mintdatalake_02
      uat: test_mintdatalake_02
      prod: prod_mintdatalake_02
    schema: rdm_dbo
    table: mint_mapping_roe
    sourceSystem: MINT_RAW_DATALAKE

- name:  transaction
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Settlement
    Table: Transaction

- name: policy
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Policy
    Table: Policy

ColumnSpecs:
  TransactionComponentAmount:
    locale: en_US.utf8
  TransactionComponentAmountGBP:
    NotInSource: True
  TransactionComponentAmountEUR:
    NotInSource: True
  TransactionComponentAmountUSD:
    NotInSource: True
  TransactionComponentAmountCAD:
    NotInSource: True
  TransactionComponentAmountRounded:
    NotInSource: True
  TransactionComponentAmountRoundedGBP:
    NotInSource: True
  TransactionComponentAmountRoundedEUR:
    NotInSource: True
  TransactionComponentAmountRoundedUSD:
    NotInSource: True
  TransactionComponentAmountRoundedCAD:
    NotInSource: True
  TransactionComponentPercentage:
    locale: en_US.utf8
  TransactionComponentAdditionsDeductionsIndicator:
    sourceName: TransactionComponentAdditionsDeductionsindicator
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: DE
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  SettledTransactionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: DE
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        - source: COLUMN
          name: KeyFactuurnummer
        sep: ':'
  SettledTransComponentID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: DE
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        - source: COLUMN
          name: KeyFactuurnummer
        - source: COLUMN
          name: TransactionComponentTypeCode
        sep: ':'
