Country: NL
existCustomTransformation: 'True'

dataSource:
- name: main
  type: SourceProgressNL
  parameters:
    sqlFileName: Query SettlementTransactionArray.sql
    querySourceType: SQL_FILE

- name: policy
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Policy
    Table: Policy

- name: dates_gaap
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Reference
    Table: DatesGAAP
    Partitions:
      - UN

ColumnSpecs:
  KeyIdPolis:
    locale: en_US.utf8
  KeyDekkingsNummer:
    locale: en_US.utf8
    sourceName: KeyDekkingsnummer
  KeyFactuurnummer:
    locale: en_US.utf8
  OriginalCurrencyCode:
    locale: en_US.utf8
  RateOfExchange:
    locale: en_US.utf8
  SettlementCurrencyCode:
    locale: en_US.utf8
  Sort:
    locale: en_US.utf8
    sourceName: Soort
  TransactionDate:
    dateTimeFormat: ISO
    locale: en_US.utf8
  TransactionReference:
    locale: en_US.utf8
  TransactionTypeCode:
    locale: en_US.utf8
  TransactionTypeDescription:
    locale: en_US.utf8
  TransactionSequenceNumber:
    locale: en_US.utf8
  PreviousSourceSystem:
    locale: en_US.utf8
    sourceName: PreviousSystem
  PreviousSourceSystemDescription:
    locale: en_US.utf8
    sourceName: PreviousSystemDescription
  PreviousTransactionReference:
    locale: en_US.utf8
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  PolicySectionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  SettledTransactionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        - source: COLUMN
          name: KeyFactuurnummer
        sep: ':'
  USGAAP_Date:
    NotInSource: True
