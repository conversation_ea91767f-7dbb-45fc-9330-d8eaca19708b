select sb.internschadenummer                                          as "KeyInternSchadenummer"
, h.idpolis                                                           as "KeyIdPolis"
, ltrim(to_char(sb.dekkingsnummer))                                   as "KeyDekkingsNummer"
, ltrim(to_char(sb.uitkeringsnummer))                                 as "KeySchadeBoekingsNummer"
, sp.naam                                                             as "Payee"
, '1'                                                                 as "RateOfExchange"
, ''                                                                  as "TransactionAuthorisationDate"
, 'EURO'                                                              as "TransactionCurrencyCode"
, sb.boekdatum                                                        as "TransactionDate"
, sb.tekst1 + sb.tekst2 + sb.tekst3 + sb.tekst4 + sb.tekst5 +
  sb.tekst6 + sb.tekst7 + sb.tekst8 + sb.tekst9 + sb.tekst10          as "TransactionNarrative"
, sb.uitkeringsnummer                                                 as "TransactionReference"
, sb.uitkeringsnummer                                                 as "TransactionSequenceNumber"
, 'PAYMENT'                                                           as "TransactionTypeCode"
, 'PAYMENT'                                                           as "TransactionTypeDescription"
from pub.schadeboeking sb
inner join pub.schade s on s.bedrijfinternschadenummer = sb.bedrijfinternschadenummer
inner join pub.histpolisversie h on h.bedrijfinternschadenummer = s.bedrijfinternschadenummer
left outer join pub.schadeboekingbegunstigde sp on sp.bedrijfuitkeringsnummer = sb.bedrijfuitkeringsnummer
left outer join (
select x.bedrijfinternschadenummer, max(x.boekdatum) as boekdatum
from pub.schadereserve x
  group by x.bedrijfinternschadenummer) m
  on m.bedrijfinternschadenummer = s.bedrijfinternschadenummer
left outer join (
select a.bedrijfinternschadenummer, max(a.boekdatum) as boekdatum
from pub.schadeboeking a
  group by a.bedrijfinternschadenummer) b
  on b.bedrijfinternschadenummer = s.bedrijfinternschadenummer

union all
select sb.internschadenummer                                          as "KeyInternSchadenummer"
, h.idpolis                                                           as "KeyIdPolis"
,ltrim(to_char(sb.dekkingsnummer))					                      as "KeyDekkingsNummer"
, sb.schadereserveguid                                                as "KeySchadeBoekingsNummer"
, ''                                                                  as "Payee"
, '1'                                                                 as "RateOfExchange"
, ''                                                                  as "TransactionAuthorisationDate"
, 'EURO'                                                              as "TransactionCurrencyCode"
, sb.boekdatum                                                        as "TransactionDate"
, ''                                                                  as "TransactionNarrative"
, sb.assur_recid                                                      as "TransactionReference"
, sb.assur_recid                                                      as "TransactionSequenceNumber"
, 'MOVEMENT'                                                          as "TransactionTypeCode"
, 'MOVEMENT'                                                          as "TransactionTypeDescription"
from pub.schadereserve sb
inner join pub.schade s on s.bedrijfinternschadenummer = sb.bedrijfinternschadenummer
inner join pub.histpolisversie h on h.bedrijfinternschadenummer = s.bedrijfinternschadenummer
left outer join (
select a.bedrijfinternschadenummer, max(a.boekdatum) as boekdatum
from pub.schadeboeking a
  group by a.bedrijfinternschadenummer) b
  on b.bedrijfinternschadenummer = s.bedrijfinternschadenummer
left outer join (
select x.bedrijfinternschadenummer, max(x.boekdatum) as boekdatum
from pub.schadereserve x
  group by x.bedrijfinternschadenummer) m
  on m.bedrijfinternschadenummer = s.bedrijfinternschadenummer
