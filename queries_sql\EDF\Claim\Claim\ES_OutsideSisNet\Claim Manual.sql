SELECT DISTINCT
    A.NUMESINI AS 'KeyInternSchadenummer',
    'None' AS 'BackgroundNarrative',
    'None' AS 'CatastropheCode',
    'None' AS 'CatastropheDescription',
    A.NUMESINI AS 'ClaimCode',
    CASE WHEN PAIS = 'España' THEN 'ESPA' ELSE 'PORT' END AS 'ClaimCountry',
    'N' AS 'ClaimDeniedIndicator',
    A.DESCSINI AS 'ClaimDescription',
    '' AS 'ClaimDiaryDate',
    'None' AS 'ClaimEventCode',
    'None' AS 'ClaimEventDescription',
    A.OPERADOR AS 'ClaimHandler',
    A.CODOPER AS 'ClaimHandlerCode',
    '' AS 'ClaimInsured',
    B.FECHMOVI AS 'ClaimLastModifiedDate',
    'NO' AS 'ClaimLeadIndicator',
    A.PAIS AS 'ClaimLocationState',
    A.FECHAPER AS 'ClaimOpenDate',
    A.NUMESINI AS 'ClaimReference',
    <PERSON><PERSON>FECHNOTI AS 'ClaimReportDate',
    <PERSON><PERSON> AS 'ClaimStatus',
    YEAREFEC  AS 'ClaimYearOfAccount',
    B.FECHCIERRE AS 'CloseDate',
    'None' AS 'CoverageNarrative',
    'N' AS 'CoverholderWithClaimsAuthority',
    '' AS 'DateOfDeclinature',
    CASE WHEN A.CODIPROD = 'AC01' THEN A.FECHOCURR
         WHEN A.CODIPROD = 'AC02' THEN A.FECHOCURR
         WHEN A.CODIPROD = 'AC03' THEN A.FECHOCURR
         WHEN A.CODIPROD = 'AC04' THEN A.FECHOCURR
         WHEN A.CODIPROD = 'AC05' THEN A.FECHOCURR
         WHEN A.CODIPROD = 'AC06' THEN A.FECHOCURR
         WHEN A.CODIPROD = 'CA01' THEN A.FECHOCURR
         WHEN A.CODIPROD = 'CA02' THEN A.FECHOCURR
         WHEN A.CODIPROD = 'AC02' THEN A.FECHOCURR
         WHEN A.CODIPROD = 'RC01' THEN A.FECHOCURR
         WHEN A.CODIPROD = 'RC02' THEN A.FECHRECLA
         WHEN A.CODIPROD = 'RC03' THEN A.FECHRECLA
         WHEN A.CODIPROD = 'RC04' THEN A.FECHOCURR
         WHEN A.CODIPROD = 'RC05' THEN A.FECHRECLA
         WHEN A.CODIPROD = 'RC06' THEN A.FECHRECLA
    END AS 'DateOfLoss',
    CASE WHEN PAIS = 'España' THEN 'ESPA' ELSE 'PORT' END AS 'GeographicalOriginOfTheClaim',
    'None' AS 'LineageReference',
    CASE WHEN A.LITICODE = 'NO' THEN 'N' ELSE 'I' END AS 'LitigationCode',
    A.LITICODE AS 'LitigationDescription',
    A.MAXPOLOSS AS 'MaximumPotentialLoss',
    'EURO' AS 'MaximumPotentialLossCurrency',
    A.MPL AS 'MaximumPotentialLossPercentage',
    'EURO' AS 'OriginalCurrencyCode',
    '' AS 'PreviousClaimReference',
    '' AS 'PreviousSourceSystem',
    'None' AS 'PreviousSourceSystemDescription',
    'None' AS 'ReasonDeclined',
    'None' AS 'ReserveNarrative',
    'None' AS 'ServiceProviderReference',
    'EURO' AS 'SettlementCurrencyCode',
    'N' AS 'SubrogationSalvageIndicator',
    'None' AS 'TacticsNarrative',
    'N/A' AS 'TPAHandleIndicator',
    A.TRIAGECODE AS 'TriageCode',
    'None' AS 'XCSClaimRef',
    'None' AS 'XCSClaimCode'
FROM  NTJDWHMRK..MEDLMASI A, NTJDWHMRK..MEDLMASS B
WHERE A.ID_MEDLMASI = B.ID_MEDLMASI_FK AND B.FECHMOVI IN (
    SELECT MAX(X.FECHMOVI)
    FROM   NTJDWHMRK..MEDLMASS X
    WHERE  A.ID_MEDLMASI = X.ID_MEDLMASI_FK
)
