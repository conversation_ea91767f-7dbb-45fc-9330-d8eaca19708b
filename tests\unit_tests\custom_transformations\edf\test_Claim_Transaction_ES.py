from datetime import date

import pytest
from mines2.core.constants import SOURCE_SYSTEM
from mines2.core.extensions.misc import assert_dataframe_equality
from pyspark.sql import DataFrame
from pyspark.sql.types import (
    DateType,
    FloatType,
    LongType,
    StringType,
    StructField,
    StructType,
)
from pyspark.testing.utils import assertDataFrameEqual

from models_scripts.transformations.edf.Claim_Transaction_ES import (
    ExtraColumnsClaimTransactionMerger,
)


class TestExtraColumnsClaimTransactionMerger:
    @pytest.fixture(scope="class")
    def main_schema(self):
        schema = StructType(
            [
                StructField("KeyInternSchadenummer", StringType()),
                StructField("KeyIdPolis", StringType()),
                StructField("KeyDekkingsNummer", StringType()),
                StructField("KeySchadeBoekingsNummer", StringType()),
                StructField("Payee", StringType()),
                <PERSON><PERSON>ct<PERSON>ield("RateOfExchange", FloatType()),
                <PERSON><PERSON>ct<PERSON>ield("TransactionAuthorisationDate", DateType()),
                StructField("TransactionCurrencyCode", StringType()),
                StructField("TransactionDate", DateType()),
                StructField("TransactionReference", StringType()),
                StructField("TransactionSequenceNumber", LongType()),
                StructField("TransactionTypeCode", StringType()),
                StructField("TransactionTypeDescription", StringType()),
                StructField(SOURCE_SYSTEM, StringType()),
            ]
        )

        return schema

    @pytest.fixture(scope="class")
    def claim_transaction_df(self, spark, main_schema):
        data = [
            (
                "KeyInternSchadenummer1",
                "KeyIdPolis1",
                "KeyDekkingsNummer1",
                "KeySchadeBoekingsNummer1",
                "Payee1",
                1.0,
                None,
                "TransactionCurrencyCode1",
                date(2023, 12, 1),
                "KeySchadeBoekingsNummer1",
                1,
                "TransactionTypeCode1",
                "TransactionTypeDescription1",
                None,
            ),
            (
                "KeyInternSchadenummer1",
                "KeyIdPolis1",
                "KeyDekkingsNummer2",
                "KeySchadeBoekingsNummer2",
                "Payee2",
                1.0,
                None,
                "TransactionCurrencyCode2",
                date(2023, 12, 2),
                "KeySchadeBoekingsNummer2",
                2,
                "TransactionTypeCode2",
                "TransactionTypeDescription2",
                None,
            ),
            (
                "KeyInternSchadenummer1",
                "KeyIdPolis1",
                "KeyDekkingsNummer3",
                "KeySchadeBoekingsNummer3",
                "Payee3",
                1.0,
                None,
                "TransactionCurrencyCode3",
                date(2023, 12, 3),
                "KeySchadeBoekingsNummer3",
                3,
                "TransactionTypeCode3",
                "TransactionTypeDescription3",
                None,
            ),
            (
                "KeyInternSchadenummer2",
                "KeyIdPolis1",
                "KeyDekkingsNummer4",
                "KeySchadeBoekingsNummer4",
                "Payee4",
                1.0,
                None,
                "TransactionCurrencyCode4",
                date(2023, 12, 3),
                "KeySchadeBoekingsNummer4",
                4,
                "TransactionTypeCode4",
                "TransactionTypeDescription4",
                None,
            ),
            (
                "KeyInternSchadenummer3",
                "KeyIdPolis2",
                "KeyDekkingsNummer5",
                "KeySchadeBoekingsNummer5",
                "Payee5",
                1.0,
                None,
                "TransactionCurrencyCode5",
                date(2023, 12, 4),
                "KeySchadeBoekingsNummer5",
                5,
                "TransactionTypeCode5",
                "TransactionTypeDescription5",
                None,
            ),
            (
                "KeyInternSchadenummer3",
                "KeyIdPolis2",
                "KeyDekkingsNummer6",
                "KeySchadeBoekingsNummer6",
                "Payee6",
                1.0,
                None,
                "TransactionCurrencyCode6",
                date(2023, 12, 5),
                "KeySchadeBoekingsNummer6",
                6,
                "TransactionTypeCode6",
                "TransactionTypeDescription6",
                None,
            ),
            (
                "KeyInternSchadenummer3",
                "KeyIdPolis2",
                "KeyDekkingsNummer7",
                "KeySchadeBoekingsNummer7",
                "Payee7",
                1.0,
                None,
                "TransactionCurrencyCode7",
                date(2023, 12, 6),
                "KeySchadeBoekingsNummer7",
                7,
                "TransactionTypeCode7",
                "TransactionTypeDescription7",
                None,
            ),
            (
                "KeyInternSchadenummer3",
                "KeyIdPolis2",
                "KeyDekkingsNummer8",
                "KeySchadeBoekingsNummer8",
                "Payee8",
                1.0,
                None,
                "TransactionCurrencyCode8",
                date(2023, 12, 7),
                "KeySchadeBoekingsNummer8",
                8,
                "TransactionTypeCode8",
                "TransactionTypeDescription8",
                None,
            ),
        ]

        df = spark.createDataFrame(data, main_schema)
        return df

    @pytest.fixture(scope="class")
    def extra_claims_df(self, spark):
        data = [
            (
                "KeyInternSchadenummer1",
                "KeyDekkingsNummer1",
                "EXTRA_RECORDS_SOURCE01:1",
                "EXTRA_RECORDS_SOURCE01:1",
                date(2022, 12, 1),
                "NewTransactionTypeCode1",
                "NewTransactionTypeDescription1",
                "",
            ),
            (
                "KeyInternSchadenummer1",
                "KeyDekkingsNummer1",
                "EXTRA_RECORDS_SOURCE01:2",
                "EXTRA_RECORDS_SOURCE01:2",
                date(2022, 12, 2),
                "TransactionTypeCode1",
                "TransactionTypeDescription1",
                "",
            ),
            (
                "KeyInternSchadenummer2",
                "KeyDekkingsNummer4",
                "EXTRA_RECORDS_SOURCE01:3",
                "EXTRA_RECORDS_SOURCE01:3",
                date(2022, 12, 3),
                "TransactionTypeCode1",
                "TransactionTypeDescription1",
                "",
            ),
            (
                "KeyInternSchadenummer3",
                "KeyDekkingsNummer5",
                "EXTRA_RECORDS_SOURCE01:4",
                "EXTRA_RECORDS_SOURCE01:4",
                date(2022, 12, 4),
                "NewTransactionTypeCode2",
                "NewTransactionTypeDescription2",
                "",
            ),
            (
                "KeyInternSchadenummer3",
                "KeyDekkingsNummer5",
                "EXTRA_RECORDS_SOURCE01:5",
                "EXTRA_RECORDS_SOURCE01:5",
                date(2024, 12, 5),
                "NewTransactionTypeCode5",
                "NewTransactionTypeDescription5",
                "",
            ),
        ]

        columns = [
            "KeyInternSchadenummer",
            "KeyDekkingsNummer",
            "KeySchadeBoekingsNummer",
            "TransactionReference",
            "TransactionDate",
            "TransactionTypeCode",
            "TransactionTypeDescription",
            SOURCE_SYSTEM,
        ]

        df = spark.createDataFrame(data, columns)
        return df

    def test_init(
        self, claim_transaction_df: DataFrame, extra_claims_df: DataFrame
    ) -> None:
        merger = ExtraColumnsClaimTransactionMerger(
            claim_transaction_df, extra_claims_df
        )
        merger._validate_columns()

    def test_raise_validate_columns(self, claim_transaction_df, extra_claims_df):
        wrong_extra_claims_df = extra_claims_df.drop("KeyInternSchadenummer")
        with pytest.raises(AssertionError):
            ExtraColumnsClaimTransactionMerger(
                claim_transaction_df, wrong_extra_claims_df
            )

    def test_append_new_records_to_main_df(
        self, spark, claim_transaction_df, extra_claims_df, main_schema
    ):
        merger = ExtraColumnsClaimTransactionMerger(
            claim_transaction_df, extra_claims_df
        )
        output_df = merger.append_new_records_to_main_df()

        expected_appended_df = spark.createDataFrame(
            [
                (
                    "KeyInternSchadenummer1",
                    "KeyIdPolis1",
                    "KeyDekkingsNummer1",
                    "EXTRA_RECORDS_SOURCE01:1",
                    "Payee1",
                    1.0,
                    None,
                    "TransactionCurrencyCode1",
                    date(2022, 12, 1),
                    "EXTRA_RECORDS_SOURCE01:1",
                    9,
                    "NewTransactionTypeCode1",
                    "NewTransactionTypeDescription1",
                    "",
                ),
                (
                    "KeyInternSchadenummer1",
                    "KeyIdPolis1",
                    "KeyDekkingsNummer1",
                    "EXTRA_RECORDS_SOURCE01:2",
                    "Payee1",
                    1.0,
                    None,
                    "TransactionCurrencyCode1",
                    date(2022, 12, 2),
                    "EXTRA_RECORDS_SOURCE01:2",
                    10,
                    "TransactionTypeCode1",
                    "TransactionTypeDescription1",
                    "",
                ),
                (
                    "KeyInternSchadenummer2",
                    "KeyIdPolis1",
                    "KeyDekkingsNummer4",
                    "EXTRA_RECORDS_SOURCE01:3",
                    "Payee4",
                    1.0,
                    None,
                    "TransactionCurrencyCode4",
                    date(2022, 12, 3),
                    "EXTRA_RECORDS_SOURCE01:3",
                    11,
                    "TransactionTypeCode1",
                    "TransactionTypeDescription1",
                    "",
                ),
                (
                    "KeyInternSchadenummer3",
                    "KeyIdPolis2",
                    "KeyDekkingsNummer5",
                    "EXTRA_RECORDS_SOURCE01:4",
                    "Payee5",
                    1.0,
                    None,
                    "TransactionCurrencyCode5",
                    date(2022, 12, 4),
                    "EXTRA_RECORDS_SOURCE01:4",
                    12,
                    "NewTransactionTypeCode2",
                    "NewTransactionTypeDescription2",
                    "",
                ),
                (
                    "KeyInternSchadenummer3",
                    "KeyIdPolis2",
                    "KeyDekkingsNummer5",
                    "EXTRA_RECORDS_SOURCE01:5",
                    "Payee5",
                    1.0,
                    None,
                    "TransactionCurrencyCode5",
                    date(2024, 12, 5),
                    "EXTRA_RECORDS_SOURCE01:5",
                    13,
                    "NewTransactionTypeCode5",
                    "NewTransactionTypeDescription5",
                    "",
                ),
            ],
            main_schema,
        )

        expected_df = claim_transaction_df.unionByName(expected_appended_df)

        assert output_df.count() == 13

        assertDataFrameEqual(output_df, expected_df)
