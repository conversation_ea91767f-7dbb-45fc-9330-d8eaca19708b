select distinct p.idpolis                                               as "KeyIdPolis"
, d.dekkingsnummer                                                      as "KeyDekkingsNummer"
, 1                                                                     as "KeyFactuurnummer"
, 'DEDUCTION'                                                           as "TCAdditionsDeductionsIndicator"
, (d.brutopremie - d.doorlprovisietp) / (case when p.termijn = 0 then 1 else p.termijn / 12 end) as "TransactionComponentAmount"
, 'NPREM'                                                               as "TransactionComponentTypeCode"
, 'NETT PREMIUM'                                                        as "TComponentTypeCodeDescription"
, ''                                                                    as "TransactionComponentPercentage"
, 'NETHERLANDS'                                                         as "TransactionComponentTerritory"
from pub.dekking d
inner join pub.polisversie p on p.internpolisnummer = d.internpolisnummer

union all
select distinct p.idpolis                                               as "KeyIdPolis"
, d.dekkingsnummer                                                      as "KeyDekkingsNummer"
, 1                                                                     as "KeyFactuurnummer"
, 'DEDUCTION'                                                           as "TCAdditionsDeductionsIndicator"
,  (d.brutopremie - d.doorlprovisietp) / (case when p.termijn = 0 then 1 else p.termijn / 12 end) as "TransactionComponentAmount"
, 'NPREM'                                                               as "TransactionComponentTypeCode"
, 'NETT PREMIUM'                                                        as "TComponentTypeCodeDescription"
, ''                                                                    as "TransactionComponentPercentage"
, 'NETHERLANDS'                                                         as "TransactionComponentTerritory"
from pub.histdekking d
inner join pub.histpolisversie p on p.idpolis = d.idpolis and p.internschadenummer = 0

where p.internschadenummer = 0

union all
select distinct p.idpolis                                               as "KeyIdPolis"
, d.dekkingsnummer                                                      as "KeyDekkingsNummer"
, 1                                                                     as "KeyFactuurnummer"
, 'DEDUCTION'                                                           as "TCAdditionsDeductionsIndicator"
,   d.doorlprovisietp / (case when p.termijn = 0 then 1 else p.termijn / 12 end)  as "TransactionComponentAmount"
, 'BKR'                                                                 as "TransactionComponentTypeCode"
, 'BROKERAGE'                                                           as "TComponentTypeCodeDescription"
, ''                                                                    as "TransactionComponentPercentage"
, 'NETHERLANDS'                                                         as "TransactionComponentTerritory"
from pub.dekking d
inner join pub.polisversie p on p.internpolisnummer = d.internpolisnummer

union all
select distinct p.idpolis                                               as "KeyIdPolis"
, d.dekkingsnummer                                                      as "KeyDekkingsNummer"
, 1                                                                     as "KeyFactuurnummer"
, 'DEDUCTION'                                                           as "TCAdditionsDeductionsIndicator"
, d.doorlprovisietp / (case when p.termijn = 0 then 1 else p.termijn / 12 end) as "TransactionComponentAmount"
, 'BKR'                                                                 as "TransactionComponentTypeCode"
, 'BROKERAGE'                                                           as "TComponentTypeCodeDescription"
, ''                                                                    as "TransactionComponentPercentage"
, 'NETHERLANDS'                                                         as "TransactionComponentTerritory"
from pub.histdekking d
inner join pub.histpolisversie p on p.idpolis = d.idpolis and p.internschadenummer = 0

where p.internschadenummer = 0
union all
select distinct p.idpolis                                               as "KeyIdPolis"
, d.dekkingsnummer                                                      as "KeyDekkingsNummer"
, 1                                                                     as "KeyFactuurnummer"
, 'ADDITION'                                                            as "TCAdditionsDeductionsIndicator"
,  d.assurantiebelasting / (case when p.termijn = 0 then 1 else p.termijn / 12 end) as "TransactionComponentAmount"
, 'IPT'                                                                 as "TransactionComponentTypeCode"
, 'INSURANCE PREMIUM TAX'                                               as "TComponentTypeCodeDescription"
, ''                                                                    as "TransactionComponentPercentage"
, 'NETHERLANDS'                                                         as "TransactionComponentTerritory"
from pub.dekking d
inner join pub.polisversie p on p.internpolisnummer = d.internpolisnummer

union all
select distinct p.idpolis                                               as "KeyIdPolis"
, d.dekkingsnummer                                                      as "KeyDekkingsNummer"
, 1                                                                     as "KeyFactuurnummer"
, 'ADDITION'                                                            as "TCAdditionsDeductionsIndicator"
, d.assurantiebelasting / (case when p.termijn = 0 then 1 else p.termijn / 12 end)  as "TransactionComponentAmount"
, 'IPT'                                                                 as "TransactionComponentTypeCode"
, 'INSURANCE PREMIUM TAX'                                               as "TComponentTypeCodeDescription"
, ''                                                                    as "TransactionComponentPercentage"
, 'NETHERLANDS'                                                         as "TransactionComponentTerritory"
from pub.histdekking d
inner join pub.histpolisversie p on p.idpolis = d.idpolis and p.internschadenummer = 0
where p.internschadenummer = 0
