targets:
  test:
    mode: production
    workspace:
      # has to be fixed for databrics connect extension to work
      host: https://adb-8799184396184460.0.azuredatabricks.net
      root_path: /Users/<USER>/.bundle/${bundle.target}/${bundle.name}
    variables: # These are applied in the resources ymls
      mines_root_path: main
      quartz_cron_expression_ingestion: 15 0 4 ? * * # at 04:00:15 every day
    run_as:
      service_principal_name: ${var.service_principal_id}

    resources:
      jobs:
        ### Ingestion Job ###
        model_ingestion_job_EDF:
          schedule:
            quartz_cron_expression: ${var.quartz_cron_expression_ingestion}
            timezone_id: UTC

          permissions:
          - service_principal_name: ${var.service_principal_id}
            level: IS_OWNER
          - group_name: ${var.admin_group}
            level: CAN_MANAGE_RUN

          email_notifications: ${var.notification_emails}
          webhook_notifications:
            on_failure:
              - id: ${var.teams_webhook_url}
            on_duration_warning_threshold_exceeded:
              - id: ${var.teams_webhook_url}

        ### Status Monitor Job ###
        monitor_status_job_EDF:
          permissions:
          - service_principal_name: ${var.service_principal_id}
            level: IS_OWNER
          - group_name: ${var.admin_group}
            level: CAN_MANAGE_RUN

          email_notifications: ${var.notification_emails}
          webhook_notifications:
            on_failure:
              - id: ${var.teams_webhook_url}
            on_duration_warning_threshold_exceeded:
              - id: ${var.teams_webhook_url}
