import pyspark.sql.functions as F
import pyspark.sql.types as T
from mines2.singlepartition.transform_functions import concat
from pyspark.sql import DataFrame

from models_scripts.transformations.common.add_columns import (
    add_column_from_match_table,
)
from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.add_columns import (
    get_spanish_country_using_policy_table,
)
from models_scripts.transformations.edf.common.currency import (
    PolicySectionCurrencyConversor,
)


def add_currency_columns(
    df: DataFrame, exchange_rate_df: DataFrame, limit_df: DataFrame
) -> DataFrame:
    """Adds currency columns to the DataFrame using the exchange rate DataFrame."""
    # Create the PolicySectionID calculated column:
    columns = [
        {"source": "CONSTANT", "value": "ES"},
        {"source": "COLUMN", "name": "KeyIdPolis"},
        {"source": "COLUMN", "name": "KeyDekkingsNummer"},
    ]
    df = df.withColumn("PolicySectionID", concat(columns, ":"))

    df_with_currency_code = add_column_from_match_table(
        df,
        limit_df,
        "PolicySectionID",
        {"LimitCurrencyCode": None},
    )
    if "Deductible" not in df_with_currency_code.columns:
        df_with_currency_code = df_with_currency_code.withColumn(
            "Deductible", F.lit(None).cast("float")
        )
    output_df = PolicySectionCurrencyConversor.add_currency_columns(
        df_with_currency_code, exchange_rate_df
    )
    output_df = output_df.drop("LimitCurrencyCode", "PolicySectionID")
    return output_df


def fill_key_reserving(main_df: DataFrame, policy_df: DataFrame) -> DataFrame:
    original_cols = main_df.columns

    df = main_df.join(
        policy_df.select(
            F.col("KeyIdPolis"),
            F.col("PolicyProductCode").cast(T.StringType()),
            F.col("AssuredMainActivityCode").cast(T.StringType()),
        ),
        "KeyIdPolis",
        how="left",
    )

    df = df.withColumn(
        "KeyReserving",
        F.when(
            F.col("PolicyProductCode") == "RC02",
            F.concat_ws(
                ":",
                F.lit("ES"),
                F.col("PolicyProductCode"),
                F.coalesce(F.col("AssuredMainActivityCode"), F.lit("")),
            ),
        ).otherwise(
            F.concat_ws(
                ":", F.lit("ES"), F.coalesce(F.col("PolicyProductCode"), F.lit(""))
            )
        ),
    )
    if "KeyReserving" not in original_cols:
        original_cols.append("KeyReserving")
    return df.select(*original_cols)


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["Section_main"]
    policy_df = df_dict["Policy_Policy"]
    exchange_rate_df = df_dict["Section_exchange_rate"]
    limit_df = df_dict["Policy_Limit"]

    output_df = get_spanish_country_using_policy_table(main_df, policy_df)
    output_df = fill_key_reserving(output_df, policy_df)
    output_df = add_currency_columns(output_df, exchange_rate_df, limit_df)

    return output_df
