SELECT DISTINCT
    A.CLAIREFE AS 'KeyInternSchadenummer',
    'None' AS 'BackgroundNarrative',
    'None' AS 'CatastropheCode',
    'None' AS 'CatastropheDescription',
    A<PERSON>CLAICODE AS 'ClaimCode',
    A<PERSON><PERSON>UN AS 'ClaimCountry',
    A<PERSON> AS 'ClaimDeniedIndicator',
    A<PERSON> AS 'ClaimDescription',
    '' AS 'ClaimDiaryDate',
    'None' AS 'ClaimEventCode',
    'None' AS 'ClaimEventDescription',
    A<PERSON><PERSON><PERSON>HA<PERSON> AS 'ClaimHandler',
    A<PERSON>CLAHANCO AS 'ClaimHandlerCode',
    A.CLAIINSU AS 'ClaimInsured',
    A<PERSON><PERSON> AS 'ClaimLastModifiedDate',
    A.CLALEAIN AS 'ClaimLeadIndicator',
    A.CLALOCST AS 'ClaimLocationState',
    A.CLAOPEDA AS 'ClaimOpenDate',
    <PERSON><PERSON><PERSON>LAIREFE AS 'ClaimReference',
    <PERSON><PERSON><PERSON> AS 'ClaimReportDate',
    <PERSON><PERSON><PERSON>TA<PERSON> AS 'ClaimStatus',
    A<PERSON>C<PERSON>C AS 'ClaimYearOfAccount',
    CASE WHEN A.CLAISTAT = 'ABIE' THEN NULL ELSE A.CLOSEDATE END AS 'CloseDate',
    'None' AS 'CoverageNarrative',
    A.COWICLAU AS 'CoverholderWithClaimsAuthority',
    '' AS 'DateOfDeclinature',
    A.DATOFLOS AS 'DateOfLoss',
    A.GEOROFCL AS 'GeographicalOriginOfTheClaim',
    'None' AS 'LineageReference',
    A.LITICODE AS 'LitigationCode',
    A.LITIDESC AS 'LitigationDescription',
    A.MAXPOTLO AS 'MaximumPotentialLoss',
    A.MAPOLOCU AS 'MaximumPotentialLossCurrency',
    A.MAPOLOPE AS 'MaximumPotentialLossPercentage',
    A.ORICURCO AS 'OriginalCurrencyCode',
    A.PRECLARE AS 'PreviousClaimReference',
    A.PRESOUSY AS 'PreviousSourceSystem',
    'None' AS 'PreviousSourceSystemDescription',
    'None' AS 'ReasonDeclined',
    'None' AS 'ReserveNarrative',
    'None' AS 'ServiceProviderReference',
    A.SETCURCO AS 'SettlementCurrencyCode',
    A.SUBSALIN AS 'SubrogationSalvageIndicator',
    'None' AS 'TacticsNarrative',
    'N/A' AS 'TPAHandleIndicator',
    A.TRIACODE AS 'TriageCode',
    'None' AS 'XCSClaimRef',
    'None' AS 'XCSClaimCode'
FROM NTJDWHMRK..MEDFSINI A
WHERE A.CLAMODDA IN (
    SELECT MAX(X.CLAMODDA)
    FROM NTJDWHMRK..MEDFSINI X
    WHERE X.CLAMODDA >= CONVERT(datetime,'01/01/2000',103)
        AND A.CLAIREFE = X.CLAIREFE
)
