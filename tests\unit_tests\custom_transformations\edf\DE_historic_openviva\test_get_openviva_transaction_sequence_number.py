from datetime import date

import pytest
from mines2.core.extensions.misc import assert_dataframe_equality
from pyspark.sql.types import DateType, StringType, StructField, StructType

from models_scripts.transformations.edf.DE_historic_openviva.get_openviva_transaction_sequence_number import (
    get_openviva_transaction_sequence_number,
)


@pytest.fixture(scope="module")
def main_df(spark):
    return spark.createDataFrame(
        data=[
            # Reserves
            (
                "1001",
                "500000",
                "1-1",
                "R",
                None,
                date(2020, 10, 1),
            ),
            (
                "1001",
                "500000",
                "1-1",
                "R",
                None,
                date(2021, 1, 1),
            ),
            # Payments
            (
                "1001",
                "500000",
                "1-1",
                "Z",
                date(2020, 10, 1),
                None,
            ),
            (
                "1001",
                "500000",
                "1-1",
                "Z",
                date(2021, 1, 1),
                None,
            ),
        ],
        schema=StructType(
            [
                StructField("KeyInternSchadenummer", StringType()),
                StructField("KeyIdPolis", StringType()),
                StructField("KeyDekkingsNummer", StringType()),
                StructField("TransactionType", StringType()),
                StructField("TransactionAuthorisationDate", DateType()),
                StructField("TransactionDate", DateType()),
            ]
        ),
    )


@pytest.fixture(scope="module")
def expected_df(spark):
    return spark.createDataFrame(
        data=[
            # Reserves
            (
                "1001",
                "500000",
                "1-1",
                "R",
                None,
                date(2020, 10, 1),
                "20201001000000",
            ),
            (
                "1001",
                "500000",
                "1-1",
                "R",
                None,
                date(2021, 1, 1),
                "20210101000000",
            ),
            # Payments
            (
                "1001",
                "500000",
                "1-1",
                "Z",
                date(2020, 10, 1),
                None,
                "20201001000000",
            ),
            (
                "1001",
                "500000",
                "1-1",
                "Z",
                date(2021, 1, 1),
                None,
                "20210101000000",
            ),
        ],
        schema=StructType(
            [
                StructField("KeyInternSchadenummer", StringType()),
                StructField("KeyIdPolis", StringType()),
                StructField("KeyDekkingsNummer", StringType()),
                StructField("TransactionType", StringType()),
                StructField("TransactionAuthorisationDate", DateType()),
                StructField("TransactionDate", DateType()),
                StructField("TransactionSequenceNumber", StringType()),
            ]
        ),
    )


@pytest.fixture(scope="module")
def result_df(main_df):
    return get_openviva_transaction_sequence_number(main_df, "yyyyMMddHHmmss")


def test_get_openviva_transaction_sequence_number(result_df, expected_df):
    assert_dataframe_equality(result_df, expected_df)


def test_get_assured_fullname_new_col(result_df, main_df):
    new_col_names = [col for col in result_df.columns if col not in main_df.columns]

    assert new_col_names == ["TransactionSequenceNumber"]


def test_get_assured_fullname_count_eq(result_df, main_df):
    assert result_df.count() == main_df.count()
