## Unreleased

## 7.20.19 (2025-03-10)

### fix

- remove condition to run_job task

## 7.20.18 (2025-03-10)

### refactor

- removed gold workflows + added run_job for monitor workflow

## 7.20.17 (2025-01-07)

### fix

- Downgrade Spark version in model_ingestion.yml for EDF

## 7.20.16 (2024-12-31)

### refactor

- Split the EDF Model

## 7.20.15 (2024-12-26)

### table

- Update data source type for some tables

## 7.20.14 (2024-12-19)

### fix

- Update RateAdequacyDE table schema

## 7.20.13 (2024-12-13)

### table

- Update SQL query for CRMQuotes table by adjusting filters

## 7.20.12 (2024-12-13)

### fix

- service connection str replaced with new one

## 7.20.11 (2024-12-12)

### refactor

- Remove NL_Direct, FrenchBordereaux, SubmissionTriage

## 7.20.10 (2024-12-10)

### refactor

- Refactor fix_claim_ref custom function to use Spark DataFrame operations

## 7.20.9 (2024-12-06)

### refactor

- remove National Markets model

## 7.20.8 (2024-12-04)

### refactor

- remove de_direct and es_direct model

## 7.20.7 (2024-12-02)

### table

- Add AccountManagerCode column to Reporting.Broker table

## 7.20.6 (2024-11-28)

### fix

- schema and yaml adaptations to counteract pandas to spark conversion problems

## 7.20.5 (2024-11-26)

### partition

- Update dev catalogName to test_mintdatalake_02

## 7.20.4 (2024-11-14)

### refactor

- Switch table references in relationship definitions

## 7.20.3 (2024-11-08)

### fix

- validate source branch

## 7.20.2 (2024-11-07)

### partition

- added CA for the table FrequencyGWP

## 7.20.1 (2024-11-05)

### fix

- replace dense_rank with row_number + confirm ordering of records for transactioncomponent

## 7.20.0 (2024-11-04)

### model

- Temporarily disable skipCrossPartitionLayer in the global config for CA_Direct model

## 7.19.8 (2024-11-04)

### refactor

- update crm quotes and renewal queries

## 7.19.7 (2024-10-31)

### fix

- casted TransactionAuthorisationDate in claim_transaction to date

## 7.19.6 (2024-10-29)

### fix

- deployment to prod

## 7.19.5 (2024-10-29)

### refactor

- modify the crm integration queries quotes and renewals

## 7.19.4 (2024-10-29)

### docs

- test commit

## 7.19.3 (2024-10-23)

### table

- Change query criteria to include uppercase and lowercase 'J' values in the WatchListYN field

## 7.19.2 (2024-10-22)

### fix

- Refactor the data aggregation in the Fact_Policies_Transaction table (CA partition)

## 7.19.1 (2024-10-21)

### refactor

- add new column ca_Integration_CRMQuotes BrokerContact, BrokerContactEmail, BrokerContactPhone
- add industryclass reference table
- update the crm renewal table adding CoverFrom column

## 7.19.0 (2024-10-11)

### feat

- switched data sources to test_mintdatalake_01

### fix

- reverted ClaimManual to the version from develop branch
- removed databricks.yml
- fixed parameters import for PolicyArraySttachmentPoint

## 7.18.21 (2024-10-10)

### fix

- started using the Country yaml flag to populate the Country column paritition: added two more tables for UK partition

### partition

- added custom transformation for severity gwp UK
- added UK partition to Frequency GWP
- added missing column to schema.yaml
- modified yaml to account for column differences between partitions
- added UK partition tables table: added Country flag in each yaml fix: changed locale from DE to en_US

## 7.18.20 (2024-10-04)

### table

- added policyarrayattachmentpoint to DE

### fix

- custom transformation file was missing the _PARTITION in name
- DE.yaml wrong column specs

## 7.18.19 (2024-10-04)

### table

- onboard reporting table settled
- onboard reference table webSureContract

### fix

- Update schemas and queries for the tables webcontract and settled
- ca partition config for settled and webcontract

## 7.18.18 (2024-10-03)

### table

- Add CoverFrom field to CRMQuotes SQL and schema

### ci

- update submodule reference

## 7.18.17 (2024-10-03)

### refactor

- Add populate_section_product_code transformation
- Add populate_section_product_code transformation

### style

- Refactor and clean up Dim_Policies_Section_EDF.py

## 7.18.16 (2024-10-01)

### fix

- removed two more module=class from tests
- remove module="class" from test
- removed validation as RuntimeDate and ModelVersion are always from the filename now
- function returning column not df
- lossratio tables were not using their special function to create model_version column
- simplified date and model version columns to only use the filename as input
- added missing kwarg
- update create_version_column and make test
- improved the creation of the RuntimeDate column

## 7.18.15 (2024-09-30)

### refactor

- Refactor to remove duplicate BrokerGroup entries
- Refactor data processing in Dim_Business_Broker_CA

### style

- Add missing newlines to improve code readability

### test

- Refactor unit test for Dim_Business_Broker_EDF

## 7.18.14 (2024-09-27)

### fix

- sql queries from singlepartition to published schema
- correct BrokerGroupMapping table

### refactor

- group premiums by section to avoid duplication

## 7.18.13 (2024-09-26)

### refactor

- Add `P.CalcQuoteDate` to CRMQuotes.sql and update filter criteria
- Add `P.CalcQuoteDate` to CRMQuotes.sql and update filter criteria

## 7.18.12 (2024-09-26)

### test

- Add unit tests for new filter functions in NationalMarkets
- Add unit tests for new filter functions in NationalMarkets

## 7.18.11 (2024-09-26)

### table

- Add converted currency columns for Technical Premium

### docs

- Refactor docstrings in currency conversion classes

### style

- Remove `initialize_logger` from import statements

## 7.18.10 (2024-09-25)

### table

- Add the CA_Integration..CRMQuotes table

### style

- Correct newline at end of CA.yaml file
- Fix SQL syntax inconsistencies and improve formatting.

## 7.18.9 (2024-09-25)

### refactor

- Centralize filter logic in common.misc functions for NationalMarkets (UK partition)
- Add transformation decorators to `add_country_name_col` common function

### ci

- update submodule reference

## 7.18.8 (2024-09-24)

### refactor

- DatabricksCatlogSQLQuery replace sqlFilepath with sqlFileName
- DatabricksCatlogSQLQuery replace sqlFilepath with sqlFileName
- DatabricksCatlogSQLQuery replace sqlFilepath with sqlFileName

## 7.18.7 (2024-09-24)

### fix

- fixed the Decimal type for rechnungop_praemie_vm column to (26, 2) in the test
- subrepo code
- proper fix for the function

## 7.18.6 (2024-09-20)

### fix

- Filter out unselected coverages in add_technical_info function (NM CA)
- Filter out unselected coverages in add_technical_info function (NM CA)

### refactor

- Capitalize currency columns in the link_currency function
- Capitalize currency columns

## 7.18.5 (2024-09-20)

### refactor

- Update regex to include negative Commission values
- Remove unnecessary column renaming in policy transformation.

### ci

- update submodule reference

### docs

- Update test description in Silver_AllData_CA unit test

## 7.18.4 (2024-09-18)

### fix

- repair column name, update tests, add filter for invoices
- fixed a select statement using a faulty alias
- rebuilt the get_broker_payment_date function
- repair column name, update tests, add filter for invoices

## 7.18.3 (2024-09-18)

### table

- Adding CountryName Column to NationalMarkets.Dim_Business_Location transformation and adding collumn to schema

### refactor

- Refactor the custom transformation for more understanding

### test

- Adding test for Dim_Business_Location

## 7.18.2 (2024-09-18)

### fix

- Correct transformation logic in Policy_Transaction table

### test

- Updating unit test for Policy_Transaction

## 7.18.1 (2024-09-16)

### fix

- Update join clause in CA_Direct relationships
- Update join clause in CA_Direct relationships

## 7.18.0 (2024-09-16)

### model

- Updated settlements function for use case and ingesting new table

### fix

- removed databricks.yml
- changed inner join to left, so that records don't go lost + adapted test
- added MINES_MarkedRecord to select, so that it actually is where it should be
- wrong column name
- added sev_buchungskuerzel_main to settlementarraytransaction
- wrong table name in df_dict for settlementarraytransaction

## 7.17.0 (2024-09-13)

### feat

- Add custom transformation function and update CA_Integration schema

### table

- Refactor column name "insuredID" to "InsuredID" on CA_Integration.submodels.Default.CRMRenewals table

### refactor

- Update SQL query in CA_Integration and adjust CRMRenewals schema
- Updating SQL source and CA_Integration.Default.CRMRenewals schema and partition
- Removing Custom transformation for CA_Integration.Default.CRMRenewals
- Update SQL query in CA_Integration and adjust CRMRenewals schema

## 7.16.8 (2024-09-13)

### table

- Update docstring for function CA_Direct.common.replace_sectionid_pandas function

### fix

- Fixing Unit test for test_add_development_reference test
- Fix logic in filter_latest_bound_version custom function

### refactor

- Update imports from test
- Update Function custom transformation
- Update Function filter_records_to_remove
- Update Custom transformations for some tests
- Update Custom transformations to use spark instead pandas
- Update Doc String for function models_scripts.transformations.ca_direct.Reporting_ClaimSettlement_CA function
- Updating the doc string for the function filter_listid
- Removing the test for the funtion replace_sectionid_pandas
- Removing the funtion replace_sectionid_pandas
- Update the logic in CA_Direct.common.filter_schemes_spark merge scheme function to verify if the columns exist

### test

- Adding test for test_Silver_CanadaConsData_CA
- Adding test for test_Silver_AllData_CA
- Adding test for test_Reporting_RemovedClaimSettlement_CA
- Adding test for test_Reporting_RemovedTrans_CA
- Adding test for test_Reporting_RemovedPolicySection_CA
- Adding test for test_Reporting_RemovedPolicy_CA.py
- Adding test for test_Reporting_RemovedClaimSettlementSplit_CA
- Adding test for test_Reporting_RemovedTransHeader_CA
- Adding test for test_Reporting_RemovedTransSplit_CA
- Adding test for test_Reporting_RemovedClaim_CA
- Adding test for test_Reporting_Premium_CA
- Adding test for test_Reporting_PolicyTransaction_CA
- Adding test for test_Reporting_PolicySection_CA
- Adding test for test_Reporting_PolicyLocation_CA
- Adding test for test_Reporting_PolicyClause_CA.py
- Adding test for test_Reporting_MKLCAPolicySectionMaxLimitXS_CA
- Adding test for test_Reporting_ClaimSettlementSplit_CA
- Adding test for test_Reporting_Contract_CA
- Adding test for test_Reporting_MKLCAPolicySectionPremiumLineContractDetails_CA
- Adding test for test_Reporting_MKLCAPolicySectionDetails_CA
- Adding test for test_Reporting_Policy_CA
- Adding test for test_Reporting_ClaimSettlement_CA
- Adding test for test_Reporting_AccountOther_CA
- Adding test for test_Reporting_AccountClient_CA
- Adding test for test_Reporting_AccountBrokerAcc_CA
- Adding test for test_PricingTool_CanadaEnoRisks_CA.py
- Adding test for test_PricingTool_CanadaEnvRisks_CA.py
- Adding test for test_Reference_GAAPFXRate_CA
- Adding test for test_Reference_LimitBasis_CA
- Adding test for test_Reference_Lookup_CA
- Adding test for test_Reference_StatusReason_CA
- Adding unit test for test_PricingTool_PricingTool_CanadaEnvData_CA
- Adding unit test for test_PricingTool_PricingTool_CanadaEnoData_CA
- Adding unit test for test_PricingTool_PricingTool_CanadaConsData_CA.py
- Adding unit test for test_PricingTool_CanadaConsRisks_CA
- Adding unit test for test_PricingTool_AllRisks_CA
- Adding unit test for test_PricingTool_AllData_CA
- Adding unit test for test_Reporting_Trans_CA
- Adding unit test for test_Reporting_TransHeader_CA
- Adding unit test for test_Reporting_TransSplit_CA
- Adding unit test for test_Silver_Claim_CA
- Adding unit test for test_Silver_ClaimSettlement_CA
- Adding unit test for test_Silver_ClaimSettlementSplit_CA.
- Adding test for strip_string_to_specific_cols, fix_and_validate_underwriter_name, set_distribution_method,  set_record_bucket,  set_calc_broker_contact,  set_calc_quote_date,  get_invalidation_info_and_quarantine_col functions
- Adding test for test_custom_transformation function in unit_tests.custom_transformations.ca_direct.test_Silver_PolicyLocation_CA
- Adding test for test_custom_transformation function in unit_tests.custom_transformations.ca_direct.test_Silver_PolicySection_CA
- Adding test for test_custom_transformation function in unit_tests.custom_transformations.ca_direct.test_Silver_Trans_CA
- Adding test for test_custom_transformation function in unit_tests.custom_transformations.ca_direct.test_Silver_TransHeader_CA
- Adding test on test_custom_transformation function in tests/unit_tests/custom_transformations/ca_direct/test_Silver_TransSplit_CA
- Adding unit test for remove_policy_records and filter_latest_bound_version function in custom_transformations.ca_direct.common.test_filter_records
- Adding unit test for CA_Direct.common.replace_sectionid_pandas function in custom_transformations.ca_direct.common.test_replace_sectionid_pandas
- Adding unit test for CA_Direct.common.add_cell_identifier_column function in custom_transformations.ca_direct.common.test_add_columns_ca_direct

## 7.16.7 (2024-09-13)

### table

- Temporarily remove some aggregated columns in NationalMarkets.DimPolicies.Policy table

### ci

- update submodule reference

## 7.16.6 (2024-09-13)

### fix

- Fix DS_Store handling for Mac users
- Fix DS_Store handling for Mac users

## 7.16.5 (2024-09-12)

### fix

- Fix GWP for Canada - NationalMarkets

### refactor

- Standardise value in RenewalIndicator field

### style

- Remove unnecessary blank lines
- Remove unnecessary comments about DateReference column

## 7.16.4 (2024-09-12)

### table

- Adding column to Master Mapping Reference table
- Adding column to Master Mapping Reference table

## 7.16.3 (2024-09-11)

### fix

- Add RenewalIndicator to policy transformation
- Add RenewalIndicator to policy transformation

## 7.16.2 (2024-09-11)

### docs

- Updating doc-string for the add_column_from_match_table custom function

## 7.16.1 (2024-09-10)

### fix

- Fix rate adequacy turnover boundary condition in get_technical_premium_uk

### ci

- update submodule reference

## 7.16.0 (2024-09-10)

## 7.15.2 (2024-09-09)

### table

- Add new fields to PolicySection and PolicySectionRatings
- Add new fields to PolicySection and PolicySectionRatings

## 7.15.1 (2024-09-07)

### fix

- Enhance policy filtering criteria in UK transformation
- Update policy section filtering logic and datasource names

### refactor

- Move constant to nationalmarkets module
- Format docstrings and remove unused logger parameter
- Add a fillna operation for currency dimensions

### style

- Update table name to "Enhanced_Policy" in UK.yaml
- Refactor imports and improve docstrings

## 7.15.0 (2024-09-06)

### fix

- Add YearOfAccount field to test data structures
- Include YearOfAccount in policy matching transformation

## 7.14.3 (2024-09-05)

### fix

- Handle null values for TechnicalPremiumCurrency

## 7.14.2 (2024-09-05)

### refactor

- streamline premium calculations and aggregation logic

## 7.14.1 (2024-09-05)

### fix

- Refactor rate adequacy data transformation logic
- Refactor rate adequacy data transformation logic

## 7.14.0 (2024-09-05)

### fix

- Add missing primary keys and correct dataframe references
- Correct field name in Fact_Policies_TechnicalPremium_CA.py
- Correct dataframe column names and expected test data
- Correct subModel and Table references in CA.yaml

### refactor

- Adjust cache and checkpoint rules, add distinct operation
- enhance premium calculations with improved aggregations

## 7.13.3 (2024-08-29)

### table

- Update schema and partition for NL_Direct..ClaimsFreeField table
- Update schema and partition for NL_Direct..ClaimsFreeField table
- Update the schema for the BrokerGroupAndSegmentation Table
- Update the schema for the BrokerGroupAndSegmentation Table

## 7.13.2 (2024-08-29)

### refactor

- Simplify PolicyID and SectionNumber extraction

### ci

- update connection to mines

## 7.13.1 (2024-08-29)

### table

- Implement NL technical premium calculation

### fix

- Correct currency linking in technical premium calculation
- Correct PolicyLineRef column and key mapping

## 7.13.0 (2024-08-28)

### model

- Introduce NULL_SECTION_ID_PLACEHOLDER constant

### table

- Add UK_Demeter TechnicalPremium

### fix

- update MINES version
- add missing currency fields to Dim_Policies_Section_UK
- Update rules, extend date range, and type cast columns

### refactor

- Update data transformations and rule applications

### partition

- Add caching and granularity change for transaction data
- Update UK partition schema and adjust transformation script

## 7.12.3 (2024-08-27)

### table

- Add the CA_Direct.Reference.UWRTeam table

### fix

- Remove unnecessary Custom Transformation for the Actuarial.Reference.ExchangeRate table and remove its Unit test

### test

- Add and update unit tests for custom transformation functions in actuarial module
- Update unit test for Actuarial.ClaimTriangles_CurrentStats_LAST_QUARTER table
- Minor improvements in actuarial unit test
- Add and update unit tests for custom transformation functions in actuarial module

## 7.12.2 (2024-08-26)

### fix

- replace hard coded MINES_MarkedRecord column name with const.MARKED_RECORD_COLUMN_NAME
- added "MINES_MarkedRecord" to the last select statement in the custom transformation for claimarraysection
- Refactor function to return selected dataframe directly
- Fix regexp_replace escaping in get_claim_section_reference.py
- fixed faulty parenthesis

### test

- Remove column in test for claim section reference

## 7.12.1 (2024-08-25)

### table

- Add calculated string columns for Incurred and Paid developments
- Adding filter in silver_Policy "ExtraInfo3" == "D19" column
- Adding filter in silver_Policy "ExtraInfo3" == "D19" column
- Adding filter in silver_Policy "ExtraInfo3" == "D19" column

### fix

- Fix locale settings for some columns in Actuarial.ClaimTriangles.DevelopmentPatterns table
- Rename columns for consistency in Silver.CanadaEnvData transformation
- Add missing columns to CA_Direct.PricingTool.CanadaEnoData table schema
- Add column specification to CA_Direct.Silver.CanadaEnvData table
- Fixing TestSilver_Policy.test_filter_status unit test.
- Standardize "Deductible" column to float type.

### refactor

- Refactor add_pol_id_col custom function
- Add function to clean and validate numeric columns
- Refactor non-numeric row marking logic in filter_records.py
- Refactor remove_newlines function to support multiple columns
- Remove unused 'transformation' import from CA scripts
- Organize add_development_reference function
- Move add_cell_identifier_column to a common module
- Add business_logic and requires decorators
- Refactor `enforce_nulls_type` for cleaner column selection

### ci

- update submodule reference

### style

- Remove commented-out code in Silver_AllData_CA.py

### test

- Add unit test for add_to_remove_and_removed_reason_cols
- Refactor and enhance `test_add_pol_id_col` function
- Improve the unit test for  mark_non_numeric_rows function

## 7.12.0 (2024-08-22)

### feat

- Introduce business_logic decorator to transformations
- Introduce business_logic decorator to transformations

### fix

- simplify logger initialization in traits.py
- Remove unused import `initialize_logger` from scripts
- Correct dataset name in EuropeanDataLake-MINES

### refactor

- replace lock removal method and revise relationships
- Apply business_logic decorator and import cleanup

## 7.11.6 (2024-08-21)

### table

- Renaming column Extrainfo3_Category to Extrainfo3_PolicySatusReason on Reporting.Policy and Silver.Policy tables
- Renaming column Extrainfo3_Category to Extrainfo3_PolicySatusReason on Reporting.Policy and Silver.Policy tables

### fix

- Rename ExtraInfo3_PolicyStatusReason column
- Fix and rename Extrainfo3_Category to ExtraInfo3_PolicySatusReason

### refactor

- Refactor replace_extrainfo3_col function to use a common variable

## 7.11.5 (2024-08-21)

### table

- Adding CanadaEnoData table to AllData
- Updating Custom transformation
- Updating transformations for PricingTool_AllData, PricingTool_CanadaConsData_CA, PricingTool_CanadaEnoData_CA,PricingTool_CanadaEnvData_CA in CA_Direct/PricingTool
- Creating transformations for Silver_AllData, Silver_CanadaConsData_CA, Silver_CanadaEnoData_CA,Silvevr_CanadaEnvData_CA in CA_Direct/Silver
- Updating Ca.yaml for PricingTool_AllData, PricingTool_CanadaConsData_CA, PricingTool_CanadaEnoData_CA,PricingTool_CanadaEnvData_CA in CA_Direct/PricingTool
- Creating schema.yaml and Ca.yaml for Silver_AllData, Silver_CanadaConsData_CA, Silver_CanadaEnoData_CA, Silver_CanadaEnvData_CA.py in CA_Direct/Silver path
- Creating function mark_non_numeric_rows and refactoring add_pol_id_col function
- Adding unit-test for function add_pol_id_col and mark_non_numeric_rows

### fix

- Fixing mark_non_numeric_rows custom function

### test

- Moving Unit tests to the correct file

## 7.11.4 (2024-08-20)

### table

- Update datatype
- Updating partition
- Update path
- Bring through Limit information from Demeter Amalgamated_PolicySectionLiabilities table

## 7.11.3 (2024-08-19)

### table

- Updated MKLCAPolicySectionDetails table with new columns
- Fixing columns for CA_Direct..MKLCAPolicySectionDetails table
- Fixing custom transformation for CA_Direct..MKLCAPolicySectionDetails table
- Fixing columns for CA_Direct..MKLCAPolicySectionDetails table
- Fixing custom transformation for CA_Direct..MKLCAPolicySectionDetails table
- Updated MKLCAPolicySectionDetails table with new columns

### refactor

- Update add_brag_reference_col custom function

## 7.11.2 (2024-08-19)

### fix

- Remove deprecated SEV_vm_start_date.sql script
- rollback wrong change on queries_sql

### test

- update test cases with new date-related fields and schema

## 7.11.1 (2024-08-16)

### fix

- rollback wrong change on queries_sql

## 7.11.0 (2024-08-16)

### fix

- Add missing ClaimReference and DateKey fields to tests
- Change relationships cardinality in relationships.yaml
- Correct column mapping and add missing columns
- Update relationship types in relationships.yaml
- Update data types and correct column references
- rename SQL query files and remove duplicate columns
- Correct table name in UK partitions

### refactor

- improve handling of null values in data transformations

### test

- Add ClaimReference to test cases for Dim_Claims_Claim_EDFIncluded ClaimReference field in multiple test rows to ensure comprehensive coverage and validation. This addition helps in validating transformations where the ClaimReference is crucial for business logic. Each test case now has a ClaimReference to match corresponding ClaimsID entries.feature: Add ClaimReference to test cases for Dim_Claims_Claim_EDFIncluded ClaimReference field in multiple test rows to ensure comprehensive coverage and validation. This addition helps in validating transformations where the ClaimReference is crucial for business logic. Each test case now has a ClaimReference to match corresponding ClaimsID entries.feature: Add ClaimReference to test cases for Dim_Claims_Claim_EDFIncluded ClaimReference field in multiple test rows to ensure comprehensive coverage and validation. This addition helps in validating transformations where the ClaimReference is crucial for business logic. Each test case now has a ClaimReference to match corresponding ClaimsID entries.feature: Add ClaimReference to test cases for Dim_Claims_Claim_EDFIncluded ClaimReference field in multiple test rows to ensure comprehensive coverage and validation. This addition helps in validating transformations where the ClaimReference is crucial for business logic. Each test case now has a ClaimReference to match corresponding ClaimsID entries.feature: Add ClaimReference to test cases for Dim_Claims_Claim_EDFIncluded ClaimReference field in multiple test rows to ensure comprehensive coverage and validation. This addition helps in validating transformations where the ClaimReference is crucial for business logic. Each test case now has a ClaimReference to match corresponding ClaimsID entries.feature: Add ClaimReference to test cases for Dim_Claims_Claim_EDFIncluded ClaimReference field in multiple test rows to ensure comprehensive coverage and validation. This addition helps in validating transformations where the ClaimReference is crucial for business logic. Each test case now has a ClaimReference to match corresponding ClaimsID entries.feature: Add ClaimReference to test cases for Dim_Claims_Claim_EDFIncluded ClaimReference field in multiple test rows to ensure comprehensive coverage and validation. This addition helps in validating transformations where the ClaimReference is crucial for business logic. Each test case now has a ClaimReference to match corresponding ClaimsID entries.feature: Add ClaimReference to test cases for Dim_Claims_Claim_EDF

## 7.10.4 (2024-08-16)

### refactor

- Add the Claim_Claim.py custom script to consolidate currency conversion functionality

### ci

- update submodule reference

### partition

- Update locale for MaximumPotentialLoss field

## 7.10.3 (2024-08-15)

### fix

- Updated Claim_Claim_ES.py to correct error in code by deleting checks on column names with yoa_suffix
- Updated ES.yaml to exclude the YOA
- Deleted ManualAdjustmentESYearOfAccount to match the new code
- Updated Claim_Claim_ES.py to exclude the YOA function that overwrite claims YOA using a manual reference file

## 7.10.2 (2024-08-13)

### table

- Refactoring name column in custom transformations CA_Direct/Pricing_Tool/ Data tables
- Refactoring name column in custom transformations CA_Direct/Pricing_Tool/ Data tables
- Refactoring name column in custom transformations CA_Direct/Pricing_Tool/ Data tables
- Refactoring name column in custom transformations CA_Direct/Pricing_Tool/ Data tables
- Refactoring name column in CA_Direct/Pricing_Tool/ Data tables
- Chanching the column name in CA_Direct/PricingTool/CanadaEnvData
- Renaming a column in CA_Direct/PricingTool/CanadaEnvData schema.yaml
- Refactoring table name in custom transformation CA_Direct/Pricing_Tool/EnvData
- Alterating the table name in the custom transformation models_scripts/transformations/ca_direct/PricingTool_AllData_CA.py
- Add Pol_ID column and update transformations

## 7.10.1 (2024-08-13)

### table

- Changing name of columns in GWP tables
- Changing name of the GWP tabels
- Changing name of the GWP tabels
- Adding column Version_Index on Gwp table on portfolio optmization and change the name of the table on  models_scripts/transformations/portfoliooptimisation/ Gwp tables
- Renaming the name of the tables Gwp's
- Adding new tables for Portfolio Optmization
- New BRAGGroup table on CA_Direct/Reference
- New BRAGGroup table on CA_Direct/Reference

### ci

- update submodule reference
- update submodule reference

### style

- Remove unnecessary whitespace and fix missing newlines

### test

- Remove outdated unit tests for CA_Direct.SubmissionTriage submodel

## 7.10.0 (2024-08-08)

### feat

- add Version_Index column with window function Added a new column called Version Index to all tables in the Portfolio Optimisation.

### fix

- fixing the call of the function add_index_column_by_column and minor improvments
- fixing the schema.yaml adding Version_Index
- fixing the column name used in the models_scripts/transformations/portfoliooptimisation/common/index_column_by and the column name used in all custom transformations of PortfolioOptimisation and making Unit test
- fixing the column name used in the models_scripts/transformations/portfoliooptimisation/common/index_column_by and the column name used in all custom transformations of PortfolioOptimisation

## 7.9.2 (2024-08-07)

### refactor

- Remove the MARKED_RECORD column in claim section scripts
- Remove unused locale settings for Policy_Limit table
- Refactor currency handling and limit calculations (Policy_Limit table)
- Refactor Claim_Section_ES transformation logic and Add default marked record column
- Add default marked record column to Claim_Section_DE.py

### docs

- Refactor `new_base_query` function signature and docstring

### style

- Improve formatting
- Remove redundant docstring in custom_transformation function
- Remove unused import and improve docstring in traits.py
- Remove unused import and docstring

## 7.9.1 (2024-08-07)

### table

- Drop the ChangeCode column from NL_Direct.Reporting.Claims table
- Drop the ChangeCode column from NL_Direct.Reporting.Claims table
- Drop the ChangeCode column from NL_Direct.Reporting.Claims table
- Add and update mapping PricingTool tables
- Add and update mapping PricingTool tables

### fix

- Fix SQL query for Claim.TransactionComponent table (ES_OutsideSisNet partition)
- Fix SQL query for Claim.Transaction table (ES_OutsideSisNet partition)

## 7.9.0 (2024-08-06)

### feat

- add CalcPolSecRef column as concatenated PK
- add CalcPolSecRef column as concatenated PK

## 7.8.2 (2024-08-05)

### refactor

- did not stage remove of file
- modified mark_records to a new column parameter for marking values

## 7.8.1 (2024-08-03)

### fix

- Fix typo in SQL script affecting data type casting

### style

- Fix indentation in SQL scripts

## 7.8.0 (2024-08-01)

### feat

- Add dimension keys manager and tests

### model

- Add relationships.yaml defining table relationships
- Add SourceSystemCountry to schema and update scripts
- Add policy linking and refactor dimension linking
- Rename Fact models to Dim models and update schemas
- Revise EDF transaction model and transformation script
- Add currency linking transformation
- Inclusion of new NationalMarkets Model

### table

- Remove unused ClaimTransactionType column
- Add currency dimension linkage
- Update schema and transformation logic
- update schema with detailed currency fields
- Add new policy fields to schema.yaml

### fix

- update mines connection
- Update DateType to DateRecordType in unit tests
- correct SQL syntax error in PolicyTechnicalPremiumCurrency
- Correct dictionary key for dim_core_currency_df
- Include surrogate keys in column assertions
- Correct currency linking and remove duplicate assignments
- correct schema data types and primary key
- Add CAD limit and improve section policy aggregation
- Add missing LimitAggregateCurrency field in schema
- Correct column references and add partition details
- Update dimension linker and transformation scripts
- Add Country field to policy_policy_df selection
- Update data type for MonthNr and Day columns
- Correct data types for Date and DateType fields
- standardize dictionary keys and function parameters
- Update Currency partition configuration in UN.yaml
- Correct table name in EDF.yaml partition
- Remove obsolete currency transformation script
- Rename table references to SisnetSCSMappingTableES
- Add column prefix parameter to calculate method
- Use the non rounded columns instead of the rounded ones for the aggregated metrics.
- Correct @transformation decorator placement
- remove unused policy_transaction_df key from mapping
- Update DataFrame key names for policy data references

### refactor

- Update primary and surrogate keys in schema
- Rename columns and add new one for commission percentage
- Rename columns to align with section naming
- Rename TechRatio to PolicyTechRatio
- Simplify join syntax and add missing comma
- Simplify joins and update column selections
- Reorganize policy and claims schema and scripts
- update and streamline policy and section yaml files
- Add logger initialization and rename functions
- Remove unused imports in transformation scripts
- Remove unused settlement_transactioncomponent entry
- Replace EDF partitions with UN partitions in Dim_Core
- Update national markets data models and transformation scripts

### partition

- Add policy linking and refactor dimension structures
- Remove CA.yaml partition from SeverityActuals

### test

- add validation for unique columns in join_col list
- update test data schema for policy and claim sections
- update tests and data for accurate mock date and policies
- update the tests for national markets dimension business broker, location, product, team
- Add unit tests for Dim_Policies_Policy and Dim_Claims_Claim

## 7.7.0 (2024-08-01)

### feat

- Translate a single UK Demeter Stored Procedure BrokerDetails into MINES

### fix

- Broker transformation file rename

## 7.6.2 (2024-08-01)

### table

- Fix custom tranformation CA_Dorect.Reporting.Policy
- Add new custom function to CA_direct.Reporting.Policy table
- Add the CA_Direct.Reference.StatusReason table

### fix

- Fix replace_extrainfo3_col custom function
- Fix the column name on models_scripts/transformations/ca_direct/Reference_StatusReason_CA.py for ListID
- Fix the table name in the custom transformation for reference StatusReason table

### style

- Update formatting

### test

- Refactor the unit test for reporting_policy table

## 7.6.1 (2024-08-01)

### table

- Add new CA_Direct.Reporting.PolicyTransaction table.
- Add GAAP_Month_Year field to PolicyTransaction schema
- Rename column and update schema in CA_Direct..PolicyTransaction table
- Add new CA-Direct.Reporting.PolicyTransaction table

### fix

- Fix the reference table name

### refactor

- Refactor transformation for GAAP columns
- Add transformation decorator and new custom function for Reporting_PolicyTransaction_CA.py
- Refactor `GAAPDate` column logic in `Reporting_PolicyTransaction_CA.py`.
- Update custom_tranformation for reporting_PolicyTransaction

### docs

- Update doc string and annotation in Reporting_PolicyTransaction_CA.py

### test

- Add tests for add_calc_gaap_cols and replace_add_fx_cols
- Add unit test for Reporting_PolicyTransaction_CA transformation
- Make unit test input methods static

## 7.6.0 (2024-07-31)

### feat

- use Default_SubmissionScore table to filter already scored WIP policies
- use Default_SubmissionScore table to filter already scored WIP policies

### fix

- fix remove Incremental flag in our Excel data sources
- fix Default_SubmissionScore table name to SubmissionScore_submissionscore_published

### test

- fix ModelVersionID column in the expected_df in test_calculate_total_score_and_tiers

## 7.5.0 (2024-07-31)

### feat

- Change datatype in schema to align with mines

### table

- Add CA_Direct_Reference_BRAG table
- Add CA_Direct_Reference_BRAG table
- Add Ca_Direct.Reference.MarkelCanadaOffices table
- Add Ca_Direct.Reference.MarkelCanadaOffices table

### fix

- Unit test case sensitivity
- remove unnecessary parameter in the sourceCSV

## 7.4.0 (2024-07-29)

### model

- Add SubmissionTriage as model instead of CA_Direct submodel
datasources: SourcePricingToolPaas has been used instead of Excel datasources
- Add SubmissionTriage as model instead of CA_Direct submodel

### table

- add ModelVersionID column from PricingTool factors tables. Also change data source to PricingToolSQL
- add ModelVersionID column from PricingTool factors tables. Also change data source to PricingToolSQL
- add PricingTool WeightFactor table

### test

- fix decimal cases in TotalScore column

## 7.3.1 (2024-07-25)

### table

- set new primary key ParentKey_PolicyID in the CA_Direct/submodels/Reporting/Docs/Schema.yamls table in the schema.yaml
- update the reporting_docs table

## 7.3.0 (2024-07-24)

### model

- Add the new CA_Integration model, Refactor CRMRenewals table location

### table

- updated partition crmrenewals

## 7.2.9 (2024-07-24)

### fix

- Added new PFR theresholds, new PB_scoring function, fix unit tests
- Added new PFR theresholds, new PB_scoring function, fix unit tests
- Added new PFR theresholds, new PB_scoring function, fix unit tests

## 7.2.8 (2024-07-23)

### fix

- Add duplicate dropping logic in Policy_Transaction_ES_OutsideSisNet
- Add duplicate dropping logic in Policy_Transaction_ES_OutsideSisNet

## 7.2.7 (2024-07-23)

### fix

- Container solution
- conflict problem

## 7.2.6 (2024-07-22)

### fix

- Refine transformation function and Add test for Spanish country column addition
- Refine transformation function and Add test for Spanish country column addition

## 7.2.5 (2024-07-20)

### fix

- correct volume and mount point names
- Configure Dev Container with Oh My Zsh and Powerlevel10k.

## 7.2.4 (2024-07-20)

### table

- Add Exposure field to SeverityActuals schema and partition

### partition

- Add new columns to SeverityActuals schema

## 7.2.3 (2024-07-19)

### table

- Add the CA_Direct.Reference.LeadLine table

## 7.2.2 (2024-07-18)

### refactor

- Add net commission calculation and update schema
- Add net commission calculation and update schema

## 7.2.1 (2024-07-18)

### table

- Add custom transformations to CA_Direct.Reference.GAAPFXRate table
- Add CA_Direct.Reference.GAAPFXRate table

### refactor

- Add transformation decorator to reformat_dates function

## 7.2.0 (2024-07-18)

### model

- Update schema and partition mappings in ProfitStudy tables

### table

- Remove redundant columns in LossRatioFutureFanChart schema

### fix

- add user email to git config in dev deployment
- correct spelling error in source name fields
- correct spelling error in source name fields

## 7.1.10 (2024-07-17)

### fix

- add new folder path to use the same data source
- add new folder path to use the same data source

## 7.1.9 (2024-07-17)

### table

- added PFRSubmissionScore table with scoring pipeline
- added SupportPolicy, PFR_Scoring table and IBCCodeMapping table
- added IBCCodeMapping table with custom transformation
- added Product_Class mapping table
- added BrokerGroupMapping mapping table
- added Product segmentation table
- added Premium segmentation table
- added LossRation segmentation table
- added BrokerDepartament excel segmentation table
- add WIP policies from CA_Direct

### fix

- remove unnecessary packages and simplify logic to custom transformations
- test_calculate_total_score_and_tiers with correct table schema
- unitest to IBCCode column
- unit test for calculating total score
- IBCCodeMapping column name BRAGColour
- custom function to extract IBC code

### test

- add SubmissionTriage_PFRSubmissionScore testing

## 7.1.8 (2024-07-16)

### table

- Add new fields to CA_Direct..MKLCACashAllocation table schema
- Add new fields to CA_Direct..MKLCACashAllocation table schema

### ci

- update submodule reference

## 7.1.7 (2024-07-16)

### fix

- Update datatype and column name in NL_Direct Reporting Claims table
- Fix ColumnSpecs for ChangeCode in NL_Direct.Reporting.Claims table to avoid duplications

### partition

- Update table column names to use camel case

## 7.1.6 (2024-07-14)

### refactor

- Update methods handling version data and timestamps

## 7.1.5 (2024-07-14)

### fix

- Add mount
- Add mount

## 7.1.4 (2024-07-13)

### fix

- correct casing in FrequencyAvse schema

## 7.1.3 (2024-07-13)

### refactor

- Simplify version and date column creation in portfolio optimisation scripts

## 7.1.2 (2024-07-13)

### fix

- update default origin_column in calculate_audits

### refactor

- Remove 'ModelVersion' and 'RuntimeDate' from yaml files

## 7.1.1 (2024-07-13)

### refactor

- Handle missing runtime_date in portfolio optimization

## 7.1.0 (2024-07-13)

### feat

- Create new partitions and update schema
- Create new partitions and update schema
- Implement partitions for Netherlands (NL) and Canada (CA)
- Implement partitions for Netherlands (NL) and Canada (CA)

### model

- Update various ProfitStudy table transformations

### fix

- hotfix to wrong variable name in ProfitStudy_LossRatioActuals_NL.py
- Update hash connection of submodule to MINES solution
- correct DataFrame names in portfolio optimisation scripts
- Delete NotificationsAdjustedPred table

### refactor

- Remove unused 'N' field from FrequencyActuals model

## 7.0.3 (2024-07-09)

### table

- Add pivot custom function and update schema in CA_Direct.PricingTool.AllData table

### refactor

- Extract dataframe concatenation into a separate function

## 7.0.2 (2024-07-09)

### refactor

- Add effective date filter to CA_Direct..CanadaConsRisks table
- Add effective date filter to CA_Direct..CanadaConsRisks table

### partition

- Remove unnecessary column specifications in FR partition from EDF.Policy tables

### test

- Fix tests for HYALINBinderProcessor
- Fix portfolio processor unit tests
- Fix SPVIEBinderProcessor unit tests
- Fix HYALINBinderProcessor unit tests and adjust calculations
- Add pytest fixture for keydekkingnummer mapping dataframe

## 7.0.1 (2024-07-09)

### fix

- Solve issue that poetry install was running parallel to submodule update on dev container

## 7.0.0 (2024-07-08)

### table

- Add CalcRetroActiveDate to Claims tables via custom transformation
- Update catalog references and query for CA_Direct.Reporting.Docs table
- Update catalog references and query for CA_Direct.Reporting.Docs table
- Add CalcClaimDate column, via custom transformation
- Add CA_Direct.Reference.SCSClaim table
- Update data types in Support_PolicyBase schema
- Remove 'Incremental' property from Support.Mapping table
- Remove FrenchBordereaux transaction support scripts and table
- Rename PolicySection to PolicyBase and update schema
- Remove FrenchBordereaux Policy.Policy and Policy.Section tables
- Add new fields to PolicySection and Transaction schemas
- Update schema of multiple tables in FrenchBordereaux model
- Add schema file to FrenchBordereaux.Support.Transaction table
- Update PolicySection schema and ColumnSpecs
- Rename "KeyDekkingsnummer" to "KeyDekkingsNummer" in schema and scripts
- Update the schema of FrenchBordereaux..PolicySection table
- Rename Broker_Name to BrokerName in FrenchBordereaux.Support.Spvie table
- Update and expand PolicySection schema in FrenchBordereaux model
- Add the Support.Transaction table
- Add 'Mapping' to data sources and update the custom transformation

### fix

- test dev deployment
- test dev deployment
- Improvements on Docker Image
- Solve typo on postCreateCommand
- Add submodule update command on te devcontainer.json postCreate
- Add submodule update command on te devcontainer.json postCreate
- Update BrokerFullName filter from PORTFOLIO to MARKEL
- Update BrokerFullName filter from PORTFOLIO to MARKEL
- Update JOIN clause in SCSClaim.sql query to avoid circular dependency and add new source
- Fix transformations in Policy_Section_FR custom transformation
- Add method to fix section product code in klarity.py
- Update column name in filter operations
- Update column name in filtering condition

### refactor

- Update deployment and relationship config
- Change 'Deductible' field data type to string
- Add handling for null values in Support_Portfolio_FR
- Update date reformatting in unit tests and transformations
- Update date pattern and clean up list of columns in Support_Portfolio_FR.py
- Set spark.sql.legacy.timeParserPolicy to 'CORRECTED' and remove unused imports and unnecessary tests
- Update date format patterns in Support_Portfolio_FR
- Add date reformatting function to Support_Portfolio_FR.py
- Refactor Inception_Date calculation into separate function
- Ensure non-null TransactionComponentAmount in policy transaction component for FR partition
- Refactor transformations for French bordereaux processors
- Refactor section rows and addon checks methods into base class
- Refactor policy product column creation to base class
- Update PortfolioProcessor with new columns and transformations
- Refactor transaction component creation in Policy_TransactionComponent_FR.py
- Update PI product descriptions in processors
- Refactor processors in French bordereaux model scripts
- Refactor broker name assignment in processors
- Remove unnecessary table creation methods
- Remove policy table and deduplication methods in processors
- Implement column name validation in policy section table
- Update union method in Support_Transaction_FR.py
- Remove column renaming in processors
- Move the 'KeyDekkingsNummer' column creation to base class
- Improve data union in binder processors
- Add transaction processors in bordereaux transformation
- Refactor processing classes to inherit from BindersProcessor base class
- Refactor processing classes to inherit from BindersProcessor base class
- Refactor the BinderProcessor classes
- Update the cancellation_statuses list
- Update the BinderProcessors and Remove unused base class
- Refactor condition for policy_table union operation

### ci

- Change devcontainer.json

### docs

- Refactor typing in transformations/misc module

### partition

- Add new partition file for French reserving classes
- Add French partition and custom transformations for EDF.Policy tables
- Add transformation script for French policy data

### style

- Update formatting in CA.yaml files for Reporting tables
- Remove unused import from unit test
- Remove unused import in test_misc.py
- Refactor code by removing unnecessary imports
- Remove unnecessary imports

### test

- Add unit tests for Reporting_Claim_CA transformation
- Add test for create_key_dekking_nummer_column function
- Remove Portfolio, Klarity, Hyalin and SPVIE binder processor unit tests
- Remove broker name and deal type assignment tests
- Refactor BindersProcessor unit tests
- Add new date format cases to unit tests
- Refactor datetime reformatting test to use class structure
- Update unit tests for date transformations
- Add new test case to custom_transformations.misc.reformat_datetime_col function
- Change SIRET_Code data type to StringType in tests

## 6.1.6 (2024-07-02)

### table

- Update data source and schema for CA_Direct Docs
- Update data source and schema for CA_Direct Docs

### partition

- Add dateTimeFormat to column specs for CA_Direct..Docs table

## 6.1.5 (2024-07-01)

### table

- Update source table name in SQL query from CA_Direct..MKLCACashAllocation table
- Update source table name in SQL query from CA_Direct..MKLCACashAllocation table

## 6.1.4 (2024-07-01)

### fix

- Allowed NPERM 0 transactions into settlementarraytransactioncomponent
- Allowed NPERM 0 transactions into settlementarraytransactioncomponent

## 6.1.3 (2024-06-26)

### fix

- Update .dev_deployment.yml for Azure Pipelines

## 6.1.2 (2024-06-26)

### fix

- Update .dev_deployment.yml for Azure Pipelines

## 6.1.1 (2024-06-26)

### fix

- Update .dev_deployment.yml for Azure Pipelines
- Update .dev_deployment.yml for Azure Pipelines
- YAT .dev_deployment.yml for Azure Pipelines

## 6.1.0 (2024-06-26)

### feat

- fake feature in order to increase the minor and try to solve issues on ci/cd

### fix

- try solve issue on ci/cd
- Solve issue on Claim Policy For Soname

### ci

- Fix ci pipeline

## 6.0.4 (2024-06-26)

### ci

- Update .dev_deployment.yml for Azure Pipelines

## 6.0.3 (2024-06-26)

### ci

- Update .dev_deployment.yml for Azure Pipelines

## 6.0.2 (2024-06-26)

### ci

- Dummy commit in order to test the pipeline

## 6.0.1 (2024-06-20)

### ci

- Update the hash of submodule

## 6.0.0 (2024-06-20)

### table

- Add CalcQuoteDate column to CA_Direct..Policy table via custom transformation
- Add CA_Direct.Reporting.QuoteDate table
- update PrimaryKey on Portfolio table to handle different DateReference
- update PrimaryKey on Portfolio table to handle different DateReference

### fix

- Update currency transformation and related unit tests
- Swap function sequence in Claim_TransactionComponent_NL

### refactor

- Update ColumnSpecs and Schema for CA_Direct..QuoteDate table
- Switch DataSource from SourceCanada to DatabricksCatalogSQLQuery
- Refactored SQL script for CA_Direct..QuoteDate table to use Databricks dialect
- Enhance currency transformation in EDF

### ci

- Update .gitignore with Qodana environment variable

## 5.0.6 (2024-06-13)

### table

- Add the CalcDevelopmentReference column to Claim, ClaimSettlement, and Trans tables via custom transformation
- Add the CalcDevelopmentReference column to CA_Direct..Trans table via custom transformation
- Add the CalcDevelopmentReference column to CA_Direct..ClaimSettlement table via custom transformation
- Add the CalcDevelopmentReference column to CA_Direct..Claim table via custom transformation

### refactor

- Refactor CalcDevelopmentReference addition in scripts

### ci

- update submodule reference

### test

- Add new test case in add_columns_ca_direct

## 5.0.5 (2024-06-13)

### refactor

- Update Claim Transaction transformation and add misc transformations

## 5.0.4 (2024-06-13)

### table

- Add new Expiry_Date column based on StartDate from 2023 and InitialEffectiveDate from 2024 file
- Add new Expiry_Date column based on StartDate from 2023 and InitialEffectiveDate from 2024 file
- Add KeyDekkingsNummer mapping table
- Add KeyDekkingsNummer mapping table

### partition

- remove FR partition from EDF model until is ready in the new schema

## 5.0.3 (2024-06-12)

### table

- Add new currency columns to the EDF.Policy.SubLimit table, via custom transformations
- Add new currency columns to the EDF.Policy.SubLimit table, via custom transformations
- Add new currency columns to the EDF.Policy.Policy table, via custom transformations
- Add new currency columns to the EDF.Policy.Limit table, via custom transformations
- Add new currency columns to the EDF.Policy.Excess table, via custom transformations
- Add new currency columns to the EDF.Claim.Claim table, via custom transformations

### fix

- Add 'Deductible' column in Policy Section scripts if this not exists
- Refactor currency column addition in Policy_Policy_NL
- Update exchange rate data source in the custom transformations for the Policy_Section table

### refactor

- Add currency conversion to dataframe
- Update function to standardize currency notation
- Add currency columns to different dataframes
- Refactor currency conversion logic in EDF transformations
- Refactor column renaming in add_currency_columns function
- Refactor currency conversion in Policy_Policy_ES script
- Add functionality to convert currency for different tables
- Add method to add new currency columns from list
- Rename EDFCurrencyConversorBase class to EDFTransComponentCurrencyConversorBase

### docs

- Update the Setup Steps section in the README file
- Update the doc strings of the add_missing_ids common function
- Update the doc strings of some methods from the CurrencyConversorBase class
- Update the README file

### partition

- Update the ColumnSpecs in the partition files - Add the currency columns to the EDF.Policy.Excess table
- Update the ColumnSpecs in the partition files - Add the currency columns to the EDF.Claim.Claim table

### test

- Refactor unit test for custom transformation in Policy_Section_DE module

## 5.0.2 (2024-06-11)

### table

- Updated date format and dataType in the schemas for CA_Direct.PricingTool risks tables
- Update date formatting across Canada PricingTool tables
- Updated date format and dataType in the schemas for CA_Direct.PricingTool risks tables

### refactor

- Update date formats in PricingTool_CanadaConsRisks_CA
- Refactor datetime column reformatting function and update tests

## 5.0.1 (2024-06-07)

### fix

- Add new partitions to Policy sources
- Add new partitions to Policy sources

## 5.0.0 (2024-06-06)

### fix

- Solve import issue

### refactor

- align submodules
- Move transformation decorators to traits module

### ci

- test ci fix
- test ci fix
- test ci fix
- test ci fix
- fix broken CI pipeline
- fix broken CI pipeline
- Reorder deployment jobs and add extra cz bumps

### docs

- Add success log to transformation function
- Correct numbering in README.md

### test

- move transformation decorator tests to new dedicated file

## 4.9.2 (2024-06-06)

### fix

- Rename MKLCATransType to MKLCA_TransType in Trans schema
- Rename MKLCATransType to MKLCA_TransType in Trans schema

## 4.9.1 (2024-06-04)

### fix

- use the single currency conversion method

## 4.9.0 (2024-06-03)

### fix

- Add missing ColumnSpecs to UK.yaml
- Rename fields in schema.yaml files

### refactor

- Improve currency solution on EDF to be able to be reused by other models.

### ci

- .gitignore file

### test

- Fix typo in the test

## 4.8.0 (2024-06-02)

### ci

- update submodule reference

### docs

- Update the Setup Steps section in the README file

## 4.7.21 (2024-05-31)

### fix

- Update schemas and partitions for data tables

## 4.7.20 (2024-05-30)

### fix

- Filter on minimum section number and add step explanations

## 4.7.19 (2024-05-30)

### table

- Update the sql queries for ES_OutsideSisNet partition from EDF.Policy submodel
- Update the sql queries for ES_OutsideSisNet partition from EDF.Claim submodel
- Update the sql queries for ES partition from EDF model

### fix

- Fix the LimitBasisCode values in the sql queries for ES and ES_OutsideSisNet partitions from EDF.Policy.Limit table

## 4.7.18 (2024-05-24)

### table

- Add new columns to CA_Direct.PricingTool.CanadaEnvData table via custom transformation
- Add new columns to CA_Direct.PricingTool.CanadaConsData table via custom transformation
- Update CA_Direct.PricingTool mapping tables - Add new columns and Update the encoding

### fix

- Add IPTLiable Field in Policy_Policy_NL.py

### refactor

- Add the add_mapping_columns common custom function

### test

- Add an unit test for add_mapping_columns common custom function

## 4.7.17 (2024-05-23)

### fix

- Move access from dev_mintdatalake_02 to use always the test_mintdatalake_02

## 4.7.16 (2024-05-23)

### table

- Update the Reference_DistributionMethod table - Add new columns and Update dataSource and ColumnSpecs parameters
- Update the Reference_DistributionMethod table - Add new columns and Update dataSource and ColumnSpecs parameters

### fix

- Fix a column name
- Fix a column name

## 4.7.15 (2024-05-20)

### fix

- solve issue with MINES submodule
- Add unittest for Claim_Section_NL.py
- Enable custom transformations
- wip
- wip
- Add NL Logic
- Add NL_Direct tables on EDF for be used by ClaimClaim_NL logic

## 4.7.14 (2024-05-20)

### table

- Add the CalcCellIndetifier calculated column to the CA_Direct.PricingTool.CanadaEnvData table
- Add the CalcCellIndetifier calculated column to the CA_Direct.PricingTool.CanadaConsData table

### refactor

- Update the existCustomTransformation flag to the CA_Direct.PricingTool.CanadaLOBData tables

## 4.7.13 (2024-05-17)

### table

- Update the primary key to the CA_direct.PricingTool.AllRisks table
- Add the primary key to the CA_direct.PricingTool.CanadaEnoRisks table

## 4.7.12 (2024-05-16)

### fix

- Fix the set_calc_priced_bucket function in the custom transformation for the CA_Direct.Silver.Policy table
- Fix the set_calc_priced_bucket function in the custom transformation for the CA_Direct.Silver.Policy table

## 4.7.11 (2024-05-16)

### table

- Add the CA_Direct.Reference.BrokerGroupAndSegmentation table

## 4.7.10 (2024-05-15)

### fix

- Remove drop column command from the GoldHistory_PolicyPolicy_ES custom script

### ci

- update submodule reference

## 4.7.9 (2024-05-14)

### table

- Add the CalcPolicyholderID column to the CA_Direct.Reporting.Policy table
- Update the CA_Direct.Silver.Trans table - Add new columns
- Update the CA_Direct.Reporting.Trans table - Add new columns

### fix

- Fix the column name in the custom transformation
- Fix the dataframe name in the custom transformation

### ci

- update submodule reference

### style

- Improve formatting

## 4.7.8 (2024-05-10)

### fix

- Adapted get_policy_id function to allow for edge cases. New name is get_claim_policy_id().
- Adapted get_policy_id function to allow for edge cases. New name is get_claim_policy_id().

## 4.7.7 (2024-05-10)

### table

- Add the CA_Direct.Silver.RemovedClaimSettlement table
- Add the CA_Direct.Silver.RemovedClaimSettlementSplit table
- Add the CA_Direct.Silver.RemovedClaim table
- Update the CA_Direct.Reporting.ClaimSettlementSplit table to use the Silver.ClaimSettlementSplit table as the data source
- Add the CA_Direct.Silver.ClaimSettlementSplit table
- Update the CA_Direct.Reporting.ClaimSettlement table to use the Silver.ClaimSettlement table as the data source
- Add the CA_Direct.Silver.ClaimSettlement table
- Update the CA_Direct.Reporting.Claim table to use the Silver.Claim table as the data source
- Add the CA_Direct.Silver.Claim table

### fix

- Fix the table name in the custom transformation
- Add a new data source
- Fix the dataframe name in the Reporting_Claim custom script

### refactor

- Rename a variable
- Rename a custom function from filter_policy_records_true to filter_claim_records_true
- Remove unnecessary function 'filter_claim_records'
- Rename some custom functions
- Refactor the filter_id_records_true custom function to simplify the logic
- Refactor the unit tests of filter_id_records_true custom function to use class
- Refactor the filter_id_records_true custom function to handle REMOVAL_REASON_COLUMN_NAME and Add unit tests
- Refactor the filter_policy_records_true function and Add a unit test
- Move functions and unit test from filter_policies_spark to filter_records script
- Update the filter_records_to_remove common custom function to handle REMOVAL_REASON_COLUMN_NAME column

### ci

- update submodule reference

### docs

- Update doc string of the filter_id_records and filter_id_records_true custom functions
- Update doc string of the filter_status custom function
- Add doc string to the add_section_product_code custom function

### test

- Add unit tests for the filter_records_to_remove common custom function

## 4.7.6 (2024-04-30)

### fix

- change SISNET to have the full data to 1900

## 4.7.5 (2024-04-30)

### fix

- New changes on the policy sql files for Outside Sisnet.
- change SISNET to have the full data to 1900

### partition

- Add selectColumnsFromSchema parameter equal False to the Policy.Policy table (ES_OutsideSisNet partition)
- Add enforceSourceSchemaOnStandard parameter equal False to the Policy.Policy table (ES_OutsideSisNet partition)
- Update SQL queries for ES_OutsideSisNet partition from EDF.Policy tables
- Update SQL query for ES_OutsideSisNet partition from EDF.Policy.Transaction table
- Update SQL query for ES_OutsideSisNet partition from EDF.Policy.TransactionComponent table
- Update SQL query for ES_OutsideSisNet partition from EDF.Policy.Section table
- Update SQL query for ES_OutsideSisNet partition from EDF.Policy.Policy table
- Add enforceSourceSchemaOnStandard parameter equal False to some tables from Policy submodel (ES_OutsideSisNet partition)
- Update SQL queries for ES_OutsideSisNet partition from EDF.Policy tables

## 4.7.4 (2024-04-30)

### fix

- change SISNET to have the full data.

## 4.7.3 (2024-04-24)

### table

- Add the PricingTool_CanadaEno table to the CA_Direct.PricingTool.[AllData ad AllRisks] tables via custom transformation
- Add a new column to the CA_Direct.PricingTool tables

### partition

- Add a new data source to the CA_Direct.PricingTool.[AllData ad AllRisks] tables

## 4.7.2 (2024-04-18)

### fix

- Hotifx for wrong calculation of ExchangeRate

## 4.7.1 (2024-04-17)

### fix

- Remove drop of SourceSystem Column on ES Partition

## 4.7.0 (2024-04-12)

### feat

- Update Spanish data mappings and validation

### table

- Update the SQL query and schema of the Reference_DetailedCoverageCode table - Add the CoverageType column
- Update the SQL query and schema of the Reference_DetailedCoverageCode table - Add the CoverageType column
- Change the datatype of the sav_at column in the CA_Direct.PricingTool.AllRisks table
- Update the custom transformation and the ColumnSpecs parameter for some CA_Direct.PricingTool.CanadaLOBRisks tables
- Update the custom transformation for the CA_Direct.PricingTool.CanadaLOBRisks tables
- Add a custom transformation for the CA_Direct.PricingTool.CanadaEnoRisks table
- Change the datatype of the sav_at column in the CA_Direct.PricingTool.CanadaLOBRisks tables
- Add the pol_id column to CA_Direct.PricingTool.CanadaEnoRisks table
- Add the pol_id column to Actuarial.PricingTool.CanadaEnoRisks table

### fix

- Remove the ColumnSpecs content of the DE_Direct.PolicyCode.ExportPolicyCode table
- Make spain use the Correct TransactionComponent fix
- Add multiline flag to Policy
- Add new activity column to KeyReserving function
- Several unused operations and mappings in EDF policy transformation scripts have been removed. The "enforce_nulls_type" function and the "sisnet_mapping_table_df" were not needed and thus removed. Column renaming in Policy_SubLimit_ES.py was simplified to improve code clarity.
- Wrong name in the df_dict
- Update custom_transformation in Claim_Claim_ES_historic.py
- Remove redundant transformation decorator
- Improve data deduplication in sisnet_scs_migration
- Remove dependencies on Support_SisnetSCSMappingTableES
- Add transformation decorators in attritional flag script
- Improve and solve scripts relating to SISNET to SCS data migration
- Add dynamic SISNET-SCS mapping and validation

### refactor

- Update the reformat_datetime_col function including the trim() method
- Add the filter_latest_bound_version common function
- Rename the filter_records_to_remove script to filter_records (to be a generic common script), and Refactor the associated custom scripts
- Add the reformat_datetime_col function
- Change order of the custom transformation and its inner functions

### test

- Add unit test for the reformat_datetime_col function

## 4.6.3 (2024-04-11)

### table

- Add CA_Direct.PricingTool.AllData table and a custom transformation

### refactor

- Add overwriteSourceSystem parameter to some CA_Direct.PricingTool tables

### ci

- update submodule reference

## 4.6.2 (2024-04-10)

### table

- Add CanadaEnoData and CanadaEnoRisks tables to the CA_Direct.PricingTool submodel
- Add CanadaEnoData and CanadaEnoRisks tables to the CA_Direct.PricingTool submodel

### fix

- Remove duplicates on Reference_Underwriters table to fix the custom transformation for Silver.Policy table
- Remove duplicates on Reference_Underwriters table to fix the custom transformation for Silver.Policy table

### refactor

- Update the logic of the SetCalcLimitNet.set_calc_limit_net method
- Make the SetCalcLimitNet.set_calc_limit_net method static

### test

- Improve the unit test for Reporting_MKLCAPolicySectionMaxLimitXS custom transformation

## 4.6.1 (2024-04-04)

### table

- Add the CalcDetailedCoverageCode column via custom transformation

### refactor

- Drop duplicates in Trans table before the join, Fix args for @requires decorator
- Add the @transformation and @requires decorators to some custom functions and Remove unnecessary import
- Remove unnecessary imports from some custom scripts

## 4.6.0 (2024-04-03)

### feat

- Add new decorators to validate and manipulate DataFrame

### table

- Update some data types - change from float to double
- Update YAML files - Add the CalcLimitNet column and new data sources

### fix

- Fix the @requires decorator to handle class methods and Add a DataFrame type check; Fix the instance check of the columns variable
- Rename class from TestableBindersProcessor to _TestableBindersProcessor to solve a PytestCollectionWarning
- Fix checking if df and results are Dataframes in the local environment
- Fix the dataframe name for Reference_HistoricalTreaty table

### refactor

- Add the @requires decorator to two custom functions for some CA_Direct.Reporting tables
- Use the @keep_original_cols decorator in the replace_limitccy_with_currencyid custom function
- Fix the call of the set_calc_limit_net custom function
- Refactor the set_calc_limit_net custom function to use class (SetCalcLimitNet); Add the @transformation decorator; Change the data type from float to double
- Organize the set_calc_limit_net function and Add some comments
- Use a custom function instead of the calculated column creation process to create the KeyReserving column
- Improve logic for the set_calc_limit_net custom function to add the CalcLimitNet column
- Add the set_calc_limit_net custom function to add the CalcLimitNet column (WIP)
- Remove unnecessary Pandas code
- Update the global locale for CA_Direct model

### test

- Add a unit test for the SetCalcLimitNet.set_calc_limit_net method; Refactor the test_replace_limitccy_with_currencyid method to not use the input_dataframes method

## 4.5.6 (2024-04-01)

### refactor

- Optimize the common custom transformation to avoid using the collect command

### test

- Add new test for filter_by_max_value function

## 4.5.5 (2024-04-01)

### partition

- Update SQL query for ES_OutsideSisNet partition from EDF.Claim.TransactionComponent table
- Update SQL query for ES_OutsideSisNet partition from EDF.Claim.Transaction table
- Update SQL query for ES_OutsideSisNet partition from EDF.Claim.Section table

## 4.5.4 (2024-04-01)

### fix

- Correct currency conversion calculation in currency.py
- Correct currency conversion calculation in currency.py
- currency conversion calculation in currency.py
- currency conversion calculation in currency.py

## 4.5.3 (2024-03-28)

### fix

- Refactor code in currency.py for column name conformity

## 4.5.2 (2024-03-28)

### fix

- Fix SQL queries from CA_Direct model - Remove the comments in the last line
- Add directory creation step in transformations script
- Add new column transformation method in currency.py
- Add multi-currency support to TransactionComponent model

### refactor

- Remove unnecessary imports
- Convert some functions to use pyspark instead pandas
- Convert some functions to use pyspark instead pandas and Use the transformation decorator

## 4.5.0 (2024-03-26)

### feat

- Add new currency conversion functionality and update module name

### table

- Add new columns with converted amounts

### fix

- Refactor method name and improve fetching dataType
- Remove threshold validation from attritional flag script
- Remove unused transformation scripts
- update MINES version
- Renamed module and expanded currency conversion functionalities
- Renamed module and expanded currency conversion functionalities

## 4.4.2 (2024-03-22)

### fix

- Update country codes in unit tests
- Remove unused file Reference_ReservingClasses_DE.py
- Update Section partition configuration in DE model
- Update transformation logic and submodule configuration

## 4.4.1 (2024-03-19)

### table

- Add the CalcTransIDAgentID calculated column to CA_Direct.Reporting.Trans table

## 4.4.0 (2024-03-19)

### model

- Add a new model named ES_Direct

### table

- Add the EDF.Reference.BrokerSegmentationES table
- Add the DE_Direct.Reference.PLZRegionMap table

## 4.3.5 (2024-03-19)

### table

- Update SQL query and the schema for the MKLCAPolicySectionMaxLimitXS table
- Update the SQL query and the schema for NL_Direct..ClaimsWatchlist table, Change a column from Country to SourceCountry
- Include the Tarifversion column - Update the YAML files and Add a custom function

### refactor

- Refactor the custom transformation for the EDF.Reference.RateAdequacyDE table to split the main function
- Refactor the custom transformation for the DE partition from EDF.Reference.ReservingClasses table to split the main function

### style

- Improve the formatting of the SQL query of the MKLCAPolicySectionMaxLimitXS table

## 4.3.4 (2024-03-16)

### table

- Remove the primary key from the CA_Direct..MKLCACashAllocation table to avoid send records to quarantine

### ci

- update submodule reference

## 4.3.3 (2024-03-15)

### table

- Update the SQL query for NL_Direct..ClaimsWatchlist table, Change a column from ProductionOffice to Country and Reorder some columns

## 4.3.2 (2024-03-15)

### table

- Add new calculated column named CalcPricedBucket - Update YAML files and Add a new custom function

### fix

- Fix submodel name in custom transformation

### refactor

- Refactor the custom transformation to improve readability

### test

- Add unit test for set_calc_priced_bucket custom function for Silver.Policy table

## 4.3.1 (2024-03-14)

### table

- Temporarily remove  the UK_Demeter.Enhanced.Settlement table

### refactor

- Update the locale parameter for UK_Demeter model

## 4.3.0 (2024-03-11)

### feat

- Update German DataSource to read from the SONAME conversion

### fix

- Refactor custom transformation in unit tests
- Refactor custom transformation in unit tests
- update submodule

## 4.2.12 (2024-03-08)

### table

- Add CA_Direct.Reporting.MKLCACashAllocation table

## 4.2.11 (2024-03-08)

### table

- Add MapConsolidated and MapEnvironmental tables to CA_Direct.PricingTool submodel

## 4.2.10 (2024-03-08)

### table

- Add CanadaConsData and CanadaEnvData tables to the CA_Direct.PricingTool submodel
- Add the Country parameter

## 4.2.9 (2024-03-08)

### table

- Add CA_Direct.PricingTool.AllRisks table and a custom transformation

## 4.2.8 (2024-03-06)

### table

- Add the CA_Direct.Reportin.Docs table

### fix

- Remove commented code

## 4.2.7 (2024-02-29)

### table

- Change the number of days in the add_days function for AsAtDatetime column

## 4.2.6 (2024-02-28)

### table

- Add the primaryKey to the Reference.HistoricalTreaty table
- Add CA_Direct.Reference.HistoricalTreaty table

### fix

- Fix the dataType of the LimitRetention column

## 4.2.5 (2024-02-28)

### table

- Add new date columns to Silver.Trans and Reporting.Trans tables; Add the add_cover_date_columns_from_insuredperson custom function

### refactor

- Refactor the add_cover_date_columns function to use the add_column_from_match_table function

## 4.2.4 (2024-02-28)

### table

- Add a new column named TreatyAgreement to the CA_Direct.Reference.MasterMapping table

## 4.2.3 (2024-02-28)

### table

- Add yaml files and SQL query to new table named ClaimsWatchlist
- Update primary key
- Add a calculated column named ClaimID for ClaimsWatchlist table
- Rename column from AS_AT_DATE to AsAtDatetime, Change its dataType and Add the is_datetime parameter to add_days function
- Rename the Watchlist table to ClaimsWatchlist
- Update schema and partition yaml - Add the AS_AT_DATE column and primary key; Fix data source parameters
- Update SQL query - Add the database name to the edf tables and Improve formatting
- Add yaml files and SQL query to new table named ClaimsWatchlist
- Add Reference.DetailedCoverageCode table (yamls and sql file)

### fix

- Remove redundant dataframe naming in get_claim_event_code
- Remove redundant dataframe naming in get_claim_event_code
- Fix incorrect syntax near the keyword FROM

### ci

- update submodule reference

### partition

- Add specification of date columns
- Add catalog_name parameter in the SQL query
- Remove unnecessary data sources

### test

- Fix unit test for Reporting_BusinessDistribution

## 4.2.2 (2024-02-26)

### fix

- Update default value in lag function

## 4.2.1 (2024-02-26)

### fix

- Improve data frame handling and renaming operations

## 4.2.0 (2024-02-26)

### model

- Create model configuration files to set the old default locale
- Change the default locale to US in the generate_model_json script and Create model configuration files to set the old default locale

### refactor

- Update the Partition._set_default_values method logic to include the partition-level inside submodel-level and Improve the unit tests
- Refactor the Partition.fill_partition method to reduce complexity through the use of the new ConformedColumn class

### style

- Remove unnecessary import
- Conform the NotInSource parameter for a column from CA_Direct.Silver.Trans table

### test

- Change the default locale in the unit tests and Add the model configs to the test artifacts
- Add unit test for Partition._set_default_values method

## 4.1.2 (2024-02-26)

### table

- Add custom transformation for BusinessDistribution table
- Add new data source to BusinessDistribution table
- Add new table for CA_Direct.Reporting submodel

### fix

- Fix a column name in the add_region_column and add_product_label_code_column functions in the custom transformation for Reporting.BusinessDistribution table

### refactor

- Add order_and_select_columns function in custom transformation for Reporting.BusinessDistribution table

### partition

- Update the CalcTransIDAgentID column specification

### test

- Fix the unit test for custom transformation from Reporting.BusinessDistribution table
- Move the unit test to the correct folder
- Add unit test for the custom transformation for BusinessDistribution table
- Fix annotations in the input_dataframes method of some custom transformation tests

## 4.1.1 (2024-02-26)

### table

- Add new reference table for CA_Direct model - Add the Reference.DistributionChannel table.
- Update schema yaml to add the primaryKey
- Add new reference table for CA_Direct model

## 4.1.0 (2024-02-22)

## 4.0.6 (2024-02-22)

### fix

- update associated MINES version
- Update python and dependent libraries versions
- Add temporary ignore for pytest imports in validation script
- fixed relative imports in tests
- rename dataframes for Settlements and PolicyCode
- rename dataframes for Settlements and PolicyCode
- function parameters
- changed checkpoint_dataframe invocation
- fixed models for TransComponent tables

### refactor

- rename sev functions
- rename german namings in Claim area
- rename german namings in Claim area
- update PolicyArrayTransactionComponent
- update PolicyArrayTransaction dataframes
- update PolicyArraySublimit dataframes
- update PolicyArraySection
- update PolicyArrayLimit dataframes
- update PolicyArrayExcess dataframes
- update PolicyPolicy dataframes
- rename policy_df

## 4.0.5 (2024-02-19)

### fix

- PolicyPolicy address issues fixed
- PolicyPolicy address issues fixed

## 4.0.4 (2024-02-18)

### refactor

- Refactor the generate_model_json script to use classes for readability

## 4.0.3 (2024-02-15)

### fix

- Fix the AddUSGAAPDateColumn class to select only the required columns from the gaap_df dataframe and Update unit tests.
- Fix the AddUSGAAPDateColumn class to select only the required columns from the gaap_df dataframe and Update unit tests

## 4.0.2 (2024-02-15)

### table

- Rename column from MKLCAPolicySectionPremiumLineContractDetails table

## 4.0.1 (2024-02-14)

### table

- Update the schema and SQL query for the MKLCAPolicySectionPremiumLineContractDetails table - Add a new column named LinCovCdUI
- Added SQL file for Reference_LinCovCd table
- Added CA.yaml partition file for Reference_LinCovCd table
- Added the schema.yaml for Reference_LinCovCd table

### fix

- Remove a missing column in the source (extra)

### refactor

- Remove the SourceSystem column before running R scripts and add it back at the end of the process
- Add SourceSystem column in process_spain_rate_adequacy and select_columns functions in the custom transformation for Reference.RateAdequacyES table
- Add SourceSystem column to expected_existing_cols in ExtraColumnsClaimTransactionMerger class
- Enable SourceSystem column in the calculate_limits custom transformation function

### ci

- update submodule reference
- update submodule reference
- update submodule reference

### partition

- Add sourceSystem parameter to DE partition tables
- Add sourceSystem parameter to ES_historic partition tables

### test

- Fix unit test to include the SourceSystem column
- Fix unit test to include the SourceSystem column

## 4.0.0 (2024-02-13)

### fix

- Fix the SQL query for Policy_Section table (ES_OutsideSisNet partition)
- Fix the SQL query for Policy_Section table (ES_OutsideSisNet partition)
- Fix the SQL query for Policy_Policy table (ES_OutsideSisNet partition)
- Fix the SQL query for Claim_Transaction table (ES_OutsideSisNet partition)
- fixed duplicates bug in get_claim_insured
- fixed duplicates bug in get_claim_insured
- Update import path in EDF.py
- Update import statements in misc.py

## 3.5.5 (2024-02-07)

### fix

- Rename the partition YAML file to use the correct name (Support.Hyalin table)

## 3.5.4 (2024-02-07)

### fix

- Update date condition in EDF validation

## 3.5.3 (2024-02-05)

### fix

- update mines hash
- Add RestrictNumericValues validation in rule_config.py
- Add rule to validate numeric ranges and improve quarantine reasons

## 3.5.2 (2024-02-01)

### fix

- changed RuiId type for Support_PolicyParameters
fix: changed Decimal to Long type conversion in PolicyCode_ExportPolicyCode
- removed useless DecimalType import
- convetred to int all *_nr columns
- long to decimal type conversion
- PolicyCode changed IntegerType to LongType
- changed RuiId type for Support_PolicyParameters

## 3.5.1 (2024-02-01)

### table

- Add custom transformations for the tables from CA_Direct.PricingTool submodel
- Add restrictNull flag to the pol_id column (tables from CA_Direct.PricingTool submodel)
- Mark the columns that contain PII information (Actuarial.PricingTool tables)

### refactor

- Update custom_transformation to remove code that drops duplicates based on unq_id

### style

- Change the column order to make the invalid data frame easier to see

## 3.5.0 (2024-01-31)

### table

- policy table and policy section
- policy section and policy policy tables
- PolicySection table with custom functions

### fix

- remove pandera dependency

### test

- SPIVE processor tests
- add base processor test and utils tests

## 3.4.0 (2024-01-30)

### feat

- removed PolicyArrayTransactionComponent and SettlementArrayTransactionComponent
- added 5 more unit tests for Soname functions

### table

- Change the data source from DE_Direct.Reference tables (SourceCSV to SourceIncrementalExcel)

### fix

- updated submodule commit to avoid conflicts with main
- get_sev_prorata, get_insurer_carrier_percentage fixed
- minor function changes
- get_sev_gor_gesamtbrutto
- get_currency_winsure arguments
- get_sev_gor_gesamtnetto
- get_sev_zab_berechnen call
- restore changes after merge
- restructure get_transaction_component_amount
- restructure get_sev_gesamtpraemiebrutto
- restructure get_sev_gesamtpraemie
- set Oracle behaviour for duplicated numbers
- get_sev_prorata
- fixed fget_prv_satz and read_provision
- get_transaction_component_amount
- added missing condition in get_provision
- ipt calculation
- get_sev_gor_gesamtnetto
- get_sev_zab_summe procedure
- get_sev_zab_grundlage
- get_sev_gesamtpraemie
- get_sev_zab_summe execution
- refactoring
- get_sev_mindestpraemie_aktiv default value set to True
- fix duplicates in insurance_carrier_percentage
- get_transaction_component_amount
- get_sev_zab_grundlage
- debug get_transaction_component_amount
- debug get_transaction_component_amount
- get_sev_pkg_provision_read_provision_risiko
- debug get_transaction_component_amount
- add optimizations
- switch PolicyArrayTransactionComponent to standard layer
- remove unnecessary change
- remove redundant files
- removed debugging commands from tests
- Fixed cast of rhe result_df in get_assured_annual_turnover.
- Fixed rounding between expected and actual results in get_assured_annual_turnover test.
- fix merge conflicts

### refactor

- cleanup scripts
- rename functions and files

### test

- tests added for 5 functions

## 3.3.16 (2024-01-29)

### fix

- Add future date check to EDF validation script
- Add future date check to EDF validation script

## 3.3.15 (2024-01-25)

### refactor

- Update the limits of max variations for the EDF model validation (Claim and Policy)

## 3.3.14 (2024-01-25)

### refactor

- Improve logs for EDF model validation
- Improve logs for EDF model validation

## 3.3.13 (2024-01-23)

### refactor

- Refactor the custom transformations to use class strutures
- Refactor the custom transformation and unit test to use class strutures
- Add add_inception_date_for_max_id function

### test

- Add unit test for add_inception_date_for_max_id function

## 3.3.12 (2024-01-18)

### test

- Improve unit test ading other cases

## 3.3.11 (2024-01-18)

### fix

- Fix the decorator structure to handle args and no args and class methods, and Improve unit tests
- Fix the decorator structure to handle args and no args
- Fix the decorator structure to handle args

### refactor

- Change the usage of decorator function to handle decorator arguments
- Add a flag to the @transformation decorator to force the creation of a checkpoint and Add unit test

### test

- Add unit tests for decorator function with decorator arguments

## 3.3.10 (2024-01-17)

### table

- Update SQL queries for tables from ES_OutsideSisNet partition

### fix

- Do a rollback to populate the PolicyProductCode column and Remove wrong columns in the SQL query

## 3.3.9 (2024-01-16)

### fix

- Change the databricks-connect version to avoid using version 14.2.1 due to a bug in the withColumnRenamed command

### refactor

- Revert process to rename AGG and AOC columns to use the withColumnRenamed command
- Remove the enforce_nulls_type function (and unnecessary imports)

### style

- Remove unnecessary imports

### test

- Improve the unit test cases

## 3.3.8 (2024-01-15)

### table

- Add new column, data source and custom transformation
- Add new source, Add custom transformations and Update add_usgaap_date function
- Add column to DatesGAAP table and custom transformation
- Add Reference.DatesGAAP table
- Add USGAAP_Date column and custom transformation

### fix

- Fix data type for DatesGAAP table
- Update query for Claim.Policy table (ES_OutsideSisNet partition)
- Update query for Claim.Policy table (ES_OutsideSisNet partition)

### refactor

- Make sure that the data type of TransactionDate column is Date type
- Add assert to make sure that the data type of columns is the same
- Add data source and Refactor custom function
- Improve custom function and unit test

### ci

- update submodule reference

### test

- Add unit test for add_usgaap_date function

## 3.3.7 (2024-01-04)

### fix

- Fix logic to populate the KeyReserving field in ReservingClasses table (ES partition)

### refactor

- Improve logic to populate the KeyReserving field to handle null values

## 3.3.6 (2024-01-01)

### fix

- Solve type of TransactionSequenceNumber

## 3.3.5 (2023-12-29)

### table

- Temporarily remove LegacyRateAdequacyES table

## 3.3.4 (2023-12-29)

### table

- Populate the AttritionalLargeFlag column for ES_OutsideSisNet partition (Claim.Claim table)
- Add filter_intermediary_ids custom function for TransSplit table and Add unit test
- Fix column data type for an Actuarial table
- Update schema - fix data types

### fix

- Refactor and enhance validation in EDF model

### refactor

- Move logic to add_year_of_account function
- Fix warning related to an environment variable that was not set
- Fix warning related to an environment variable that was not set
- Fix warning related to an invalid escape sequence

### style

- Improve formatting

### test

- Improve unit test

## 3.3.3 (2023-12-22)

### table

- Add new column, data source, custom transformation and unit test for Trans table

### docs

- Improve doc string for add_column_from_match_table function

## 3.3.2 (2023-12-22)

### fix

- change datatype in report table
- change datatype in report table

## 3.3.1 (2023-12-21)

### table

- Add new data source and custom transformation
- Add new data source, custom transformation and unit test for manual_fix

### fix

- Fix variable in filter_schemes function
- Install Databricks Connect to fix ModuleNotFoundError

### refactor

- Include new column in manual_fix_section_ids function
- Update _validate_source_type - Use the sources_list variable and Remove redundant assert

### ci

- update submodule reference
- Move mines2 from main to dev dependencies
- Improve poetry installation
- Move mines2 from dev to main dependencies

### style

- Improve formatting
- Improve formatting

## 3.3.0 (2023-12-21)

### model

- Add CreditSafe model

## 3.2.6 (2023-12-19)

### table

- Add TransactionDate to primaryKey in schema.yaml
- Add custom transformation for Claim TransactionComponent
- Add unit tests for `ExtraColumnsMerger` and implement custom transformation
- Add ExtraTransactionsAdjustmentsES table and partition

### fix

- rollback run of pytest
- temp commit to check the pipeline
- Update task scripts with Databricks environment variables
- Remove unwanted column on tests
- Update data transformations and validation
- Update ES.yaml to disable custom transformation
- Remove ExtraTransactionsNTDMARK and update TransactionTypeDescription source
- unnecessary entries from ExtraTransactionsNTDMARK schemas
- Refactor code and amend partition type
- Add unit test and implement custom transformation for ExtraTransactionsAdjustmentsES

### refactor

- update submodule hash
- Refactor code and remove redundant elements
- class and methods in Claim Transaction script
- pytest-xdist to test dependencies

### style

- Apply some hints for pycharm introspection.

## 3.2.5 (2023-12-17)

### fix

- Deleted relationships.yaml that is creating issues
- Deleted relationships.yaml that is creating issues

## 3.2.4 (2023-12-15)

### table

- Change column and the logic for the join
- New support table for Spain; Add custom transformation and unit test | ActivityCode

### refactor

- Fix relationship between ActivityCodeES tables
- Add new relationship for ActivityCodeES tables
- Update the logic for the fill_key_reserving function

### partition

- Add new data source and Update fill_key_reserving custom function to add the ActivityCode

### test

- Update test_fill_key_reserving function
- Add unit test for the fill_key_reserving function

## 3.2.3 (2023-12-15)

### table

- Add new column

### fix

- Handle NoneType in concatenation
- Fix the column name
- Update submodule hash for models
- Add pytest-xdist and use UTC in timestamps

## 3.2.2 (2023-12-13)

### table

- Add CalcSubmissionDate column
- Add CalcSubmissionDate column, Add custom  function and unit test

### docs

- Add missing Setup Step

### style

- Fix formatting

### test

- Fix unit test

## 3.2.1 (2023-12-12)

### fix

- Remove spark.stop() | Method not found in SparkConnectService

### partition

- Fix Policy.Limit query

## 3.2.0 (2023-12-10)

### feat

- Improve efficiency in policy records filtering

### fix

- Refine policy data join operation in filter_policies_spark
- update submodules reference to mines on models repo.

## 3.1.49 (2023-12-07)

### table

- Rename table
- Add reference table for Spain

### refactor

- Allow partition columns in join clause

### style

- Improve formatting to make it easier to read

### test

- Add new unit test for valid relationship

## 3.1.48 (2023-12-04)

### table

- Fix selectColumnsFromSchema parameter

### fix

- Remove wrong file

### style

- Add blanck line in the end of file

## 3.1.47 (2023-12-04)

### table

- Add custom transformation for Claim.Claim
- Add custom transformation for Claim.Policy

### fix

- Fix sqlFileName

### partition

- Add fill_key_reserving custom function
- Add ES_OutsideSisNet partition and queries for Claim submodel

### style

- Fix blanck line in the end of file

## 3.1.46 (2023-12-01)

### table

- Remove symbol ; from the end of the queries
- Rename table from NL_Direct
- Update queries to remove not native columns in EDF

### partition

- Fix the query to use it as a subquery
- Update/Fix selectColumnsFromSchema parameter

### style

- Fix formatting

## 3.1.45 (2023-12-01)

### style

- Apply isort to all scripts in solution

## 3.1.44 (2023-11-30)

### table

- Rename tables from NL_Direct model.
- Rename tables from NL_Direct model

### fix

- Solve issues on testing of logging module
- Solve the wrong type of columns for DE_Direct
- Solve the wrong type of columns for DE_Direct

### refactor

- Apply suggestions from qodana and add qodana.yaml configuration file

### ci

- Update models associated mines version

### test

- Update library for databricks connect

## 3.1.43 (2023-11-28)

### table

- Update query for ES_OutsideSisNet
- Update queries for ES_OutsideSisNet

### refactor

- Enable the handle_null_limits_spark custom function

## 3.1.42 (2023-11-28)

### table

- Add new reference table
- Add new reference table

## 3.1.41 (2023-11-28)

### table

- Add new table for DE_Direct
- Add new table for DE_Direct

## 3.1.40 (2023-11-27)

### fix

- Remove selectColumnsFromSchema parameter for an Actuarial table
- Remove selectColumnsFromSchema parameter for an Actuarial table

## 3.1.39 (2023-11-24)

### fix

- Drop duplicated records on df_underwriters
- Drop duplicated records on df_underwriters

## 3.1.38 (2023-11-24)

### docs

- Change linked submodule commit

## 3.1.37 (2023-11-23)

### table

- Reingest the Claimant table (NL partition)

### fix

- Fix country in DE partition

### refactor

- Update the use of Constants.
- Update the use of Constants

### partition

- Reingest the Claimant table (DE partition)

### test

- Fix expected data in tests

## 3.1.36 (2023-11-22)

### table

- Update data types
- Add reporting tables

## 3.1.35 (2023-11-21)

### table

- Update schema and dataSource parameter

## 3.1.34 (2023-11-21)

### table

- Update primary key

## 3.1.33 (2023-11-21)

### table

- Add new tables for DE_Direct model

### fix

- Rename tables to follow the pattern

## 3.1.32 (2023-11-17)

### table

- Add custom transformations and new data source
- Add custom transformations: add_spanish_country
- Remove audit columns from queries

### fix

- Remove unnecessary dataSource

### refactor

- Remove Country column from queries
- Remove unnecessary imports and constants
- Rename add_country function
- Update constants, Fix doc string and Reorder functions
- Update constants to use from mines2

### style

- Remove extra empty row

## 3.1.31 (2023-11-17)

### table

- Add new reference table
- Add new reference table

## 3.1.30 (2023-11-16)

### fix

- Fix condition in assertion
- Refactor and Fix condition in conform_euro_currency function
- Fix query
- Remove wrong column
- Fix selectColumnsFromSchema parameter
- Move queries to submodel folder
- Fix files - CRLF line terminators
- Fix file name

### refactor

- Convert pandas functions to pyspark
- Refactor custom_transformation
- Move conform_euro_currency function to common script
- Convert pandas functions to pyspark
- Add calculate_limits common function and unit test
- Remove unnecessary fix_new_renewal_values_es script

### docs

- Add ToDo
- Fix doc string

### partition

- Add custom transformation
- Add new partition ES_OutsideSisNet for EDF.Policy
- Add sql queries for EDF.Policy

### style

- Fix formatting

### test

- Add unit test for handle_null_limits_spark
- Add unit test for conform_euro_currency function

## 3.1.29 (2023-11-15)

### fix

- update submodule
- Fix Issue of duplication of PolicySection when backwardPopulation for NL
- Solve issue for SubLimit and Excess generating duplicates on NL backpropagation
- Add backward population to excess, limit and sublimit table
- Add missing import on backwarD_populate_records
- Apply new R script
- fix R script
- Move extra TransactioncomponentNL queries to new support submodel
- typo on source type
- Add id columns as columns to avoid copy over

### refactor

- Update some relationships
- Update some relationships

### partition

- Change logic for backpopulate records on NL

## 3.1.28 (2023-11-15)

### table

- Add new custom_transformation and unit tests for Reporting.Users table.
- Add new custom_transformation and unit tests

## 3.1.27 (2023-11-14)

### table

- Add new calculated column.
- Add new calculated column

## 3.1.26 (2023-11-13)

### style

- Fake Commit
- Fake Commit

## 3.1.25 (2023-11-10)

### table

- Indicate PII columns
- Indicate PII columns

## 3.1.24 (2023-11-09)

### table

- Update primary key
- Indicate PII columns
- Remove PII indication
- Indicate PII columns

## 3.1.23 (2023-11-09)

### table

- Add column, data source, custom transformation and unit test

## 3.1.22 (2023-11-08)

### table

- Add new columns
- Add new columns and custom transformation
- Add new columns, custom transformations and unit tests

### fix

- Remove column selection in custom transformation

### perf

- Remove counts

## 3.1.21 (2023-11-02)

### fix

- Change DE_Direct wrongly date columns to datetime
- Change DE_Direct wrongly date columns to datetime

## 3.1.20 (2023-11-01)

### table

- Make LimitMap as a data source
- Make LimitMap as a data source

## 3.1.19 (2023-11-01)

### table

- Change Flags for NL

### fix

- Make code compare correctly the KeyIdPolis by converting to int

## 3.1.18 (2023-10-31)

### table

- Add new column, custom transformation and unit test
- Rollback RenewalPolicyIndicator field and set_renewal_indicator function
- Add new column, custom transformation and unit test
- Add new columns and custom transformations
- Add new column and Updated custom script

### fix

- Fix custom transformation and unit test
- Fix table name in custom_transformation
- Fix custom transformation and unit test
- Drop duplicates in  match df

### refactor

- Convert functions to use PySpark and Add unit test
- Add new custom transformation
- Add unit test for add_section_product_code function
- Change the concat function
- Refactor filter_schemes_to_remove function to use PySpark
- Add filter_schemes functions
- Fix column prefix in add_column_from_match_table
- Add asserts to add_column_from_match_table function

### docs

- Update doc string

### test

- Add unit test for Silver_Policy_CA transformation
- Update test
- Add test for filter_schemes function
- Add new tests for add_column_from_match_table
- Improve tests for add_column_from_match_table
- Add new test for add_column_from_match_table

## 3.1.17 (2023-10-30)

### table

- Update sql query for MKLCAPolicySectionDetails table
- Update data sources and Add custom transformation

### test

- Add unit test for Reference_Underwriters custom transformation

## 3.1.16 (2023-10-30)

### fix

- Call function

### refactor

- Add fix_business_classification function for ES and unit test

## 3.1.15 (2023-10-29)

### fix

- Remove space from custom script

## 3.1.14 (2023-10-29)

### fix

- HistoricPolicy Processing wrong column

## 3.1.13 (2023-10-29)

### fix

- Tables naming were wrong on GoldHistoric tables

## 3.1.12 (2023-10-27)

### fix

- in DE_Direct relationship allow vertrag without schaden

## 3.1.11 (2023-10-27)

### table

- Add new column
- Fix the source column names

## 3.1.10 (2023-10-27)

### fix

- Change sourceName from HistoricPolicy

## 3.1.9 (2023-10-27)

### table

- Add new GoldHistory PolicyStatus

## 3.1.8 (2023-10-27)

## 3.1.7 (2023-10-27)

## 3.1.6 (2023-10-26)

### fix

- Remove Partitions from data source
- Remove Partitions from data source

## 3.1.5 (2023-10-25)

### table

- Add AttritionalFlag

### fix

- rollback partitions on DE and NL
- Add missing Partitions
- fix dict naming on custom transformation claim_claim
- remove circular dependency
- Add Threshold to CSV
- Add AttritionalLargeFlag as notInSource column
- on ClaimClaim custom reference_reservingclass should be reference_reservingclasses

## 3.1.4 (2023-10-25)

### table

- Add new reference table for Canada
- Add new reference table for Canada

## 3.1.3 (2023-10-25)

### fix

- Fix rule to define between New and Renew
- Fix rule to define between New and Renew

## 3.1.2 (2023-10-23)

### table

- Add ClaimReport.Bordereux table

### refactor

- Disable some relationships
- Replace filter_last_date by filter_by_max_value function
- Add filter_by_max_value function

### test

- Add unit test for filter_by_max_value function | Remove test_filter_last_date

## 3.1.1 (2023-10-22)

### ci

- Fix naming of the script

## 3.1.0 (2023-10-22)

### feat

- Change locations of custom scrip

### fix

- adjust rule of post validation to business changes of requirements

## 3.0.30 (2023-10-22)

### refactor

- Remove rdd
- Add new filter_policy_records and collect_values_from_column functions
- Add collect_values_from_column function

### test

- Add unit test for filter_policy_records
- Refactor test

## 3.0.29 (2023-10-20)

### table

- Add new reference table and custom transformation + unit test
- Add new reference table and custom transformation + unit test

## 3.0.28 (2023-10-20)

### fix

- Fix renewal values for ES
- Fix condition in fix_renewal_values_de function
- Fix renewal values for NL
- Fix renewal values for DE

### refactor

- Add manual_fix_renewal_values_es function and unit test
- Add replace_column_values_to_lowercase function
- Add remove_whitespace function
- Rename path of unit test
- Improve add_column_from_match_table function and Move to another folder
- Improve add_column_from_match_table function

### style

- Add ToDo
- Remove unnecessary import
- Remove unnecessary imports

### test

- Improve test_manual_fix_renewal_values_es
- Update test
- Add unit test for add_column_from_match_table function
- Add unit test for Policy_Policy_ES script
- Improve unit test for fix_renewal_values_de function
- Add unit test for Policy_Policy_DE script
- Add unit test for Policy_Policy_NL script
- Add unit test for Policy_Policy_NL script

## 3.0.27 (2023-10-20)

### refactor

- Update the set_record_bucket function
- Update the set_record_bucket function

## 3.0.26 (2023-10-18)

### table

- Remove new column
- Add new column
- Remove new column
- Add new column
- Add new column
- Add new columns
- Add new column

### style

- Remove unnecessary ColumnSpecs item

## 3.0.25 (2023-10-18)

### fix

- Disable wrong relationship
- Fix sql query for table from Canada

## 3.0.24 (2023-10-17)

### ci

- Copy scripts for model pipeline

## 3.0.23 (2023-10-17)

### ci

- make CopyDataLake works on other branches

## 3.0.22 (2023-10-16)

### table

- Update Reporting.PolicyLocation table and Add custom transformation
- Add Silver.PolicyLocation table and custom transformation

### refactor

- Verify if number of rows changed

### ci

- Change DATABRICKS_CLUSTER_ID parameter

### style

- Fix formatting

## 3.0.21 (2023-10-16)

### fix

- Solve AddKeyReserving issue

### refactor

- Add extra commands to devcontainer

### test

- Create unittest for custom transformation

## 3.0.20 (2023-10-13)

### table

- Add primary key
- Add new table for Canada and sql query

### fix

- Remove unnecessary filter in sql query

### refactor

- Add new relationship
- Update data types and fix column name

### partition

- Change dataSource

### style

- Remove unnecessary file
- Remove white spaces

## 3.0.19 (2023-10-12)

### fix

- hotfix for settlement_section

## 3.0.18 (2023-10-11)

### table

- Create unique ids for TransSplit table
- Create unique ids for TransHeader table
- Create unique ids for Trans table
- Create unique ids for PolicyXSLimit table
- Create unique ids for PolicySection table
- Create unique ids for PolicyLocation table
- Create unique ids for PolicyClause table
- Create unique ids for MasterMapping table
- Create unique ids for MKLCAPolicySectionPremiumLineContractDetails table
- Create unique ids for MKLCAPolicySectionMaxLimitXS table
- Create unique ids for MKLCAPolicySectionDetails table
- Create unique ids for MKLCALinPolicyTrans table
- Create unique ids for Policy table
- Create unique ids for ClaimSettlementSplit table
- Create unique ids for ClaimSettlement table
- Create unique ids for Claim table

### fix

- Remove KeyReserving column (PolSecRef not exists)
- remove comment
- Remove empty column ExtraInfo1 #31402

## 3.0.17 (2023-10-11)

### refactor

- Update custom transformation and Add unit test
- Update custom transformation and Add unit test

### ci

- Update env variables
- Add env variables

## 3.0.16 (2023-10-10)

### table

- Add ObjectFreeField table for NL_Direct
- Add ObjectFreeField table for NL_Direct

### fix

- Fix column names

## 3.0.15 (2023-10-09)

### feat

- added tests for date_custom module
- added sev.gor_gesamtbrutto
- Added checkpoint to zab_grundlage.
- converted markel.package_provision.fget_prv_satz
- created constants for PolicyPolicy load
- added markel_own functions
- migrated excess and deductible changes from Oracle
- added sev.pkg_schaden.get_zahlungs_datum
- get_typ_buchen and get_pkg_felgtext added
- parametrization for PolicyCode added
- Load parametrization for Winsure

### table

- Integrate get_sev_rechnungop_praemie_vm procedure
- add rechnungop procedure
- Integrate get_sev_rechnungop_praemie_vm procedure
- implement get_sev_gefahr_typ_buchen
- Policy tables added
- Add new table exportpolicycode

### fix

- Add Not In Source
- changed submodule commit to avoid merge conflicts
- code refactoring
- switch to standard layer and pyspark rounding issue
- restore merge changes
- add checkpoints
- update get_sev_gesamtpraemiebrutto
- PolicyCode fix datetime to date
- removed @transformation for now
- Functions fixes.
- remove redundant logic
- add missing call to get_sev_gor_gesamtnetto
- remove parameter from get_sev_gor_gesamtnetto
- get_sev_gor_gesamtnetto coalesce function
- get_sev_gor_gesamtnetto logic condition
- get_sev_gor_gesamtnetto output column list
- add missing condition
- get_sev_vers_steuersatz
- get_sev_mindestpraemie_aktiv
- Fixed zab_grundlage.
- Fixed zab_grundlage.
- redundant distinct
- variable name
- format and add F.lit
- format
- format
- Update PolicyCode related procedures
- update PolicyCode logic
- fixed Settlement issues
- minor changes per comments
- changed ap_bp parameter to optional
- changed checkpoint to use decorator
- PolicyCode
- add default 0 value
- update comments
- update comments
- add function comments
- fix policyarraylimit DE.yaml
- bugfix policy limit and sublimit
- revert models_scripts/transformations/de_direct/common/etl/policy.py
- replace udf function
- update comments
- add function comments
- fixed policy tables
- get_claim_risk(_type), get_claim_event_code
- updated submodule path
- returm submodule version for merge
- replace '' to None
- fixed deduplication for get_sev_typ_ebene
- return tables, fix get_sev_feldtext
- return tables, fix get_sev_feldtext
- pulled latest submodule
- get_gefahr_typ_buchen
- get_claim_risk_type procedure
- remove redundant ColumnSpecs
- changed MINES submodule commit to the latest from main
- Add ColumnSpec to PolicyCode
- Change PolicyCode datatypes
- Change comparison column datatype
- changed test name

### refactor

- Refactored de_direct/common/edf
- Refactored de_direct/common/edf
- Changed col_names to Columns in params
- Add methods documentation to PolicyCode
- Add datatypes to PolicyCode

## 3.0.14 (2023-10-08)

### table

- Add table PlanAllocation

### fix

- Mark KeyReserving as calculated
- Remove incremental ingestion flag from settlement section
- Add missing logic for KeyReserving on Settlement tables
- Add handle for direct read, will be used for when we want to avoid the normal naming conventions
- Remove Temporary the Primary Key on vertrag
- remove datetimes with forbidden .ff pattern
- Apply restrictNull and needsEpoch

## 3.0.13 (2023-10-05)

### table

- Add new columns

### refactor

- Add new custom transformation to Silver.Trans table and refactor functions to use pyspark

### perf

- Improve the filter_policy_records function

## 3.0.12 (2023-10-04)

### table

- Add new calculated column
- Add new calculated column

### fix

- Fix conformCurrency flag values
- Fix restrictNull flag values

### test

- Fix conformCurrency flag value

## 3.0.11 (2023-10-03)

### fix

- Fix imports in custom transformations | EDF.Policy.Policy tables
- re-Add custom transformation for LimitBasis table
- Fix imports in custom transformations

## 3.0.10 (2023-10-03)

### fix

- change commit on submodules2

## 3.0.9 (2023-10-03)

### fix

- Create custom scripts for Settlement section

## 3.0.8 (2023-10-02)

### fix

- yat

## 3.0.7 (2023-10-02)

### fix

- yat

## 3.0.6 (2023-10-02)

### fix

- yat

## 3.0.5 (2023-10-02)

### fix

- run_mines now are being exported to the right folder

## 3.0.4 (2023-10-02)

### fix

- run_mines not being exported

## 3.0.3 (2023-10-02)

### ci

- yat on fix devops pipelines

## 3.0.2 (2023-10-02)

### ci

- fix download of submodule

## 3.0.1 (2023-10-02)

### ci

- remove submodules from bump
- yat on fix devops pipelines
- Fix missing folder

## 3.0.0 (2023-10-02)

### major

- Change from Base to Models logic

### feat

- Mines as a submodule for Models
- Migrate Custom Transformations to Base Repo

### fix

- add more flags
- Remove back unused job
- Remove back unused job
- missing f on f-strings

### ci

- Try follow a tutorial to run submodules in de
- Try follow a tutorial to run submodules in devops
- yat
- yat
- Add PersistCredentials
- Add installing of git submodule
- Add mines submodule to the test

## 1.20.23 (2023-09-29)

### fix

- Remove PII for SONAME as required
- change flags to be boolean

## 1.20.22 (2023-09-28)

### fix

- Fix table and column names

### refactor

- Create yaml file for relationship validation process

## 1.20.21 (2023-09-28)

### table

- Add new calculated column and primary key

## 1.20.20 (2023-09-28)

### table

- Add Reference.LimitMap table
- Add new column and new datasource - LimitBasis

## 1.20.19 (2023-09-27)

### table

- Ad new column for Policy.Policy table

## 1.20.18 (2023-09-27)

### table

- Update schema and sql query for Canada table

### refactor

- Update sql query for Canada table
- Update sql query for Canada table

## 1.20.17 (2023-09-21)

### fix

- update column names names of new tables

## 1.20.16 (2023-09-21)

### table

- Add new Tables for PotfolioOptimisation

## 1.20.15 (2023-09-20)

### partition

- Add custom transformation to Settlement NL tables
- Add custom transformation to Settlement NL tables

## 1.20.14 (2023-09-20)

### refactor

- Add custom transformation to Settlement Trans NL table

## 1.20.13 (2023-09-20)

### table

- Add unique ids to Demeter tables

### fix

- Fix parameter in concat function
- Fix Country parameter in yaml partitions

### refactor

- Add Partition class

### test

- Add test_partition method

## 1.20.12 (2023-09-20)

### table

- Add new reference table - LimitBasis
- Add new reference table - LimitBasis

## 1.20.11 (2023-09-20)

### table

- Move table to another submodel
- Move table to another submodel

## 1.20.10 (2023-09-15)

### fix

- hotfix for production issue on ReservingClass ES

## 1.20.9 (2023-09-14)

### fix

- add handle for out of bounds dates

## 1.20.8 (2023-09-13)

### fix

- minutes and months in wrong position on yaml

## 1.20.7 (2023-09-13)

### fix

- minutes were using the format of months for new DE tables

## 1.20.6 (2023-09-13)

### fix

- Add handle for string dates on DE_Direct

## 1.20.5 (2023-09-13)

### fix

- improve safety of IsCurrentFlag by adding YearOfAccount

## 1.20.4 (2023-09-13)

### fix

- Extra fixes on new tables

## 1.20.3 (2023-09-13)

### fix

- missing partitions on main branch
- Change rules for Earliest and Latest Policy Flags

## 1.20.2 (2023-09-12)

### table

- Add new tables and columns to DE_Direct

## 1.20.1 (2023-09-12)

### fix

- The ReservingClass was generating wrong KeyReserving

## 1.20.0 (2023-09-11)

### model

- Add new model UK_Demeter
- Add new model UK_Demeter

### table

- Change column name to camel case
- Fix spaces in column names

### fix

- Fix column source name
- Remove duplicated column

### refactor

- Remove unidecode function

## 1.19.1 (2023-09-08)

### fix

- wrong source column name on GeneralCorrespondance table

## 1.19.0 (2023-09-08)

### feat

- Add selectColumnsFromSchema flag

### table

- Remove columns missing on source table

### fix

- avoid enforcing of source schema on cada tables with calculatedd columns
- selectColumnsFromSchema in Actuarial tables
- Make limit and sublimit policy tables stop using the conformed schema approach
- Add Flags to columns not in the Source
- Avoid using Schema on the query for RateAdequacyUK, since it contain complex declare functions
- Remove Description from Diaryin NL_Direct since it was missing in the source
- Remove Section from TableSection in NL_Direct since it was missing in the source
- Remove PolicyNumber not present on the source for PolicyMemo
- Remove column missing in source
- Change tables from FULL_TABLE to SCHEMA

## 1.18.7 (2023-09-07)

### table

- Add first Assur table (GeneralCorrespondance)
- Add first Assur table (GeneralCorrespondance)

## 1.18.6 (2023-09-06)

### fix

- Fix script

### refactor

- Add parquetValidName parameter and Update tests
- Add parquetValidName parameter and Update tests

## 1.18.5 (2023-09-01)

### table

- Add three new tables to NL_Direct

## 1.18.4 (2023-08-31)

### fix

- Temporary remove GeneralCorrespondance

## 1.18.3 (2023-08-31)

### fix

- Change to custom script

## 1.18.2 (2023-08-31)

### table

- Add automatic calculation to ReservingClasses

### fix

- Remove unwanted .sql file
- Remove optional flag

## 1.18.1 (2023-08-31)

### fix

- Apply change on sql for Claim NL

## 1.18.0 (2023-08-31)

### model

- Add new tables to NL_direct

### table

- Add new tables and columns to DE_Direct

### fix

- Remove wrong relations on DE_direct relationships.yaml
- Add SectionCode
- Make Project.json ignore

## 1.17.43 (2023-08-28)

### fix

- Fix filter to reduce data volume from log table
- Fix filter to reduce data volume from log table

## 1.17.42 (2023-08-28)

### table

- Add Reporting.RemovedPolicySection table
- Add Silver.PolicySection table

### partition

- Change source to use the Silver table

## 1.17.41 (2023-08-24)

### table

- Add new columns to MemoClaims table to NL_Direct model
- Add new columns to MemoClaims table

## 1.17.40 (2023-08-24)

### fix

- crossPartition primarykeycontrain rule wasn't checking duplicates.
- crossPartition primarykeycontrain rule wasn't checking duplicates.

## 1.17.39 (2023-08-23)

### table

- Add new columns

### partition

- Add new columns to settlemente transactions tables

## 1.17.38 (2023-08-23)

### table

- Add calculatedColumn flag
- Move Transaction table to new IntegerIds
- Move TransactionComponent to new IntegerIds

### fix

- Add sublayer to table level calculated columns

## 1.17.37 (2023-08-22)

### table

- Add missing column on PolicyPolicy

## 1.17.36 (2023-08-22)

### fix

- Solve issue on missing column

## 1.17.35 (2023-08-22)

### table

- Update schema, partition, data souce and sql query - ExchangeRate table

### fix

- Solve issue on missing column

## 1.17.34 (2023-08-21)

### fix

- Add Back ClaimsPolicyID to easy the transition

## 1.17.33 (2023-08-21)

### fix

- Connection claim_Policy and policy_ policy german

## 1.17.32 (2023-08-20)

### fix

- Make Intermediate tables have the right type

## 1.17.31 (2023-08-20)

### fix

- Add Requests to poetry toml

## 1.17.30 (2023-08-20)

### fix

- Conform schema to provided one

## 1.17.29 (2023-08-20)

### fix

- Try changing ClaimPolicy and PolicyPolicy connection

## 1.17.28 (2023-08-19)

### fix

- Add Partitions filter to yaml

## 1.17.27 (2023-08-18)

### fix

- Change folder name
- Temp change folder name to force change in case

## 1.17.26 (2023-08-18)

### table

- Changes to enable LIVE and FINAL partitions

### fix

- Fix wrong Period on yaml Last_Quarter
- Final currentStats should have ResultSet = Final
- Add IsFinal Column to CurrentStats
- Change type of Dynamic flag to boolean
- Add live version to EuropeanDataLake

## 1.17.25 (2023-08-17)

### table

- Add Policy Year of Account on ClaimClaim
- Add Policy Year of Account

## 1.17.24 (2023-08-17)

### table

- Remove outstanding claims on spain

## 1.17.23 (2023-08-16)

### fix

- Change source table for schaden_historie

## 1.17.22 (2023-08-16)

### fix

- Primary Key with wrong name on DE_Direct table

## 1.17.21 (2023-08-16)

### table

- Add Reporting.Removed[Trans] tables
- Add Silver.TransSplit table
- Add Silver.TransHeader table
- Add Silver.Trans table

### partition

- Change source to use the Silver table

## 1.17.20 (2023-08-16)

### table

- Change submodel name
- Change Table name and add partitions

## 1.17.19 (2023-08-15)

### fix

- wrong type name for string

## 1.17.18 (2023-08-14)

### fix

- Wrong type name

## 1.17.17 (2023-08-14)

### fix

- Remove wrong column

## 1.17.16 (2023-08-13)

### table

- Remove PII flag from UserId column
- Remove ChangeLogDetail table | temp solution

### fix

- small tweaks to new tables
- Changes for Soname work

### partition

- Change data source to use query | Reduce data volume

## 1.17.15 (2023-08-09)

### table

- Add new ClaimHistoricTable

### refactor

- Change order of columns
- rollback space changes

## 1.17.14 (2023-08-08)

### table

- Add Notes table for Canada
- Add Notes table for Canada

## 1.17.13 (2023-08-03)

### table

- Add new column from custom transformation for Reporting.Users table
- Add new column

### fix

- Fix primaryKey flag - replace primaryKeys to primaryKey

### refactor

- Change new column position

### partition

- Add new data source

### style

- Fix formatting - remove extra spaces

## 1.17.12 (2023-08-02)

### table

- Add new log reference table for Canada

### partition

- Change dataSource for MKLCALinPolicyTrans table

## 1.17.11 (2023-07-31)

### table

- Add new reference table for Canada
- Add new reference table for Canada

### fix

- Remove old command

## 1.17.10 (2023-07-28)

### fix

- Adapt Name of source column on MasterMapping to new cleaned one on standard
- Adapt Name of source column on MasterMapping to new cleaned one on standard
- PK table missing in prod, hotfix already applied in prod

## 1.17.9 (2023-07-25)

### table

- Add new calc columns created in silver table.
- Add new calc columns created in silver table

## 1.17.8 (2023-07-23)

### fix

- Revert remove dynamic update of SPAIN in a hard way, since some tables have different requirements.

## 1.17.7 (2023-07-20)

### fix

- True transformation was being applied to wrong table on ProfitStudy
- Define custom validation for ProfityStudy

## 1.17.6 (2023-07-20)

### fix

- rollback to pipe for spain reserving classes

## 1.17.5 (2023-07-20)

### partition

- fix csv file name for es_historic reservinclass

## 1.17.4 (2023-07-20)

### table

- Adjust column name and split partition for reservingClass

## 1.17.3 (2023-07-20)

### fix

- Remove dynamic update of SPAIN in a hard way, since some tables have different requirements.

## 1.17.2 (2023-07-20)

### fix

- hotfix for sublimits breaking

## 1.17.1 (2023-07-20)

### partition

- Make sublimit custom for ES and ES_historic

## 1.17.0 (2023-07-19)

### model

- Add ClaimID to all Claim tables
- Add PolicyID to all tables on settlement and Policy submodel

### fix

- Add Country custom to all tables

### partition

- Add Policy table as a source for German
- Add customTransformation for german
- Remove Country from german partition

## 1.16.11 (2023-07-19)

### ci

- remove deployment poetry from pipeline

## 1.16.10 (2023-07-15)

### ci

- remove build from pipeline

## 1.16.9 (2023-07-15)

### fix

- remoe poetry build from base

### refactor

- migrate standard layer

### ci

- remove migrations
- fix validation pipeline

## 1.16.8 (2023-07-14)

### table

- Add calculated columns for AccountContact table
- Add calculated columns and data source for Policy table

## 1.16.7 (2023-07-12)

### table

- Add new column and data source
- Add new tables for PrincingTool submodel
- Rename some tables for PrincingTool submodel

### fix

- Update table parameter in data source - PrincingTool
- Fix table name

## 1.16.5 (2023-07-11)

### refactor

- remove mines2 from base repo
- Remove clean code from mines

## 1.16.4 (2023-07-11)

### fix

- remove unecessary tests

## 1.16.3 (2023-07-06)

### fix

- hotfix for names of notifications files

## 1.16.2 (2023-07-06)

### table

- Add new Reference table (yamls and sql query file)
- Add new Reference table (yamls and sql query file)

### fix

- Fix table name

## 1.16.1 (2023-07-06)

### table

- Rename CA_Direct..LinPolicyTrans table
- Rename CA_Direct..LinPolicyTrans table

## 1.16.0 (2023-07-05)

### feat

- Add new source, SourceParquet, to ingest parquet files from uploads.

### fix

- adjust schema
- change parameters for SourceParquet
- automatically remove special character from parquet source.

## 1.15.16 (2023-06-29)

### table

- Add new columns and Update sql query
- Add new columns and Update sql query

## 1.15.15 (2023-06-27)

### table

- Add new reporting table - LinPolicyTrans
- Add new reporting table

## 1.15.14 (2023-06-26)

### fix

- Fix the country name in some calculated columns - table EDF.Policy.Policy_ES_historic
- Fix the country name in some calculated columns

## 1.15.13 (2023-06-26)

### fix

- Add missing parameter sep on partition DE.yaml
- Add missing parameter sep

## 1.15.12 (2023-06-26)

### table

- Add new table for RateAdequactES

### fix

- Change dataSource name
- Name of tec_ratio to tech_ratio
- make join of RateAdequacyES custom be based on PolicyID
- Add asserts to improve robustness22
- Add asserts to improve robustness
- name of the source for RateAdequacyES

### style

- Add new line in the end of file
- remove unecessary decimal places

## 1.15.11 (2023-06-23)

### refactor

- Add new dataSource for Trans.. tables

### partition

- Change the existCustomTransformation parameter

## 1.15.10 (2023-06-22)

### refactor

- Fix case
- Fix dataSource type

### partition

- Change source type for Canada reference table; Add sql query file

## 1.15.9 (2023-06-22)

### partition

- Change the dataSource for Canada tables

## 1.15.8 (2023-06-15)

### table

- Replace special characters in the column name
- Replace special characters in the column name

## 1.15.7 (2023-06-15)

### refactor

- temporarily removing a relationship
- temporarily removing a relationship

## 1.15.6 (2023-06-15)

### table

- Replace special characters in the column name
- Replace special characters in the column name

## 1.15.5 (2023-06-14)

### table

- Fix column names from a Reference table
- Fix column name from Policy table
- Fix column names from a Reference table

## 1.15.4 (2023-06-14)

### fix

- Fix filter_status method to handle null values correctly

## 1.15.3 (2023-06-12)

### fix

- hotfix for get_tables being able to handle : in partition

## 1.15.2 (2023-06-12)

### fix

- hotfix of missing commit removing incremental_old logic and change layer of AccountOther to clean

## 1.15.1 (2023-06-12)

### fix

- Remove PolicyID column because is calculated
- Update query for a data source of Policy.Policy table

### refactor

- Improve merge logic and unit test
- Update custom transformation for Policy.Policy ES and ES_historic
- Remove formatting KeyIdPolis code

### partition

- Add duplicated query and new data source

## 1.15.0 (2023-06-11)

### feat

- Migrate Clean layer to Databricks
Change source of crossTable to delta.
Change the format to delta.
feat: Generic functions for write on datalake. That would letter better log of the changes.
ci: remove automatic run of tests before commit, since it was taking too much time
ci: Changes to make the debug on local mode easier.

### fix

- make get_tables to save the table_by_phases json
- Change Layer from published to clean because new restriction

## 1.14.14 (2023-06-07)

### refactor

- replace Attacama to Ataccama

## 1.14.13 (2023-06-06)

### fix

- Add PII flags for documentation

### refactor

- Add replace_sectionid method

## 1.14.12 (2023-06-05)

### table

- Add Reporting.AccountOther table

## 1.14.11 (2023-06-05)

### refactor

- Add new filter to fix_underwriter_based_on_schemeid method

## 1.14.10 (2023-06-02)

### refactor

- Improve custom script and test - add new case for empty string
- Fix custom script
- Improve custom script and test - add new case for empty string

## 1.14.9 (2023-06-01)

### table

- Add new columns and custom transformation
- Remove Null constraint
- Add new columns and custom transformation

## 1.14.8 (2023-05-31)

### table

- Add WebsureContractReferences table and query

## 1.14.7 (2023-05-30)

### table

- Add SettlementType table
- Add CatastropheCode table

### fix

- hotfix the data type of PolicyID for RateAdequacyES
- Change default value for enforceSourceSchemaOnStandard flag

### refactor

- Fix KeyIdPolis format from new_renewal data source
- Add enforceSourceSchemaOnStandard parameter; Reorder keys
- Add enforceSourceSchemaOnStandard parameter
- Remove lib
- Add custom script and unit test

### partition

- Add new data source new_renewal; Add sql query

### style

- Fix formatting

## 1.14.6 (2023-05-26)

### table

- Update RateAdequacyES to use the new code

## 1.14.5 (2023-05-24)

### refactor

- Add new common function remove_policy_records

### partition

- Add custom transformation to MKLCAPolicySectionPremiumLineContractDetails table
- Add custom transformation to MKLCAPolicySectionMaxLimitXS table
- Add custom transformation to MKLCAPolicySectionDetails table

## 1.14.4 (2023-05-22)

### table

- Add AccountClient table, custom script and unit test
- Add AccountBrokerAcc table, custom script and unit test
- Move Account table to Silver submodel

## 1.14.3 (2023-05-22)

### table

- Change query, source and schema from ClaimStatus table
- Change query, source and schema from ClaimStatus table

### fix

- Change the ValidFrom column name to source name

### refactor

- Change existCustomTransformation parameter
- Add custom transfomation to ClaimStatus table

### test

- Add unit test for custom transfomation of ClaimStatus table

## 1.14.2 (2023-05-18)

### table

- Add/Rename calculated columns to/from Settlement.TransactionComp table
- Rename calculated column from Settlement.Transaction table
- Add calculated column to Settlement.Policy table
- Add calculated columns to Settlement.Section table

### fix

- Change column names

### refactor

- Update join_clause of relationships for Settlement tables

### docs

- Add comment

## 1.14.1 (2023-05-17)

### refactor

- Update schema and partition for MasterMapping table

## 1.14.0 (2023-05-16)

### model

- Add PricingTool submodel

### refactor

- Add new data source

### style

- Improve formatting

## 1.13.17 (2023-05-16)

### refactor

- Updated schema.yaml
- Updated CA.yaml

## 1.13.16 (2023-05-12)

### refactor

- Update schemas and sql query files for tables from Canada

## 1.13.15 (2023-05-10)

### table

- Add table Silver.Policy; Add custom script and unit test
- Add table RemovedPolicy; Add custom script and unit test
- Change data source of Policy table; Update custom script and unit test

### refactor

- Fix table source name
- Move to correct folder

### partition

- Add new data source to Trans table; Update custom script and unit test

## 1.13.14 (2023-05-09)

### refactor

- Remove some relationships for CA_Direct model

## 1.13.13 (2023-05-02)

### partition

- Change dataSource for Claim table

## 1.13.12 (2023-05-02)

### table

- Change schema and data source; Add sql file - PolicySectionMaxLimitXS
- Change schema and data source; Add sql file - PolicySectionDetails

### partition

- Add dateTimeFormat parameter

## 1.13.11 (2023-05-01)

### table

- Add new reference table for CA_Direct model

### fix

- Remove primaryKey

### partition

- Changed query for RateAdequacyDE

## 1.13.10 (2023-04-28)

### table

- Change schema and data source from view
- Change schema and data source from view

## 1.13.9 (2023-04-28)

### table

- Add RateAdequacyUK table

### fix

- Conform column names
- Fix query - comment some commands
- Remove quotes from query

### refactor

- Add uniform_null__values_to_apply parameter
- Remove primary key
- Fix column data types
- Add query for RateAdequacyUK

## 1.13.8 (2023-04-27)

### table

- Add new flag columns

## 1.13.7 (2023-04-26)

### table

- Add new column to Policy table from custom script

## 1.13.6 (2023-04-26)

## 1.13.5 (2023-04-25)

## 1.13.4 (2023-04-25)

### refactor

- Remove print; Add ToDo
- Stop uploading relationships file to ADL

## 1.13.3 (2023-04-24)

### fix

- Add new id columns in Settlment Transaction and Settlement TransactionComponent

## 1.13.2 (2023-04-20)

### table

- Add new reference tables for CA_Direct model
- Add new reference tables for CA_Direct model

### refactor

- Make UniformNullValues class handle values to apply and values to ignore

### partition

- Add flag uniform_null__values_to_ignore

## 1.13.0 (2023-04-14)

### model

- Make the submodel EDF.Attacama a new model

### table

- Add new column and new source to Policy table
- Add new reference table DistributionMethod
- Add new Silver_RateAdequacyDE

### fix

- Change dataSource type of RateAdequacyDE table
- Change dataSource type of RateAdequacyDE table
- hotfix to RateAdequacyDE
- handle multiples years on rateAdequacy
- change year to date as a parameter in RateAdequacy
- change expected parameter to RunId
- Change destination folder to be scripts/mines

### refactor

- write tables.json to data lake. So the new Silver Process can read it from there
- write tables.json to data lake

### perf

- Delete dataframes after use

### partition

- Add New Query

## 1.12.18 (2023-04-05)

### fix

- Update source column name

## 1.12.17 (2023-04-04)

## 1.12.16 (2023-04-04)

## 1.12.15 (2023-04-04)

## 1.12.14 (2023-04-04)

### fix

- add back exali for RateAdequacy
- Handle null values on PolicyProductCode for ES_historic

## 1.12.13 (2023-04-04)

## 1.12.12 (2023-04-03)

### table

- Add new calculated columns

## 1.12.11 (2023-04-03)

### table

- Add schemas
- Add new tables from views for CA_Direct.Reporting

### fix

- Add querySourceType parameter in partition of tables from DE_Direct model

### refactor

- Change the querySourceType parameter
- Change the querySourceType parameter
- Rename source table name
- Change the querySourceType
- Handle white spaces in source column name
- Update SQLQuery classes
- Add SQLQueryFromFullTable class; Handle white spaces in source column name
- Handle white spaces in source column name

### partition

- Add sourceName to some columns
- Add sourceName to one column
- Add new Columns on RateAdequacyDE
- Added Exali, MarkelNow contracts; new fields

## 1.12.10 (2023-03-30)

### table

- Add new reference table Underwriters

### refactor

- Improve method to get invalidation_info and quarantine_col
- Add new etl rule for Policy_CA custom script and new unit test

### partition

- Add new source to Policy table

## 1.12.9 (2023-03-28)

## 1.12.8 (2023-03-28)

### fix

- Change type of one relationship from CA_Direct model

## 1.12.7 (2023-03-27)

### fix

- solve dedup on RateAdequacyDE and rollback PolicyLastModifiedDate and solve relationship

## 1.12.6 (2023-03-24)

### fix

- drop_duplications wasn't work as expected

## 1.12.5 (2023-03-23)

### table

- Add new calculated column for Trans table

### fix

- Add e handle new flag NotInSource

### test

- Add unit test for Reporting_Trans_CA custom script

## 1.12.4 (2023-03-23)

### fix

- hotfix for production

## 1.12.3 (2023-03-22)

### partition

- Change ClaimPolicyID column for NL
- Change column for NL

## 1.12.2 (2023-03-22)

### table

- Add new Custom Transformation
- Add new Reference table
- New reference table and new records for Claim ES

### fix

- custom script

## 1.12.1 (2023-03-21)

### test

- Add artifacts for handle multiple phases

## 1.12.0 (2023-03-20)

### feat

- Enable automatic handling multiple phases. Add new table and custom script

### fix

- get_tables dependency solver
- ReUnderwriting schema
- remove unneed split

### style

- add couple of annotations

## 1.11.21 (2023-03-20)

### fix

- remove one relationship fromCanada

### partition

- Remove columns missing in the schema

### style

- fix formatting

## 1.11.20 (2023-03-16)

### style

- remove space

## 1.11.19 (2023-03-16)

## 1.11.18 (2023-03-16)

### fix

- types in schema migrations

## 1.11.17 (2023-03-16)

### table

- Create migration script for handle None in currency
- substitute ExchangeRates from Eclipse to Igloo
- Create migration script for change schema on sublimit ES
- Change expectation for Limit table on german
- Change sublimit table to have AGG and AOC

### fix

- add back SubLimitBasisCode column
- add back limit and sublimit

### test

- Create unit test for Reference_ExchangeRate_UN custom script

## 1.11.16 (2023-03-15)

### fix

- Updated NL.yaml to create the Unique ClaimsSectionPolicyID in the ColumnsSpecs instead of custom transformation
- Updated Policy_Section_NL.py by excluding the Creation of ClaimsPolicySectionID
- Updated Query ClaimSectionArray.sql with old KeyIDpolis solution

## 1.11.15 (2023-03-14)

### fix

- hotfix for epoch_converted referenced before assingment

## 1.11.14 (2023-03-14)

### partition

- Change locale of German partitio to US

## 1.11.13 (2023-03-14)

## 1.11.12 (2023-03-13)

### table

- fix relationship

### fix

- remove code that was failing because of default value being a dict

## 1.11.11 (2023-03-13)

### fix

- remove limit and sublimit

## 1.11.10 (2023-03-13)

## 1.11.9 (2023-03-10)

### fix

- Fix filter_schemes function
- Change data type of SchemeID

### refactor

- Add get_source_pdf function in move_to_clean scripts.
- Add custom scripts for Canada tables
- Add new source for filter_schemes from Canada
- Add filter_schemes function
- move tests of custom scripts to folder of models
- move custom scripts to folders of models
- Add custom_transformation for Reporting_Claim_CA
- Add get_source_pdf function in move_to_clean scripts

## 1.11.8 (2023-03-10)

### partition

- add primarykey to RateAdequacy
- Change sql and schema for RateAdequacy.sql

## 1.11.7 (2023-03-10)

### table

- Add new table for exclusions on Claim Claim

### fix

- enable load data from published on rootPath

### partition

- adjust Claim Claim to apply exclussions

## 1.11.6 (2023-03-09)

### fix

- missing maptype on calculated columns

## 1.11.5 (2023-03-09)

## 1.11.4 (2023-03-09)

### fix

- rollback reservingClass change
- do not temporarily use the relationship for ReservingClasses
- rollback reservingClass change

## 1.11.3 (2023-03-07)

### table

- Add WebsureInsuranceProducts table for Canada
- Add WebsureInsuranceProducts table for Canada

## 1.11.2 (2023-03-03)

### fix

- Handle nulls in currency name after add_missing_ids function

## 1.11.1 (2023-03-02)

### table

- Add table InsuredPerson for Canada; Update relationships

### style

- add white space in the end of file

## 1.11.0 (2023-03-02)

### feat

- Add new flag conformCurrency on schema and new rule ConformCurrencyName

### refactor

- make the generate_invalid_pdf func as a method of TableData class

### style

- rename test_check_tables_and_columns method

### test

- Improve test_clean_space__uniform_null__conform_currency
- Add unit test for UniformNullValues rule
- add unit test for ConformCurrencyName rule

## 1.10.3 (2023-02-28)

## 1.10.2 (2023-02-28)

### fix

- Change enconding for ES_historic partition
- Change encoding for ReservingClasses partitions
- Fix relationship in EDF (ReservingClasses)

### refactor

- Include check_row_number method in Table class; Fix formatting

### partition

- add ES_historic partition for ReservingClasses

## 1.10.1 (2023-02-27)

### table

- Add new table WebsureSchemes for Canada (CA_Direct model).
- Add new table WebsureSchemes for Canada

## 1.10.0 (2023-02-27)

### feat

- New Validation Step
- New Validation Step

### ci

- Add relationships for EDF model

### test

- Add new case in test_check_tables_and_columns

## 1.9.6 (2023-02-23)

### table

- New ReservingClass table for Canada

### fix

- wrong name of primary keys on Canada ReservingClass schema yaml
- table name wrong on custom transformation (Policy_Limit_DE)

## 1.9.5 (2023-02-23)

### fix

- Remove unused .sql
- parse_number failing on RateAdequacyES
- make column null for netherland source

### partition

- Add new query from Kevin

## 1.9.4 (2023-02-23)

### fix

- move float to the right place

## 1.9.3 (2023-02-23)

### fix

- duplication on Transaction Table

## 1.9.2 (2023-02-22)

### fix

- make object be always save as string

## 1.9.1 (2023-02-22)

### ci

- make bump and changelog avoid new ci

## 1.9.0 (2023-02-22)

### table

- added new logic for RateAdequacy ES
- add schema to igloo DevPatterns
- add schema to igloo ClaimTriangles
- Add new tables for Igloo

### fix

- Updated 0_run_calculation.R to add Commission to NetPremium as for European s...
- Wrong column name
- change locale of ES_historic
- fix ES queries
- start using generic mapping for igloo

### refactor

- make code able to handle multiple partitions

### ci

- add .databricks to gitignore
- add cz changelog to pipeline

### partition

- Add New Calculated Column
- Add SectionProductCode to Keyreserving for germany

### test

- add unittest for new custom transformation

## 1.8.4 (2023-02-14)

### fix

- move tblClaim to T10 source

## 1.8.3 (2023-02-14)

### table

- Add ClaimStatus table using As-At concept
- Add new Claim Canada Tables
- Add back Contract Year

### fix

- add new source in definition + fix missing partitions folder

### build

- Stop running unittest before each commit

## 1.8.2 (2023-02-14)

### fix

- remove tables; fix table and column names

### refactor

- add check_tables_and_columns

### test

- add test for check_tables_and_columns

## 1.8.1 (2023-02-13)

### fix

- remove unused type enforcing
- make all audit columns types enforced

### test

- add regression test for enforce audit field type

## 1.8.0 (2023-02-13)

### feat

- handle relationships

### model

- add relationships

### fix

- improve check duplicates

### refactor

- add submodel name to table names
- change if/raise to assert; change to verify the real keys
- replace N to 0..N

### test

- add test for invalid table names
- add new case for duplicate relations
- add case for valid relationship
- add new tests for check_relationships

## 1.7.6 (2023-02-09)

### refactor

- change output of process_dataframe; use read_from_datalake and write_to_datalake

## 1.7.5 (2023-02-08)

### ci

- add pyyaml lib to main dependencies and remove it from dev/tests

### style

- fix formatting
- remove comments
- fix space/word

### test

- remove commented code

## 1.7.4 (2023-02-08)

### table

- add new RateAdequacy table

## 1.7.3 (2023-02-07)

### refactor

- check if files exist before deleting
- remove '|' to avoid TypeError

## 1.7.2 (2023-02-06)

## 1.7.1 (2023-02-03)

### fix

- add missing SourceCanada source
- hotfix to solve quarter issue

## 1.7.0 (2023-02-02)

### table

- New Actuarial Table

### fix

- Temporary Delete tables with error
- Add support for reducing date on datetime_to_string
- prepare igloo to handle unexpected characters
- multiples fixes after big bang commit
- Add new source to definition

### refactor

- merge similar data sources

## 1.6.5 (2023-02-01)

### table

- create binding columns
- update primary keys to include country
- remove empty primaryKeys parameter
- update unique ids to include country

### test

- improve test for update_data_source_sql_table
- add test for source not expected

## 1.6.4 (2023-01-31)

### fix

- settlement_transaction_component solve duplication issue

## 1.6.3 (2023-01-30)

### table

- add new unique primary key columns
- Remove ContractYear table.
- Add new AsAt tables for Canada.

### fix

- as_at_table columns
- wrong data type for object column

### build

- Add small fixes to the commitzen custom setup
- Add custom types to commitzen and pre-commit hook

## 1.6.2 (2023-01-29)

### refactor

- set data type of some audit columns
- make sql_query_table handle empty schema
- restore tables with PII
- change existCustomTransformation parameter
- add new parameter sql_table
- handle PII in different environments
- write query from columns

### style

- remove commented code

### test

- write query from columns

## 1.6.1 (2023-01-27)

### refactor

- sort items
- remove primaryKey from some tables
- fix parameter name primaryKey
- remove tables with pii
- rename dataSources to dataSource
- add new data type

### ci

- populate schemas for Canada

### style

- fix spaces

### test

- add primaryKey and PII paramenters on tests

## 1.6.0 (2023-01-27)

### feat

- dynamic parameters on yaml
- New Igloo Schema

### fix

- make code work on python3.8
- model location change
- Change model name and location

### refactor

- remove unused extension function

## 1.5.1 (2023-01-27)

### refactor

- remove legacy code

## 1.5.0 (2023-01-26)

### feat

- add generic function copy_activity_json

### refactor

- use copy_activity_json function
- use copy_activity_json function
- add new data type
- update get_parameters and move_to_standard functions
- update get_parameters and set_schema functions

### build

- move lib to dev
- add openpyxl package

### ci

- add tables for new model CA_Direct

### test

- add test for get_parameters function

## 1.4.7 (2023-01-25)

### fix

- change upsert to require a primary key

### style

- remove extra parentheses

### test

- remove test_incremental_ingestion_without_pk

## 1.4.6 (2023-01-24)

### fix

- add extra audit columns to all tables

### refactor

- change position of create_output_json func

### test

- add test for create_audit_columns function

## 1.4.5 (2023-01-23)

### fix

- Apply suggestions from code review

### ci

- add schema to sev_partner_anschrift table
- add new table 'sev_partner_anschrift'

## 1.4.4 (2023-01-23)

### fix

- add GOR_UNGUELTIG_AB open bond
- make three more columns has open bounds
- add openBound to POL_GUELTIG_AB
- unifrom null value to apply to all data types
- Add handle for more possible nulls
- Add openBound concept
- Wrong convertion of date without date pattern
- change custom transformation to false

## 1.4.3 (2023-01-23)

### fix

- make avro work with empty tables

## 1.4.2 (2023-01-20)

### fix

- remove condition about strings with null
- handle nulls in the CleanWhiteSpaces class
- wrong convertion of date without date pattern

### refactor

- change position of rules
- improve condition about null values

### ci

- do not remove the output file

### test

- add test TestCleanWhiteSpaces
- improve test_set_schema_with_date

## 1.4.1 (2023-01-19)

### fix

- make string when clean white spaces

### refactor

- add binary types

## 1.4.0 (2023-01-18)

### fix

- remove duplicate key in partition file
- change not_exist_ok to False

### refactor

- rename data source in partitions - de_direct
- add new type 'clob'
- add exception for OutOfBoundsDatetime
- use regex for search key (type)
- add except on type convertion
- update map_type

### ci

- change keyvault on dev pipeline
- change datalake for devops

### style

- dummy change to test pipeline

### test

- add test for set_schema with date

## 1.3.8 (2023-01-16)

### feat

- add move_to_standard script
- make the read_from_datalake function handle avro
- add write_avro function
- make the read_from_datalake function handle csv

### fix

- change wrong constant
- change condition in set_schema
- update read_avro to deal with other types as string
- convert columns of the pdf_main to str

### refactor

- change parameters in get_parameters
- rename main function
- change csv by txt in test_write_to_datalake_wrong_format
- change csv by txt in test_read_from_datalake_wrong_format
- rename TestApplyIncrementalIngestion class and methods
- replace apply_incremental_ingestion by upsert
- change order of dataframes in concat
- add 'file_path' variable
- rename test_write_to_datalake function
- add classes
- change dataset_parameters from Attacama

### docs

- improve docstring and annotation
- add docstring to TestApplyIncrementalIngestion

### style

- remove comments
- improve formatting
- improve annotations

### test

- add map to convert types and unit test
- add new cases and tests for set_schema
- add test for empty schema
- change dtype
- add unit test for move_to_standard
- simplify test for read avro from datalake
- add unit test for write_avro
- add test to read avro from datalake
- add test to read csv from datalake

## 1.3.7 (2023-01-06)

### fix

- fix use of the clean_white_spaces function
- change data type to make merge
- change data type to make merge
- update conditions to apply NumbersFormatting

### refactor

- make the atof arg as str type

### ci

- change included branches for trigger

### style

- improve formatting
- improve formatting
- change formatting

## 1.3.6 (2023-01-05)

### fix

- change wrong constant
- add new pk for transaction and transactionComponent

### perf

- add cleanup

### ci

- new migration function
- install new pre-commit hooks

### style

- remove unused ;

## 1.3.5 (2023-01-02)

### fix

- solve issue on past migration

## 1.3.4 (2023-01-02)

### fix

- add missing removal and index=False

### ci

- try to export the keyvaultname
- remove wrong poetry shell
- improve naming on pipeline

## 1.3.3 (2023-01-02)

### fix

- move to poetry shell on the beggining

### ci

- another test

## 1.3.2 (2023-01-02)

### fix

- identation

## 1.3.1 (2023-01-02)

### fix

- another try

## 1.3.0 (2023-01-02)

### feat

- migrations functionality - base

### fix

- add missing quote
- try to fix connection name
- change name of main branch
- remove prefix used for test on branch
- update name of ClaimHistoric submodel on one-shot
- pre-commit yaml
- example change
- add "input" as default value for storage parameter
- change script type to bash
- add missing _ on job name
- another try
- try to fix the CI yaml

### ci

- test change type of activity
- avoid running the pipeline twice because of the bump
- try on fixing credential issue
- try to fix dev_deployment
- make poetry run on pre-commit hook
- fix yamls, poetry shell don't work on azure pipelines
- fix yamls
- validations yaml
- update yaml deployment with migrations
- merge with main
- Create new manual deployment shell script

### docs

- add a todo remember

### test

- add new test cases for datalake, dataframes and a entire script for batch
- add tests for datalake, migration and upsert functions
