# edf_databricks

The 'edf_databricks' project was generated by using the default-python template.

## Getting started

1. Install the Databricks CLI from https://docs.databricks.com/dev-tools/cli/databricks-cli.html

2. Authenticate to your Databricks workspace:
    ```
    $ databricks configure
    ```

3. Access the root folder of the package where the databricks.yml file is located:
    ```
    $ cd edf_databricks/
    ```

4. Use the bundle validate command to validate your bundle before deploying it to your workspaces:
    ```
    $ databricks bundle validate
    ```

5. To deploy a development copy of this project, type:
    ```
    $ databricks bundle deploy --target dev
    ```
    (Note that "dev" is the default target, so the `--target` or `-t` parameter
    is optional here.)

    This deploys everything that's defined for this project.
    For example, the default template would deploy a job called
    `[dev yourname] edf_databricks_job` to your workspace.
    You can find that job by opening your workpace and clicking on **Workflows**.

6. Similarly, to deploy a production copy, type:
   ```
   $ databricks bundle deploy --target prod
   ```

7. To run a job or pipeline, use the "run" comand:
   ```
   $ databricks bundle run
   ```

8. Optionally, install developer tools such as the Databricks extension for Visual Studio Code from
   https://docs.databricks.com/dev-tools/vscode-ext.html. Or read the "getting started" documentation for
   **Databricks Connect** for instructions on running the included Python code from a different IDE.

9. For documentation on the Databricks asset bundles format used
   for this project, and for CI/CD configuration, see
   https://docs.databricks.com/dev-tools/bundles/index.html.
