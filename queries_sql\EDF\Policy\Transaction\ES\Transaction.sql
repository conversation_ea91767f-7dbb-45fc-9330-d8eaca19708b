SELECT DISTINCT
    B.POLICODE AS 'KeyIdPolis',
    <PERSON><PERSON>SECTREFE AS 'KeyDekkingsNummer',
    A<PERSON>ID_MEDFPOTR AS 'KeyFactuurnummer',
    A<PERSON>ORICURCO AS 'OriginalCurrencyCode',
    A<PERSON>RATEOF<PERSON> AS 'RateOfExchange',
    A<PERSON>UR<PERSON> AS 'SettlementCurrencyCode',
    CAST(A.TRASDATE AS DATE) AS 'TransactionDate',
    A<PERSON>TRANREFE AS 'TransactionReference',
    A<PERSON><PERSON><PERSON><PERSON><PERSON> AS 'TransactionTypeCode',
    A<PERSON>TRATYPDE AS 'TransactionTypeDescription',
    A<PERSON>TRASEQNU AS 'TransactionSequenceNumber'
FROM
    NTJDWHMRK..MEDFPOTR A,
    NTJDWHMRK..MEDFPOLI B,
    NTJDWHMRK..MEDFPOSE C
WHERE
    B.ID_MEDFPOLI = C.ID_MEDFPOLI_FK
    AND C.ID_MEDFPOSE = A.ID_MEDFPOSE_FK
    AND B.FECHALTA >= CONVERT(datetime,'01/01/2000',103)
