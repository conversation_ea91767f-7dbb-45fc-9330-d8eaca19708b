import pyspark.sql.functions as F
import pyspark.sql.types as T
from pyspark.sql import Column, DataFrame

from models_scripts.transformations.common.traits import transformation


def add_key_reserving_scs(
    main_df: DataFrame, policy_df: DataFrame, activity_code_df: DataFrame
) -> DataFrame:
    """Add KeyReserving column to the dataframe for SCS partition"""

    clean_section_product_code_col = uniform_null(F.col("SectionProductCode"))
    clean_policy_product_code_col = uniform_null(F.col("PolicyProductCode"))
    clean_section_reference_col = uniform_null(F.col("SectionReference"))
    clean_assured_main_activity_code_col = uniform_null(
        F.col("AssuredMainActivityCode")
    )

    cond_is_migrated = clean_section_product_code_col.isNotNull()

    policy_code_for_key_reserving_col = F.when(
        cond_is_migrated, clean_section_product_code_col
    ).otherwise(clean_policy_product_code_col)

    assured_activity_for_key_reserving_col = F.when(
        cond_is_migrated, clean_section_reference_col
    ).otherwise(clean_assured_main_activity_code_col)

    return add_key_reserving_spain(
        main_df,
        policy_df,
        activity_code_df,
        policy_code_for_key_reserving_col,
        assured_activity_for_key_reserving_col,
    )


def add_key_reserving_sisnet(
    main_df: DataFrame, policy_df: DataFrame, activity_code_df: DataFrame
) -> DataFrame:
    """Add KeyReserving column to the sisnet DataFrame"""
    return add_key_reserving_spain(main_df, policy_df, activity_code_df)


@transformation
def add_key_reserving_spain(
    main_df: DataFrame,
    policy_df: DataFrame,
    activity_code_df: DataFrame,
    policy_code_col: Column | None = None,
    assured_activity_col: Column | None = None,
    activity_col: Column | None = None,
) -> DataFrame:
    """Add KeyReserving column to the dataframe"""
    original_cols = main_df.columns
    policy_selected_df = policy_df.select(
        "PolicyID", "KeyIdPolis", "PolicyProductCode", "AssuredMainActivityCode"
    )
    activity_selected_df = activity_code_df.select(
        "PolicyID", F.col("ActivityCode").cast(T.StringType()).alias("ActivityCode")
    )

    merged_policy_df = main_df.join(policy_selected_df, on=["KeyIdPolis"], how="left")
    merged_df = merged_policy_df.join(activity_selected_df, on=["PolicyID"], how="left")
    if policy_code_col is None:
        policy_code_col = F.col("PolicyProductCode")
    if assured_activity_col is None:
        assured_activity_col = F.col("AssuredMainActivityCode")
    if activity_col is None:
        activity_col = F.col("ActivityCode")

    cond_is_rc02 = policy_code_col == F.lit("RC02")
    cond_is_actp0741 = activity_col == F.lit("ACTP0741")

    cond_special_activity = cond_is_rc02 & cond_is_actp0741

    key_reserving_base_col = F.concat_ws(
        ":", F.lit("ES"), F.coalesce(policy_code_col, F.lit(""))
    )
    key_reserving_rc02_col = F.concat_ws(
        ":",
        F.lit("ES"),
        F.coalesce(policy_code_col, F.lit("")),
        F.coalesce(assured_activity_col, F.lit("")),
    )
    key_reserving_special_activity_col = F.concat_ws(
        ":",
        F.lit("ES"),
        F.coalesce(policy_code_col, F.lit("")),
        F.coalesce(assured_activity_col, F.lit("")),
        F.coalesce(activity_col, F.lit("")),
    )

    key_reserving_col = (
        F.when(cond_special_activity, key_reserving_special_activity_col)
        .when(cond_is_rc02, key_reserving_rc02_col)
        .otherwise(key_reserving_base_col)
    )

    with_key_reserving_df = merged_df.withColumn("KeyReserving", key_reserving_col)

    if "KeyReserving" not in original_cols:
        original_cols.append("KeyReserving")
    output_df = with_key_reserving_df.select(*original_cols)

    return output_df


def uniform_null(col: Column) -> Column:
    """Replace null string values with None"""

    list_of_expected_forms_of_str_null = [
        "null",
        "nan",
        "none",
        "na",
        "nat",
        "n/a",
        "<na>",
    ]

    cond_is_in_expected = F.lower(col).isin(list_of_expected_forms_of_str_null)
    cond_is_null = col.isNull()
    uniformed_col = F.when(cond_is_in_expected | cond_is_null, F.lit(None)).otherwise(
        col
    )

    return uniformed_col
