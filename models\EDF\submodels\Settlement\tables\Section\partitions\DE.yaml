Country: custom
existCustomTransformation: 'True'

dataSource:
- name: main
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: dev_europeandatalake_01
      uat: uat_europeandatalake_01
      prod: prod_europeandatalake_01
    schema: DE_Direct
    table: Settlement_SettlementArraySection
    sourceSystem: SPARK_EDF_EXPORT

- name: policy
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Policy
    Table: Policy

ColumnSpecs:
  KeyDekkingsNummer:
    sourceName: KeyDekkingsnummer
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: DE
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  PolicySectionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: DE
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  KeyReserving:
    NotInSource: True
