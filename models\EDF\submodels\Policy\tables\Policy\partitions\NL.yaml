Country: NL
existCustomTransformation: 'True'

dataSource:
- name: main
  type: SourceProgressNL
  parameters:
    sqlFileName: EDF Policy Full.sql
    querySourceType: SQL_FILE

- name: re_underwriting
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Reference
    Table: ReUnderwriting

- name: broker
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: SupportNL
    Table: Broker

- name: exchange_rate
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: test_mintdatalake_02
      uat: test_mintdatalake_02
      prod: prod_mintdatalake_02
    schema: rdm_dbo
    table: mint_mapping_roe
    sourceSystem: MINT_RAW_DATALAKE

ColumnSpecs:
  ClaimsPolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  KeyIdPolis:
    locale: en_US.utf8
  AdministrationOfficeCode:
    locale: en_US.utf8
  AssuredAddressArea:
    locale: en_US.utf8
  AssuredAddressCity:
    locale: en_US.utf8
  AssuredAddressStreet:
    locale: en_US.utf8
  AssuredAnnualTurnover:
    locale: en_US.utf8
    sourceName: AssAnnTo
  AssuredAnnualTurnoverCurrency:
    locale: en_US.utf8
    sourceName: AssAnnToCur
  AssuredCode:
    locale: en_US.utf8
  AssuredCountry:
    locale: en_US.utf8
  AssuredFullName:
    locale: en_US.utf8
  AssuredMainActivityCode:
    locale: en_US.utf8
  AssuredMainActivityDescription:
    locale: en_US.utf8
  AssuredNumberOfFullTimeEmployees:
    locale: en_US.utf8
    sourceName: AssNrOfFTE
  AssuredPostcode:
    locale: en_US.utf8
  AssuredProvince:
    locale: en_US.utf8
  AssuredShortName:
    locale: en_US.utf8
  AssuredState:
    locale: en_US.utf8
  AssuredTerritory:
    locale: en_US.utf8
  BrokerAddressArea:
    locale: en_US.utf8
  BrokerAddressCity:
    locale: en_US.utf8
  BrokerAddressStreet:
    locale: en_US.utf8
  BrokerCodeID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: BrokerCode
        sep: ':'
  BrokerCountry:
    locale: en_US.utf8
  BrokerFullName:
    locale: en_US.utf8
  BrokerGroupCode:
    locale: en_US.utf8
  BrokerGroupName:
    locale: en_US.utf8
  BrokerPostcode:
    locale: en_US.utf8
  BrokerProvince:
    locale: en_US.utf8
  BrokerShortName:
    locale: en_US.utf8
  ClaimsBasisCode:
    locale: en_US.utf8
  ClaimsBasisDescription:
    locale: en_US.utf8
  CoverholderCode:
    locale: en_US.utf8
  CoverholderName:
    locale: en_US.utf8
  CustomerClassification:
    locale: en_US.utf8
  CustomerClassificationCode:
    locale: en_US.utf8
  DistributionPartner:
    locale: en_US.utf8
  DistributionType:
    locale: en_US.utf8
  ExpiryDate:
    dateTimeFormat: ISO
    locale: en_US.utf8
  IBCIndustryCode:
    locale: en_US.utf8
  InceptionDate:
    dateTimeFormat: ISO
    locale: en_US.utf8
  InsurerEntityCode:
    locale: en_US.utf8
  InsurerEntityDescription:
    locale: en_US.utf8
  LapsedReason:
    locale: en_US.utf8
  Layer:
    locale: en_US.utf8
  OrderPercentage:
    locale: en_US.utf8
  OutwardsFACIndicator:
    locale: en_US.utf8
  PeerReview1:
    locale: en_US.utf8
  PeerReview1Code:
    locale: en_US.utf8
  PeerReview1Comment:
    locale: en_US.utf8
  PeerReview1Date:
    dateTimeFormat: ISO
    locale: en_US.utf8
  PeerReview2:
    locale: en_US.utf8
  PeerReview2Code:
    locale: en_US.utf8
  PeerReview2Comment:
    locale: en_US.utf8
  PeerReview2Date:
    dateTimeFormat: ISO
    locale: en_US.utf8
  PlacementType:
    locale: en_US.utf8
  PolicyCode:
    locale: en_US.utf8
  PolicyLastModifiedDate:
    dateTimeFormat: ISO
  PolicyProductCode:
    locale: en_US.utf8
  PolicyProductDescription:
    locale: en_US.utf8
  PolicyReference:
    locale: en_US.utf8
  PreviousPolicyReference:
    locale: en_US.utf8
  PreviousSourceSystem:
    locale: en_US.utf8
  PreviousSourceSystemDescription:
    locale: en_US.utf8
  ProducingOfficeCode:
    locale: en_US.utf8
  QuotationReference:
    locale: en_US.utf8
  ReferralUnderwriter:
    locale: en_US.utf8
  ReinsurancePolicyIndicator:
    locale: en_US.utf8
  ReinsuranceReference:
    locale: en_US.utf8
  ReinsuredAddressArea:
    locale: en_US.utf8
  ReinsuredAddressCity:
    locale: en_US.utf8
  ReinsuredAddressStreet:
    locale: en_US.utf8
  ReinsuredAnnualTurnover:
    locale: en_US.utf8
  ReinsuredAnnualTurnoverCurrency:
    locale: en_US.utf8
  ReinsuredCode:
    locale: en_US.utf8
  ReinsuredCountry:
    locale: en_US.utf8
  ReinsuredFullName:
    locale: en_US.utf8
  ReinsuredNumberOfFullTimeEmployees:
    locale: en_US.utf8
    sourceName: ReinsuredNumberOfFTEmployees
  ReinsuredPostcode:
    locale: en_US.utf8
  ReinsuredProvince:
    locale: en_US.utf8
  ReinsuredShortName:
    locale: en_US.utf8
  ReinsuredState:
    locale: en_US.utf8
  ReinsuredTerritory:
    locale: en_US.utf8
  RenewalPolicyIndicator:
    locale: en_US.utf8
  RenewalPolicyReference:
    locale: en_US.utf8
  RenewalSequenceNumber:
    locale: en_US.utf8
  RiskCode:
    locale: en_US.utf8
  StatusCode:
    locale: en_US.utf8
  StatusDescription:
    locale: en_US.utf8
  TacitRenewalIndicator:
    locale: en_US.utf8
  TerrorismCode:
    locale: en_US.utf8
  Timezone:
    locale: en_US.utf8
  TradeCodeOrIndustry:
    locale: en_US.utf8
  TrustFund:
    locale: en_US.utf8
  UnderlyingLimit:
    calculatedValue: null
    mapType: calculated
  UnderlyingLimitCurrency:
    locale: en_US.utf8
  UnderwriterCodeID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: UnderwriterCode
        sep: ':'
  UnderwriterName:
    locale: en_US.utf8
  WrittenDate:
    dateTimeFormat: ISO
    locale: en_US.utf8
  YearOfAccount:
    locale: en_US.utf8
  ReUnderwritingExercise:
    NotInSource: True
  BusinessClassification:
    NotInSource: True
  PolicySequenceNumber:
    NotInSource: True
  IPTLiable:
    NotInSource: True
  AssuredAnnualTurnoverRounded:
    NotInSource: True
  AssuredAnnualTurnoverGBP:
    NotInSource: True
  AssuredAnnualTurnoverEUR:
    NotInSource: True
  AssuredAnnualTurnoverUSD:
    NotInSource: True
  AssuredAnnualTurnoverCAD:
    NotInSource: True
  AssuredAnnualTurnoverRoundedGBP:
    NotInSource: True
  AssuredAnnualTurnoverRoundedEUR:
    NotInSource: True
  AssuredAnnualTurnoverRoundedUSD:
    NotInSource: True
  AssuredAnnualTurnoverRoundedCAD:
    NotInSource: True
