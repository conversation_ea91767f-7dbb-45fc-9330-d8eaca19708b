SELECT DISTINCT
  CONCAT(PO_INTERNAL_POL_NO, '/', PO_INTERNAL_RSN)  AS KeyIdPolis
  ,'MDR01' AS AdministrationOfficeCode
  ,CASE WHEN PO_PROD_OFFICE = 'BARCELON' THEN 'BARCELONA' ELSE PO_PROD_OFFICE END AS AssuredAddressArea
  ,CASE WHEN PO_PROD_OFFICE = 'BARCELON' THEN 'BARCELONA' ELSE PO_PROD_OFFICE END AS AssuredAddressCity
  ,'N/A' AS AssuredAddressStreet
  ,'N/A' AS AssuredAnnualTurnover 
  ,'EURO' as AssuredAnnualTurnoverCurrency
  ,'N/A' AS AssuredCode
  ,'ESPA' as AssuredCountry
  ,COALESCE(PO_ACCOUNT_NAME, PO_CORR_NAME) AS AssuredFullName
  ,'N/A' AS AssuredMainActivityCode
  ,'N/A' AS AssuredMainActivityDescription
  ,'N/A' AS AssuredNumberOfFullTimeEmployees
  ,'N/A' AS AssuredPostCode
  ,'N/A' AS AssuredProvince
  ,'N/A' AS AssuredShortName
  ,'N/A' AS AssuredState
  ,'N/A' AS AssuredTerritory
  ,'N/A' AS BrokerAddressArea
  ,'N/A' AS BrokerAddressCity
  ,'N/A' AS BrokerAddressStreet
  ,'N/A' AS BrokerCode
  ,'ESPA' as BrokerCountry
  ,ft.BR_NAME AS BrokerFullName
  ,'N/A' AS BrokerGroupCode
  ,COALESCE(bg.BG_NAME, 'All Other Brokers') AS BrokerGroupName
  ,'N/A' AS BrokerPostcode
  ,'N/A' AS BrokerProvince
  ,'N/A' AS BrokerShortName
  ,'N/A' AS ClaimsBasisCode
  ,'N/A' AS ClaimsBasisDescription
  ,'N/A' AS CoverholderCode
  ,'N/A' AS CoverholderName
  ,'N/A' AS CustomerClassification
  ,'N/A' AS CustomerClassificationCode
  ,'N/A' AS DistributionPartner
  ,'N/A' AS DistributionType
  ,p.PO_EXPIRY_DATE as ExpiryDate
  ,'N/A' AS IBCIndustryCode
  ,p.PO_INCEPTION_DATE as InceptionDate
  ,'MISE' as InsurerEntityCode
  ,'N/A' AS InsurerEntityDescription
  ,'N/A' AS LapsedReason
  ,'N/A' AS Layer
  ,'N/A' AS OrderPercentage
  ,'N/A' AS OutwardsFACIndicator
  ,'N/A' AS PeerReview1
  ,'N/A' AS PeerReview1Code
  ,'N/A' AS PeerReview1Comment
  ,'N/A' AS PeerReview1Date
  ,'N/A' AS PeerReview2
  ,'N/A' AS PeerReview2Code
  ,'N/A' AS PeerReview2Comment
  ,'N/A' AS PeerReview2Date
  ,'OM' as PlacementType
  ,p.PO_INTERNAL_POL_NO as PolicyCode
  ,p.PO_INCEPTION_DATE as PolicyLastModifiedDate
  ,CASE
    WHEN rv.RV_DESC = '(ES) Legal Expenses'  THEN  'AC01'
    WHEN rv.RV_DESC = '(ES) PA - Events'  THEN  'AC01'
    WHEN rv.RV_DESC = '(ES) PA - Expatriados'  THEN  'AC01'
    WHEN rv.RV_DESC = '(ES) PA - Federations'  THEN  'AC01'
    WHEN rv.RV_DESC = '(ES) PA - Individual'  THEN  'AC01'
    WHEN rv.RV_DESC = '(ES) PA - Non-Regulated Collective Agreements'  THEN  'AC01'
    WHEN rv.RV_DESC = '(ES) PA - Other non-regulated Collective Agreements'  THEN  'AC01'
    WHEN rv.RV_DESC = '(ES) PA - Regulated collective agreements (convenios)'  THEN  'AC01'
    WHEN rv.RV_DESC = '(ES) PA - Regulated collective agreements (convenios) - O.D.'  THEN  'AC01'
    WHEN rv.RV_DESC = '(ES) PA - Travel'  THEN  'AC01'
    WHEN rv.RV_DESC = '(ES) PA (AON Net - Eventos)'  THEN  'AC01'
    WHEN rv.RV_DESC = '(ES) Reinsurance Business Travel - ISOS GENERAL'  THEN  'AC01'
    WHEN rv.RV_DESC = '(ES) Reinsurance Business Travel - ISOS ON CAMPUS'  THEN  'AC01'
    WHEN rv.RV_DESC = '(ES) Surety - Travel Agents'  THEN  'CA01'
    WHEN rv.RV_DESC = '(ES) Surety (Advance Payment Bond)'  THEN  'CA01'
    WHEN rv.RV_DESC = '(ES) Surety (Bid Bond)'  THEN  'CA01'
    WHEN rv.RV_DESC = '(ES) Surety (Customs Bond)'  THEN  'CA01'
    WHEN rv.RV_DESC = '(ES) Surety (MEFF Bond)'  THEN  'CA01'
    WHEN rv.RV_DESC = '(ES) Surety (Performance Bond)'  THEN  'CA01'
    WHEN rv.RV_DESC = '(ES) Surety (Performance Private Bond)'  THEN  'CA01'
    WHEN rv.RV_DESC = '(ES) Surety (Renewable Bid Bond)'  THEN  'CA01'
    WHEN rv.RV_DESC = '(ES) Surety (Renewable Network Access for Energy Bond)'  THEN  'CA01'
    WHEN rv.RV_DESC = '(ES) Associations, Clubs & Affinity Groups'  THEN  'RC01'
    WHEN rv.RV_DESC = '(ES) Beauty Centres'  THEN  'RC01'
    WHEN rv.RV_DESC = '(ES) Cleaning Services'  THEN  'RC01'
    WHEN rv.RV_DESC = '(ES) Construction & Engineering'  THEN  'RC01'
    WHEN rv.RV_DESC = '(ES) Councils'  THEN  'RC01'
    WHEN rv.RV_DESC = '(ES) Education Centres'  THEN  'RC01'
    WHEN rv.RV_DESC = '(ES) Environmental Impairment Liability'  THEN  'RC01'
    WHEN rv.RV_DESC = '(ES) Event Organisers'  THEN  'RC01'
    WHEN rv.RV_DESC = '(ES) Excess General Liability'  THEN  'RC01'
    WHEN rv.RV_DESC = '(ES) Horse owners'  THEN  'RC01'
    WHEN rv.RV_DESC = '(ES) Hospitality'  THEN  'RC01'
    WHEN rv.RV_DESC = '(ES) Hotels'  THEN  'RC01'
    WHEN rv.RV_DESC = '(ES) Leisure'  THEN  'RC01'
    WHEN rv.RV_DESC = '(ES) Manufacturing, Processing, Installation & Repair'  THEN  'RC01'
    WHEN rv.RV_DESC = '(ES) Precotizados GL'  THEN  'RC01'
    WHEN rv.RV_DESC = '(ES) Property Owners, Landlords and Tenants'  THEN  'RC01'
    WHEN rv.RV_DESC = '(ES) Public Garages'  THEN  'RC01'
    WHEN rv.RV_DESC = '(ES) RCG Y RCP Combinado'  THEN  'RC01'
    WHEN rv.RV_DESC = '(ES) Retail & Wholesale'  THEN  'RC01'
    WHEN rv.RV_DESC = '(ES) Security'  THEN  'RC01'
    WHEN rv.RV_DESC = '(ES) Service Industries'  THEN  'RC01'
    WHEN rv.RV_DESC = '(ES) Social Services'  THEN  'RC01'
    WHEN rv.RV_DESC = '(ES) Temporary Employment Agencies'  THEN  'RC01'
    WHEN rv.RV_DESC = '(ES) Transportation'  THEN  'RC01'
    WHEN rv.RV_DESC = '(ES) Combinado Centros Sanitarios + D&O'  THEN  'RC03'
    WHEN rv.RV_DESC = '(ES) Commercial D&O'  THEN  'RC03'
    WHEN rv.RV_DESC = '(ES) D&O Molar Lite'  THEN  'RC03'
    WHEN rv.RV_DESC = '(ES) Excess Commercial D&O'  THEN  'RC03'
    WHEN rv.RV_DESC = '(ES) Excess Financial D&O'  THEN  'RC03'
    WHEN rv.RV_DESC = '(ES) Financial D&O'  THEN  'RC03'
    WHEN rv.RV_DESC = '(ES) Nursing Homes + D&O combined product'  THEN  'RC03'
    WHEN rv.RV_DESC = '(ES) Pre-cotizado D&O'  THEN  'RC03'
    WHEN rv.RV_DESC = '(ES) Complementary Medicine' AND p.PO_INCEPTION_DATE <= '2010-01-01T00:00:00.000+00:00' THEN  'RC05'
    WHEN rv.RV_DESC = '(ES) Empresas IT'  THEN  'RC05'
    WHEN rv.RV_DESC = '(ES) Health and Safety - Misc.'  THEN  'RC05'
    WHEN rv.RV_DESC = '(ES) Management Consultants'  THEN  'RC05'
    WHEN rv.RV_DESC = '(ES) Miscellaneous'  THEN  'RC05'
    WHEN rv.RV_DESC = '(ES) PI Agencias de Viajes'  THEN  'RC05'
    WHEN rv.RV_DESC = '(ES) PI Molar Lite'  THEN  'RC05'
    WHEN rv.RV_DESC = '(ES) Portugal PI Miscellaneous'  THEN  'RC05'
    WHEN rv.RV_DESC = '(ES) Pre-cotizado PI'  THEN  'RC05'
    WHEN rv.RV_DESC = '(ES) Pre-cotizado PI (Campaña Especial)'  THEN  'RC05'
    WHEN rv.RV_DESC = '(ES) Pre-cotizado PI (Empresas IT)'  THEN  'RC05'
    WHEN rv.RV_DESC = '(ES) Ambulatory Surgery Centres'  THEN  'RC06'
    WHEN rv.RV_DESC = '(ES) Analysis Labs'  THEN  'RC06'
    WHEN rv.RV_DESC = '(ES) Blood / Tissue Banks'  THEN  'RC06'
    WHEN rv.RV_DESC = '(ES) Clinical Trials'  THEN  'RC06'
    WHEN rv.RV_DESC = '(ES) Complementary Medicine' AND p.PO_INCEPTION_DATE > '2010-01-01T00:00:00.000+00:00'  THEN  'RC06'
    WHEN rv.RV_DESC = '(ES) Fertility'  THEN  'RC06'
    WHEN rv.RV_DESC = '(ES) Imaging / Diagnostic Centres'  THEN  'RC06'
    WHEN rv.RV_DESC = '(ES) Med Mal Collectives'  THEN  'RC06'
    WHEN rv.RV_DESC = '(ES) Medical/Dental'  THEN  'RC06'
    WHEN rv.RV_DESC = '(ES) Nursing Homes'  THEN  'RC06'
    WHEN rv.RV_DESC = '(ES) Nursing Homes precotizado'  THEN  'RC06'
    WHEN rv.RV_DESC = '(ES) Other'  THEN  'RC06'
    WHEN rv.RV_DESC = '(ES) Urgent Care Centres'  THEN  'RC06'
    WHEN rv.RV_DESC = '(ES) Architects'  THEN  'SCS01'
    WHEN rv.RV_DESC = '(ES) Crouco PI+D&O Binder'  THEN  'SCS01'
    WHEN rv.RV_DESC = '(ES) Design & Construct'  THEN  'SCS01'
    WHEN rv.RV_DESC = '(ES) Engineers'  THEN  'SCS01'
    WHEN rv.RV_DESC = '(ES) Estate Agents'  THEN  'SCS01'
    WHEN rv.RV_DESC = '(ES) Excess PI Architects'  THEN  'SCS01'
    WHEN rv.RV_DESC = '(ES) Health and Safety - Construction'  THEN  'SCS01'
    WHEN rv.RV_DESC = '(ES) Marine Engineers / Surveyors'  THEN  'SCS01'
    WHEN rv.RV_DESC = '(ES) Miscellaneous Construction'  THEN  'SCS01'
    WHEN rv.RV_DESC = '(ES) Surveyors'  THEN  'SCS01'
    WHEN rv.RV_DESC = '(ES) Insurance Brokers'  THEN  'SCS02'
    WHEN rv.RV_DESC = '(ES) Miscellaneous Financial'  THEN  'SCS02'
    WHEN rv.RV_DESC = '(ES) Money Transfer'  THEN  'SCS02'
    WHEN rv.RV_DESC = '(ES) Venture Capitalists'  THEN  'SCS02'
    WHEN rv.RV_DESC = '(ES) Accountants'  THEN  'SCS03'
    WHEN rv.RV_DESC = '(ES) Excess PI'  THEN  'SCS03'
    WHEN rv.RV_DESC = '(ES) Miscellaneous Professions'  THEN  'SCS03'
    WHEN rv.RV_DESC = '(ES) Professions PI'  THEN  'SCS03'
    WHEN rv.RV_DESC = '(ES) Solicitors'  THEN  'SCS03'
    WHEN rv.RV_DESC = '(ES) Solicitors Portugal'  THEN  'SCS03'
    WHEN rv.RV_DESC = '(ES) Terrorism Liability (T3L)'  THEN  'SCS03'
    WHEN rv.RV_DESC = 'BUSINESS NOT WRITTEN'  THEN  'SCS03'
    ELSE 'SCS03' 
  END  AS PolicyProductCode
  ,COALESCE(rv.RV_DESC, 'BUSINESS NOT WRITTEN') as PolicyProductDescription
  ,p.PO_INTERNAL_POL_NO as PolicyReference
  ,p.PO_AGREE_NO as PreviousPolicyReference
  ,'SCS' AS PreviousSourceSystem
  ,'SCS' as PreviousSourceSystemDescription
  ,CASE WHEN PO_PROD_OFFICE = 'BARCELON' THEN 'Barcelona' ELSE initcap(PO_PROD_OFFICE) END AS ProducingOfficeCode
  ,'N/A' AS QuotationReference
  ,'N/A' AS ReferralUnderwriter
  ,'N/A' AS ReinsurancePolicyIndicator
  ,'N/A' AS ReinsuranceReference
  ,'N/A' AS ReinsuredAddressArea
  ,'N/A' AS ReinsuredAddressCity
  ,'N/A' AS ReinsuredAddressStreet
  ,'N/A' AS ReinsuredAnnualTurnover
  ,'N/A' AS ReinsuredAnnualTurnoverCurrency
  ,'N/A' AS ReinsuredCode
  ,'N/A' AS ReinsuredCountry
  ,'N/A' AS ReinsuredFullName
  ,'N/A' AS ReinsuredNumberOfFullTimeEmployees
  ,'N/A' AS ReinsuredPostcode
  ,'N/A' AS ReinsuredProvince
  ,'N/A' AS ReinsuredShortName
  ,'N/A' AS ReinsuredState
  ,'N/A' AS ReinsuredTerritory
  ,'N/A' AS RenewalPolicyIndicator
  ,'N/A' AS RenewalPolicyReference
  ,p.PO_INTERNAL_RSN as RenewalSequenceNumber
  ,'N/A' AS RiskCode
  ,'N/A' AS StatusCode
  ,'N/A' AS StatusDescription
  ,p.PO_TACIT_RENEWAL_IND as TacitRenewalIndicator
  ,'N/A' AS TerrorismCode
  ,'Europe/Madrid' as Timezone
  ,'N/A' AS TradeCodeOrIndustry
  ,'N/A' AS TrustFund
  ,'N/A' AS UnderlyingLimit
  ,'EURO' as UnderlyingLimitCurrency
  ,'EURO' as UnderwriterCode
  ,'EURO' as UnderwriterName
  ,p.PO_INCEPTION_DATE as WrittenDate
  ,YEAR(p.PO_INCEPTION_DATE) as YearOfAccount
FROM {mintdatalake}.scs_dbo.policies p
LEFT JOIN 
  (
    SELECT DISTINCT FT_PO_INTERNAL_POL_NO, FT_PO_INTERNAL_RSN,BR_ABBREV, BR_NAME, BR_BG_ABBREV
    FROM (
      SELECT DISTINCT
        FT_PO_INTERNAL_POL_NO, FT_PO_INTERNAL_RSN,
        br.BR_ABBREV,
        br.BR_NAME,
        BR_BG_ABBREV,
        ft.FT_TRANS_DATE,
        ROW_NUMBER() OVER (
          PARTITION BY FT_PO_INTERNAL_POL_NO, FT_PO_INTERNAL_RSN
          ORDER BY FT_TRANS_DATE DESC
        ) AS rn
      FROM {mintdatalake}.scs_dbo.financial_trans ft
      LEFT JOIN {mintdatalake}.scs_dbo.brokers br
        ON ft.FT_BR_ABBREV_PRODUCER = br.BR_ABBREV
    ) WHERE rn = 1
  ) ft  ON p.PO_INTERNAL_POL_NO = ft.FT_PO_INTERNAL_POL_NO 
        AND p.PO_INTERNAL_RSN = ft.FT_PO_INTERNAL_RSN
LEFT JOIN {mintdatalake}.scs_dbo.broker_groups bg
  ON ft.BR_BG_ABBREV = bg.BG_ABBREV
LEFT JOIN {mintdatalake}.scs_dbo.refvalues rv
  ON rv.RV_VALUE = p.PO_RV_COB_CODE
  AND rv.RV_RD_DOMAIN = 'COB'
WHERE PO_PROD_OFFICE IN ('SPAIN', 'MADRID', 'BARCELON', 'BARCELONA', 'BCN01', 'MDR01' )