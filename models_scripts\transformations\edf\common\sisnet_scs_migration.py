import pyspark.sql.functions as F
from pyspark.sql import DataFrame, Window

from models_scripts.transformations.common.traits import transformation


def get_migrated_policy_without_section(
    policy_df: DataFrame, mapping_df: DataFrame
) -> DataFrame:
    """Get Policies to be updated in SCS and deleted in SISNET"""
    policy_mapping_df = (
        mapping_df.filter(F.col("SCS_KeyIdPolis").isNotNull())
        .select(
            "KeyIdPolis",
            "SCS_KeyIdPolis",
            "SCS_KeyDekkingsNummer",
            "SectionProductDescription",
        )
        .distinct()
    )

    migrated_policies_df = policy_mapping_df.join(
        policy_df, on="KeyIdPolis", how="left"
    )

    return migrated_policies_df.select(
        "SCS_KeyIdPolis", "SCS_KeyDekkingsNummer", "PolicyProductCode"
    ).distinct()


def get_migrated_policy(
    policy_df: DataFrame, policy_section_df: DataFrame, mapping_df: DataFrame
) -> DataFrame:
    """Get Policies to be updated in SCS and deleted in SISNET"""
    policy_mapping_df = (
        mapping_df.filter(F.col("SCS_KeyIdPolis").isNotNull())
        .select(
            "KeyIdPolis",
            "SCS_KeyIdPolis",
            "SCS_KeyDekkingsNummer",
            "SectionProductDescription",
        )
        .distinct()
    ).alias("migrated")

    policy_wide_df = policy_df.join(policy_section_df, on="KeyIdPolis", how="left")
    migrated_policies_df = policy_mapping_df.join(
        policy_wide_df, on="KeyIdPolis", how="left"
    )

    migrated_policy_df = migrated_policies_df.withColumn(
        "AssuredMainActivityCode",
        F.when(
            F.col("PolicyProductCode") == "RC02", F.col("AssuredMainActivityCode")
        ).otherwise(F.lit(None)),
    )
    return migrated_policy_df.select(
        "SCS_KeyIdPolis",
        "SCS_KeyDekkingsNummer",
        "migrated.SectionProductDescription",
        "PolicyProductCode",
        "AssuredMainActivityCode",
    ).distinct()


def add_suffix(df: DataFrame, suffix: str) -> DataFrame:
    """Add suffix to the columns of the dataframe"""
    add_suffix_query = [f"{col} as {col}{suffix}" for col in df.columns]
    return df.selectExpr(*add_suffix_query)


class SCSPolicyUpdater:
    """Class to update the SCS policies, It will be used to update:
    - PolicyProductCode for PolicyPolicy
    - SectionProductCode for PolicySection
    - SectionReference for PolicySection
    We are updating the policies in SCS that are migrated from SISNET using the SISNET information
    """

    MIGRATED_SUFFIX = "_migrated"

    @classmethod
    @transformation
    def get_updated(
        cls,
        migrated_policy_df: DataFrame,
        scs_policy_df: DataFrame,
        scs_section_df: DataFrame,
    ) -> DataFrame:
        """Deduplicate migrated policies"""
        suffixed_migrated_policy_df = add_suffix(
            migrated_policy_df, cls.MIGRATED_SUFFIX
        )

        (
            duplicated_scs_to_update_df,
            non_duplicated_scs_to_update_df,
        ) = cls._split_duplicated_migrated(suffixed_migrated_policy_df)
        wide_scs_policy_df = cls._get_policy_with_section(scs_policy_df, scs_section_df)

        duplicated_with_scs_df = cls._join_updates_with_scs(
            duplicated_scs_to_update_df, wide_scs_policy_df
        )
        updated_duplicated_df = cls._update_duplicated_product_codes(
            duplicated_with_scs_df
        )
        deduplicated_df = cls._deduplicate_updates(updated_duplicated_df)

        scs_to_update = deduplicated_df.unionByName(
            non_duplicated_scs_to_update_df, allowMissingColumns=True
        )

        all_scs_match_df = cls._merge_scs_with_updated(
            wide_scs_policy_df, scs_to_update
        )

        output_df = cls._update_product_section_codes(all_scs_match_df)

        return output_df

    @classmethod
    def _split_duplicated_migrated(cls, df: DataFrame) -> tuple[DataFrame, DataFrame]:
        """Split the duplicated SCS policies to be updated"""

        dedup_window = Window.partitionBy(
            f"SCS_KeyIdPolis{cls.MIGRATED_SUFFIX}",
            f"SCS_KeyDekkingsNummer{cls.MIGRATED_SUFFIX}",
        )
        to_update_with_duplicated_count_df = df.withColumn(
            "count", F.count("*").over(dedup_window)
        )
        duplicated_scs_to_update_df = to_update_with_duplicated_count_df.filter(
            F.col("count") > 1
        )
        non_duplicated_scs_to_update_df = to_update_with_duplicated_count_df.filter(
            F.col("count") == 1
        )

        return duplicated_scs_to_update_df, non_duplicated_scs_to_update_df

    @staticmethod
    @transformation
    def _get_policy_with_section(
        policy_df: DataFrame, section_df: DataFrame
    ) -> DataFrame:
        """Get the policy with the section"""
        return policy_df.join(section_df, on="KeyIdPolis", how="left")

    @classmethod
    @transformation
    def _join_updates_with_scs(
        cls, to_update_df: DataFrame, wide_scs_policy_df: DataFrame
    ) -> DataFrame:
        """Join the policies to be updated with the SCS policies"""
        duplicated_with_scs_df = to_update_df.join(
            wide_scs_policy_df,
            on=[
                F.col("KeyDekkingsNummer")
                == F.col(f"SCS_KeyDekkingsNummer{cls.MIGRATED_SUFFIX}"),
                F.col("KeyIdPolis") == F.col(f"SCS_KeyIdPolis{cls.MIGRATED_SUFFIX}"),
            ],
            how="left",
        )
        return duplicated_with_scs_df

    @classmethod
    @transformation
    def _update_duplicated_product_codes(cls, df: DataFrame) -> DataFrame:
        """Update the codes of the duplicated policies"""

        update_codes_cond = ~F.col("PolicyProductCode").like("SCS%")
        new_policy_product_code = F.when(
            update_codes_cond, F.col("PolicyProductCode")
        ).otherwise(F.col(f"PolicyProductCode{cls.MIGRATED_SUFFIX}"))
        new_assured_activity_code = F.when(
            update_codes_cond, F.col("AssuredMainActivityCode")
        ).otherwise(F.col(f"AssuredMainActivityCode{cls.MIGRATED_SUFFIX}"))

        migrated_cols_dict = {
            col_name: F.col(col_name)
            for col_name in df.columns
            if col_name.endswith(cls.MIGRATED_SUFFIX)
        }
        migrated_cols_dict[
            f"PolicyProductCode{cls.MIGRATED_SUFFIX}"
        ] = new_policy_product_code
        migrated_cols_dict[
            f"AssuredMainActivityCode{cls.MIGRATED_SUFFIX}"
        ] = new_assured_activity_code

        updated_df = df.select(
            *[col.alias(col_name) for col_name, col in migrated_cols_dict.items()]
        ).distinct()

        return updated_df

    @classmethod
    @transformation
    def _deduplicate_updates(cls, df: DataFrame) -> DataFrame:
        """Deduplicate the updates"""
        dedup_key_reserving = Window.partitionBy(
            f"SCS_KeyIdPolis{cls.MIGRATED_SUFFIX}",
            f"SCS_KeyDekkingsNummer{cls.MIGRATED_SUFFIX}",
        ).orderBy(
            F.col(f"PolicyProductCode{cls.MIGRATED_SUFFIX}").asc_nulls_last(),
            F.col(f"AssuredMainActivityCode{cls.MIGRATED_SUFFIX}").asc_nulls_last(),
        )
        deduplicated_df = (
            df.withColumn("row_number", F.row_number().over(dedup_key_reserving))
            .filter(F.col("row_number") == 1)
            .drop("row_number")
        )

        return deduplicated_df

    @classmethod
    @transformation
    def _merge_scs_with_updated(
        cls, wide_scs_policy_df: DataFrame, scs_to_update: DataFrame
    ) -> DataFrame:
        """Merge the SCS policies with the updated policies"""
        merged_df = wide_scs_policy_df.join(
            scs_to_update,
            [
                F.col("KeyDekkingsNummer")
                == F.col(f"SCS_KeyDekkingsNummer{cls.MIGRATED_SUFFIX}"),
                F.col("KeyIdPolis") == F.col(f"SCS_KeyIdPolis{cls.MIGRATED_SUFFIX}"),
            ],
            how="left",
        )

        return merged_df

    @classmethod
    @transformation
    def _update_product_section_codes(cls, df: DataFrame) -> DataFrame:
        """Get the updated policy from the migrated policy."""
        columns_mapping = {
            col_name: F.col(col_name)
            for col_name in df.columns
            if not col_name.endswith(cls.MIGRATED_SUFFIX)
        }
        cond_product_code_exists = F.col(
            f"PolicyProductCode{cls.MIGRATED_SUFFIX}"
        ).isNotNull()
        new_section_product_code = F.when(
            cond_product_code_exists, F.col(f"PolicyProductCode{cls.MIGRATED_SUFFIX}")
        ).otherwise(F.col("SectionProductCode"))
        new_section_reference = F.when(
            cond_product_code_exists,
            F.col(f"AssuredMainActivityCode{cls.MIGRATED_SUFFIX}"),
        ).otherwise(F.col("SectionReference"))

        columns_mapping["SectionProductCode"] = new_section_product_code
        columns_mapping["SectionReference"] = new_section_reference

        updated_df = df.select(
            *[col.alias(col_name) for col_name, col in columns_mapping.items()]
        )

        return updated_df
