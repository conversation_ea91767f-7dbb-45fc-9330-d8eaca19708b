SELECT
  PB.IDPOLIS AS 'KeyIdPolis',
  <PERSON><PERSON><PERSON>(TO_CHAR(PBV.DEKKINGSNUMMER)) + <PERSON>TRIM(TO_CHAR(PB.PRODUCTCODE)) + <PERSON><PERSON><PERSON>(TO_CHAR(PBV.DEKKINGSCODE)) AS 'KeyDekkingsnummer',
  PB.FACTUURNUMMER AS 'KeyFactuurnummer',
  'DEDUCTION' AS 'TCAdditionsDeductionsIndicator',
  SUM(PBV.BEDRAG) - SUM(PBV.ASSURANTIEBELASTING) AS 'TransactionComponentAmount',
  'NPREM' AS 'TransactionComponentTypeCode',
  'NETT PREMIUM' AS 'TComponentTypeCodeDescription',
  '' AS 'TransactionComponentPercentage',
  'NETHERLANDS' AS 'TransactionComponentTerritory'
FROM
  PUB.PREMIEBOEKINGVERDELING PBV
  INNER JOIN PUB.PREMIEBOEKING PB
  ON PB.BEDRIJFFACTUURNUMMER = PBV.BEDRIJFFACTUURNUMMER
  LEFT OUTER JOIN PUB.POLISVERSIE PYOA
  ON PYOA.IDPOLIS = PB.IDPOLIS
  LEFT OUTER JOIN PUB.HISTPOLISVERSIE HYOA
  ON HYOA.IDPOLIS = PB.IDPOLIS
GROUP BY
  PB.IDPOLIS,
  LTRIM(TO_CHAR(PBV.DEKKINGSNUMMER)) + LTRIM(TO_CHAR(PB.PRODUCTCODE)) + LTRIM(TO_CHAR(PBV.DEKKINGSCODE)),
  PB.FACTUURNUMMER 
  
UNION ALL
  
  SELECT
        PB.IDPOLIS AS 'KeyIdPolis',
    LTRIM(TO_CHAR(PBV.DEKKINGSNUMMER)) + LTRIM(TO_CHAR(PB.PRODUCTCODE)) + LTRIM(TO_CHAR(PBV.DEKKINGSCODE)) AS 'KeyDekkingsnummer',
    PB.FACTUURNUMMER AS 'KeyFactuurnummer',
    'DEDUCTION' AS 'TCAdditionsDeductionsIndicator',
    SUM(PBV.DOORLPROVISIETP) AS 'TransactionComponentAmount',
    'BKR' AS 'TransactionComponentTypeCode',
    'BROKERAGE' AS 'TComponentTypeCodeDescription',
    '' AS 'TransactionComponentPercentage',
    'NETHERLANDS' AS 'TransactionComponentTerritory'
  FROM
    PUB.PREMIEBOEKINGVERDELING PBV
    INNER JOIN PUB.PREMIEBOEKING PB
    ON PB.BEDRIJFFACTUURNUMMER = PBV.BEDRIJFFACTUURNUMMER
    LEFT OUTER JOIN PUB.POLISVERSIE PYOA
    ON PYOA.IDPOLIS = PB.IDPOLIS
    LEFT OUTER JOIN PUB.HISTPOLISVERSIE HYOA
    ON HYOA.IDPOLIS = PB.IDPOLIS
  GROUP BY
    PB.IDPOLIS,
    LTRIM(TO_CHAR(PBV.DEKKINGSNUMMER)) + LTRIM(TO_CHAR(PB.PRODUCTCODE)) + LTRIM(TO_CHAR(PBV.DEKKINGSCODE)),
    PB.FACTUURNUMMER 
    
    UNION ALL
    
    SELECT
      PB.IDPOLIS AS 'KeyIdPolis',
      LTRIM(TO_CHAR(PBV.DEKKINGSNUMMER)) + LTRIM(TO_CHAR(PB.PRODUCTCODE)) + LTRIM(TO_CHAR(PBV.DEKKINGSCODE)) AS 'KeyDekkingsnummer',
      PB.FACTUURNUMMER AS 'KeyFactuurnummer',
      'ADDITION' AS 'TCAdditionsDeductionsIndicator',
      SUM(PBV.ASSURANTIEBELASTING) AS 'TransactionComponentAmount',
      'IPT' AS 'TransactionComponentTypeCode',
      'INSURANCE PREMIUM TAX' AS 'TComponentTypeCodeDescription',
      '' AS 'TransactionComponentPercentage',
      'NETHERLANDS' AS 'TransactionComponentTerritory'
    FROM
      PUB.PREMIEBOEKINGVERDELING PBV
      INNER JOIN PUB.PREMIEBOEKING PB
      ON PB.BEDRIJFFACTUURNUMMER = PBV.BEDRIJFFACTUURNUMMER
      LEFT OUTER JOIN PUB.POLISVERSIE PYOA
      ON PYOA.IDPOLIS = PB.IDPOLIS
      LEFT OUTER JOIN PUB.HISTPOLISVERSIE HYOA
      ON HYOA.IDPOLIS = PB.IDPOLIS
    GROUP BY
      PB.IDPOLIS,
      LTRIM(TO_CHAR(PBV.DEKKINGSNUMMER)) + LTRIM(TO_CHAR(PB.PRODUCTCODE)) + LTRIM(TO_CHAR(PBV.DEKKINGSCODE)),
      PB.FACTUURNUMMER 
      
      UNION ALL
      
      SELECT
       PB.IDPOLIS AS 'KeyIdPolis',
        LTRIM(TO_CHAR(PBV.DEKKINGSNUMMER)) + LTRIM(TO_CHAR(PB.PRODUCTCODE)) + LTRIM(TO_CHAR(PBV.DEKKINGSCODE)) AS 'KeyDekkingsnummer',
        PB.FACTUURNUMMER AS 'KeyFactuurnummer',
        'ADDITION' AS 'TCAdditionsDeductionsIndicator',
        SUM(PBV.POLISKOSTEN) AS 'TransactionComponentAmount',
        'RCOST' AS 'TransactionComponentTypeCode',
        'RENEWAL COSTS' AS 'TComponentTypeCodeDescription',
        '' AS 'TransactionComponentPercentage',
        'NETHERLANDS' AS 'TransactionComponentTerritory'
      FROM
        PUB.PREMIEBOEKINGVERDELING PBV
        INNER JOIN PUB.PREMIEBOEKING PB
        ON PB.BEDRIJFFACTUURNUMMER = PBV.BEDRIJFFACTUURNUMMER
        LEFT OUTER JOIN PUB.POLISVERSIE PYOA
        ON PYOA.IDPOLIS = PB.IDPOLIS
        LEFT OUTER JOIN PUB.HISTPOLISVERSIE HYOA
        ON HYOA.IDPOLIS = PB.IDPOLIS
      WHERE
        PBV.POLISKOSTEN <> 0
      GROUP BY
        PB.IDPOLIS,
        LTRIM(TO_CHAR(PBV.DEKKINGSNUMMER)) + LTRIM(TO_CHAR(PB.PRODUCTCODE)) + LTRIM(TO_CHAR(PBV.DEKKINGSCODE)),
        PB.FACTUURNUMMER 
        
        UNION ALL
        
        SELECT
          PB.IDPOLIS AS 'KeyIdPolis',
          LTRIM(TO_CHAR(PBV.DEKKINGSNUMMER)) + LTRIM(TO_CHAR(PB.PRODUCTCODE)) + LTRIM(TO_CHAR(PBV.DEKKINGSCODE)) AS 'KeyDekkingsnummer',
          PB.IDPREMIEBOEKINGTP AS 'KeyFactuurnummer',
          'DEDUCTION' AS 'TCAdditionsDeductionsIndicator',
          SUM(PBV.BEDRAG) - SUM(PBV.ASSURANTIEBELASTING) AS 'TransactionComponentAmount',
          'NPREM' AS 'TransactionComponentTypeCode',
          'NETT PREMIUM' AS 'TComponentTypeCodeDescription',
          '' AS 'TransactionComponentPercentage',
          'NETHERLANDS' AS 'TransactionComponentTerritory'
        FROM
          PUB.PREMIEBOEKINGTPVERDELING PBV
          INNER JOIN PUB.PREMIEBOEKINGTP PB
          ON PB.IDPREMIEBOEKINGTP = PBV.IDPREMIEBOEKINGTP
          LEFT OUTER JOIN PUB.POLISVERSIE PYOA
          ON PYOA.IDPOLIS = PB.IDPOLIS
          LEFT OUTER JOIN PUB.HISTPOLISVERSIE HYOA
          ON HYOA.IDPOLIS = PB.IDPOLIS
        GROUP BY
          PB.IDPOLIS,
          LTRIM(TO_CHAR(PBV.DEKKINGSNUMMER)) + LTRIM(TO_CHAR(PB.PRODUCTCODE)) + LTRIM(TO_CHAR(PBV.DEKKINGSCODE)),
          PB.IDPREMIEBOEKINGTP 
          
          UNION ALL
          
          SELECT
            PB.IDPOLIS AS 'KeyIdPolis',
            LTRIM(TO_CHAR(PBV.DEKKINGSNUMMER)) + LTRIM(TO_CHAR(PB.PRODUCTCODE)) + LTRIM(TO_CHAR(PBV.DEKKINGSCODE)) AS 'KeyDekkingsnummer',
            PB.IDPREMIEBOEKINGTP AS 'KeyFactuurnummer',
            'DEDUCTION' AS 'TCAdditionsDeductionsIndicator',
            SUM(PBV.DOORLPROVISIETP) AS 'TransactionComponentAmount',
            'BKR' AS 'TransactionComponentTypeCode',
            'BROKERAGE' AS 'TComponentTypeCodeDescription',
            '' AS 'TransactionComponentPercentage',
            'NETHERLANDS' AS 'TransactionComponentTerritory'
          FROM
            PUB.PREMIEBOEKINGTPVERDELING PBV
            INNER JOIN PUB.PREMIEBOEKINGTP PB
            ON PB.IDPREMIEBOEKINGTP = PBV.IDPREMIEBOEKINGTP
            LEFT OUTER JOIN PUB.POLISVERSIE PYOA
            ON PYOA.IDPOLIS = PB.IDPOLIS
            LEFT OUTER JOIN PUB.HISTPOLISVERSIE HYOA
            ON HYOA.IDPOLIS = PB.IDPOLIS
          GROUP BY
            PB.IDPOLIS,
            LTRIM(TO_CHAR(PBV.DEKKINGSNUMMER)) + LTRIM(TO_CHAR(PB.PRODUCTCODE)) + LTRIM(TO_CHAR(PBV.DEKKINGSCODE)),
            PB.IDPREMIEBOEKINGTP 
            
            UNION ALL
            
            SELECT
              PB.IDPOLIS AS 'KeyIdPolis',
              LTRIM(TO_CHAR(PBV.DEKKINGSNUMMER)) + LTRIM(TO_CHAR(PB.PRODUCTCODE)) + LTRIM(TO_CHAR(PBV.DEKKINGSCODE)) AS 'KeyDekkingsnummer',
              PB.IDPREMIEBOEKINGTP AS 'KeyFactuurnummer',
              'ADDITION' AS 'TCAdditionsDeductionsIndicator',
              SUM(PBV.ASSURANTIEBELASTING) AS 'TransactionComponentAmount',
              'IPT' AS 'TransactionComponentTypeCode',
              'INSURANCE PREMIUM TAX' AS 'TComponentTypeCodeDescription',
              '' AS 'TransactionComponentPercentage',
              'NETHERLANDS' AS 'TransactionComponentTerritory'
            FROM
              PUB.PREMIEBOEKINGTPVERDELING PBV
              INNER JOIN PUB.PREMIEBOEKINGTP PB
              ON PB.IDPREMIEBOEKINGTP = PBV.IDPREMIEBOEKINGTP
              LEFT OUTER JOIN PUB.POLISVERSIE PYOA
              ON PYOA.IDPOLIS = PB.IDPOLIS
              LEFT OUTER JOIN PUB.HISTPOLISVERSIE HYOA
              ON HYOA.IDPOLIS = PB.IDPOLIS
            GROUP BY
              PB.IDPOLIS,
              LTRIM(TO_CHAR(PBV.DEKKINGSNUMMER)) + LTRIM(TO_CHAR(PB.PRODUCTCODE)) + LTRIM(TO_CHAR(PBV.DEKKINGSCODE)),
              PB.IDPREMIEBOEKINGTP 
              
              UNION ALL
              
              SELECT
                PB.IDPOLIS AS 'KeyIdPolis',
                LTRIM(TO_CHAR(PBV.DEKKINGSNUMMER)) + LTRIM(TO_CHAR(PB.PRODUCTCODE)) + LTRIM(TO_CHAR(PBV.DEKKINGSCODE)) AS 'KeyDekkingsnummer',
                PB.IDPREMIEBOEKINGTP AS 'KeyFactuurnummer',
                'ADDITION' AS 'TCAdditionsDeductionsIndicator',
                SUM(PBV.POLISKOSTEN) AS 'TransactionComponentAmount',
                'RCOST' AS 'TransactionComponentTypeCode',
                'RENEWAL COSTS' AS 'TComponentTypeCodeDescription',
                '' AS 'TransactionComponentPercentage',
                'NETHERLANDS' AS 'TransactionComponentTerritory'
              FROM
                PUB.PREMIEBOEKINGTPVERDELING PBV
                INNER JOIN PUB.PREMIEBOEKINGTP PB
                ON PB.IDPREMIEBOEKINGTP = PBV.IDPREMIEBOEKINGTP
                LEFT OUTER JOIN PUB.POLISVERSIE PYOA
                ON PYOA.IDPOLIS = PB.IDPOLIS
                LEFT OUTER JOIN PUB.HISTPOLISVERSIE HYOA
                ON HYOA.IDPOLIS = PB.IDPOLIS
              WHERE
                PBV.POLISKOSTEN <> 0
              GROUP BY
                PB.IDPOLIS,
                LTRIM(TO_CHAR(PBV.DEKKINGSNUMMER)) + LTRIM(TO_CHAR(PB.PRODUCTCODE)) + LTRIM(TO_CHAR(PBV.DEKKINGSCODE)),
                PB.IDPREMIEBOEKINGTP