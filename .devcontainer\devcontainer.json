{"name": "Python Dev Container", "dockerFile": "Dockerfile", "context": "..", "settings": {"terminal.integrated.shell.linux": "/bin/zsh"}, "features": {"ghcr.io/devcontainers/features/azure-cli:1": [], "ghcr.io/eitsupi/devcontainer-features/jq-likes:2": []}, "extensions": ["ms-python.python", "ms-azuretools.vscode-azurecli"], "forwardPorts": [], "postCreateCommand": "git submodule update --init --recursive && poetry install --with dev --with tests && poetry run pre-commit install", "mounts": ["source=models_repo,target=/root/,type=volume"], "containerEnv": {"PYTHONPATH": "/workspaces"}, "runArgs": ["--init"], "shutdownAction": "stopContainer"}