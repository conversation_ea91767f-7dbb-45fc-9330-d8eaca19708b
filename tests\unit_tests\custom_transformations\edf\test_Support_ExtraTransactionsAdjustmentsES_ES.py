from datetime import date

import pytest
from pyspark.testing.utils import assertDataFrameEqual

from models_scripts.transformations.edf.Support_ExtraTransactionsAdjustmentsES_ES import (
    add_calculated_columns,
)


class TestAddCalculatedColumns:
    @pytest.fixture(scope="class")
    def extra_claims_df(self, spark):
        data = [
            (
                "KeyInternSchadenummer1",
                "KeyDekkingsNummer1",
                date(2022, 12, 1),
                "NewTransactionTypeCode1",
                "NewTransactionTypeDescription1",
            ),
            (
                "KeyInternSchadenummer1",
                "KeyDekkingsNummer1",
                date(2022, 12, 2),
                "TransactionTypeCode1",
                "TransactionTypeDescription1",
            ),
            (
                "KeyInternSchadenummer2",
                "KeyDekkingsNummer4",
                date(2022, 12, 3),
                "TransactionTypeCode1",
                "TransactionTypeDescription1",
            ),
            (
                "KeyInternSchadenummer3",
                "KeyDekkingsNummer5",
                date(2022, 12, 4),
                "NewTransactionTypeCode2",
                "NewTransactionTypeDescription2",
            ),
            (
                "KeyInternSchadenummer3",
                "KeyDekkingsNummer5",
                date(2024, 12, 5),
                "NewTransactionTypeCode5",
                "NewTransactionTypeDescription5",
            ),
        ]

        columns = [
            "KeyInternSchadenummer",
            "KeyDekkingsNummer",
            "TransactionDate",
            "TransactionTypeCode",
            "TransactionTypeDescription",
        ]

        # Create DataFrame
        df = spark.createDataFrame(data, columns)
        return df

    def test_calc_new_records(self, spark, extra_claims_df):
        output_df = add_calculated_columns(extra_claims_df)
        # noinspection DuplicatedCode
        expected_df = spark.createDataFrame(
            [
                (
                    "KeyInternSchadenummer1",
                    "KeyDekkingsNummer1",
                    date(2022, 12, 1),
                    "NewTransactionTypeCode1",
                    "NewTransactionTypeDescription1",
                    "EXTRA_RECORDS_SOURCE01:1",
                    "EXTRA_RECORDS_SOURCE01:1",
                ),
                (
                    "KeyInternSchadenummer1",
                    "KeyDekkingsNummer1",
                    date(2022, 12, 2),
                    "TransactionTypeCode1",
                    "TransactionTypeDescription1",
                    "EXTRA_RECORDS_SOURCE01:2",
                    "EXTRA_RECORDS_SOURCE01:2",
                ),
                (
                    "KeyInternSchadenummer2",
                    "KeyDekkingsNummer4",
                    date(2022, 12, 3),
                    "TransactionTypeCode1",
                    "TransactionTypeDescription1",
                    "EXTRA_RECORDS_SOURCE01:3",
                    "EXTRA_RECORDS_SOURCE01:3",
                ),
                (
                    "KeyInternSchadenummer3",
                    "KeyDekkingsNummer5",
                    date(2022, 12, 4),
                    "NewTransactionTypeCode2",
                    "NewTransactionTypeDescription2",
                    "EXTRA_RECORDS_SOURCE01:4",
                    "EXTRA_RECORDS_SOURCE01:4",
                ),
                (
                    "KeyInternSchadenummer3",
                    "KeyDekkingsNummer5",
                    date(2024, 12, 5),
                    "NewTransactionTypeCode5",
                    "NewTransactionTypeDescription5",
                    "EXTRA_RECORDS_SOURCE01:5",
                    "EXTRA_RECORDS_SOURCE01:5",
                ),
            ],
            [
                "KeyInternSchadenummer",
                "KeyDekkingsNummer",
                "TransactionDate",
                "TransactionTypeCode",
                "TransactionTypeDescription",
                "TransactionReference",
                "KeySchadeBoekingsNummer",
            ],
        )

        assertDataFrameEqual(output_df, expected_df)
