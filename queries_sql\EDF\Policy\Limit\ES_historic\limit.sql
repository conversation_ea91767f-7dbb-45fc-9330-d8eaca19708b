SELECT DISTINCT
  CONCAT(p.PO_INTERNAL_POL_NO, '/', p.PO_INTERNAL_RSN)  AS KeyIdPolis,
  KeyDekkingsNummer,
  PO_GROSS_LIMIT AS Limit,
  'AOC' AS LimitBasisCode,
  'ANY ONE CLAIM' AS LimitBasisDescription,
  'EURO' AS LimitCurrencyCode,
  'Y' AS TopLimit
FROM {mintdatalake}.scs_dbo.policies p
LEFT JOIN {mintdatalake}.scs_dbo.r_cbe_cob_extensions rc
  ON p.PO_RV_COB_CODE = rc.CBE_RV_COB_CODE
LEFT JOIN {mintdatalake}.scs_dbo.refvalues rv
  ON rv.RV_VALUE = rc.CBE_REVISED_CLASS
  AND rv.RV_RD_DOMAIN = 'RCL'
LEFT JOIN (
            SELECT DISTINCT PO_INTERNAL_POL_NO, PO_INTERNAL_RSN, KeyDekkingsNummer FROM (
            SELECT DISTINCT
                PO_INTERNAL_POL_NO,
                PO_INTERNAL_RSN,
                CASE 
                    WHEN RV_DESC = '1. Construction Professional Risks' THEN 1
                    WHEN RV_DESC = '2. Financial Services Professional Risks' THEN 2
                    WHEN RV_DESC = '3. Miscellaneous Professional Risks' THEN 3
                    WHEN RV_DESC = '4. Professions Professional Risks' THEN 4
                    WHEN RV_DESC= '6. D and O' THEN 5
                    WHEN RV_DESC = '11. Liability' THEN 6
                    WHEN RV_DESC = 'Surety' THEN 7
                    WHEN RV_DESC = 'Personal Accident' THEN 8
                    WHEN RV_DESC = 'Private Clinics' THEN 9
                  ELSE NULL
                END AS KeyDekkingsNummer
              FROM
              (
                SELECT DISTINCT
                    P.PO_INTERNAL_POL_NO as PO_INTERNAL_POL_NO,
                    P.PO_INTERNAL_RSN as PO_INTERNAL_RSN,
                    RV_DESC
                FROM {mintdatalake}.scs_dbo.policies p
                LEFT JOIN {mintdatalake}.scs_dbo.r_cbe_cob_extensions rc
                  ON p.PO_RV_COB_CODE = rc.CBE_RV_COB_CODE
                LEFT JOIN {mintdatalake}.scs_dbo.refvalues rv
                  ON rv.RV_VALUE = rc.CBE_REVISED_CLASS
                  AND rv.RV_RD_DOMAIN = 'RCL'
                WHERE p.PO_PROD_OFFICE IN ('SPAIN', 'MADRID', 'BARCELON', 'BARCELONA', 'BCN01', 'MDR01' )
              )
              UNION 
                SELECT DISTINCT
                    ft.FT_PO_INTERNAL_POL_NO as PO_INTERNAL_POL_NO,
                    ft.FT_PO_INTERNAL_RSN as PO_INTERNAL_RSN,
                    CASE 
                        WHEN RV_DESC = '1. Construction Professional Risks' THEN 1
                        WHEN RV_DESC = '2. Financial Services Professional Risks' THEN 2
                        WHEN RV_DESC = '3. Miscellaneous Professional Risks' THEN 3
                        WHEN RV_DESC = '4. Professions Professional Risks' THEN 4
                        WHEN RV_DESC= '6. D and O' THEN 5
                        WHEN RV_DESC = '11. Liability' THEN 6
                        WHEN RV_DESC = 'Surety' THEN 7
                        WHEN RV_DESC = 'Personal Accident' THEN 8
                        WHEN RV_DESC = 'Private Clinics' THEN 9
                      ELSE NULL
                    END AS KeyDekkingsNummer
                FROM {mintdatalake}.scs_dbo.financial_trans ft
                LEFT JOIN {mintdatalake}.scs_dbo.financial_trans_sections fs
                    ON ft.FT_KEY = fs.FS_FT_KEY
                LEFT JOIN {mintdatalake}.scs_dbo.r_cbe_cob_extensions rc
                    ON fs.FS_RV_COB_CODE = rc.CBE_RV_COB_CODE
                LEFT JOIN {mintdatalake}.scs_dbo.refvalues rv
                    ON rv.RV_VALUE = rc.CBE_REVISED_CLASS
                    AND rv.RV_RD_DOMAIN = 'RCL'
                WHERE rv.RV_DESC IS NOT NULL
                AND ft.FT_PO_INTERNAL_POL_NO IN 
                  (
                    SELECT DISTINCT PO_INTERNAL_POL_NO 
                    FROM {mintdatalake}.scs_dbo.policies 
                    WHERE PO_PROD_OFFICE IN ('SPAIN', 'MADRID', 'BARCELON', 'BARCELONA', 'BCN01', 'MDR01')
                  )
            ) WHERE KeyDekkingsNummer is NOT NULL
) rv ON rv.PO_INTERNAL_POL_NO = p.PO_INTERNAL_POL_NO AND rv.PO_INTERNAL_RSN = p.PO_INTERNAL_RSN
WHERE PO_PROD_OFFICE IN ('SPAIN', 'MADRID', 'BARCELON', 'BARCELONA', 'BCN01', 'MDR01' )