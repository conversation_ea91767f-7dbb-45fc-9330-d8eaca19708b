import pyspark.sql.functions as F
from pyspark.sql import DataFrame, Window

from models_scripts.transformations.common.traits import business_logic


def add_gaap_end_date(df: DataFrame) -> DataFrame:
    """Add GAAPEndDate column converting the EndDate column (dd-MMM-yy format) to date
    type.
    """
    df = df.withColumn("GAAPEndDate", F.to_date(F.col("EndDate"), "dd-MMM-yy"))

    # Get the last GAAPEndDate for each GAAPMonthYear:
    window = Window.partitionBy("GAAPMonthYear").orderBy(F.desc("GAAPEndDate"))
    df = df.withColumn("_row_number", F.row_number().over(window))
    df = df.filter(df._row_number == 1).drop("_row_number")

    return df


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["DatesGAAP_main"]
    output_df = add_gaap_end_date(main_df)
    return output_df
