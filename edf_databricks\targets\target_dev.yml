targets:
  #  The 'dev' target, used for development purposes.
  # Whenever a developer deploys using 'dev', they get their own copy.
  dev:
    # We use 'mode: development' to make sure everything deployed to this target gets a prefix
    # like '[dev my_user_name]'. Setting this mode also disables any schedules and
    # automatic triggers for jobs and enables the 'development' mode for Delta Live Tables pipelines.
    mode: development
    workspace:
      # has to be fixed for databrics connect extension to work
      host: https://adb-8799184396184460.0.azuredatabricks.net
    run_as:
      service_principal_name: ${var.service_principal_id}
    default: true
