select distinct
    hp.Idpolis
    , hp.InternPolisnummer
    , hp.Polisnummer
    , year(ifnull(hp.histhoofdpremievervaldatum, hp.ingangsdatum))  AS 'UWYr'
    , ifnull(hp.histhoofdpremievervaldatum, hp.ingangsdatum)        AS 'UWDt'
    , hp.Expiratiedatum
    , case when hp.expiratiedatum is not null and hp.soortpolis <> 0 then hp.expiratiedatum else hp.contractvervaldatum end as 'Expiry'
    , 'HPOLIS'                                                      AS 'Source'
    , hp.Creatiedatum
    --, hp.Mutatiedatum
    , case when hp.Mutatiedatum >= ifnull(hp.histhoofdpremievervaldatum, hp.ingangsdatum) then hp.Mutatiedatum else ifnull(hp.histhoofdpremievervaldatum, hp.ingangsdatum) end as 'Mutatiedatum'
    , hp.Mutatiereden
    --, hp.Lopendepolis
    , hp.Termijn
    , hp.Soortpolis
    , hp.Productcode
    , hd.Dekkingscode
    , hd.Dekkingsnummer
    , ltrim(to_char(hd.Dekkingsnummer)) + ltrim(to_char(hp.productcode)) + ltrim(to_char(hd.dekkingscode))  AS 'SectionId'
    , hd.BrutoPremie                                                AS 'GPRM'
    , hd.DoorlProvisieTp                                            AS 'BKR'
    , hd.BrutoPremie - hd.DoorlProvisieTp                           AS 'NPRM'
    , hd.Assurantiebelasting                                        AS 'IPT'
from pub.histdekking hd
inner join pub.histpolisversie hp on hp.idpolis = hd.idpolis
where hp.InternSchadenummer = 0
