Country: custom
existCustomTransformation: 'True'

dataSource:
- name: main
  type: SourceSisnetES
  parameters:
    sqlFileName: 'Policy Section Manual Business.sql'
    querySourceType: SQL_FILE
    selectColumnsFromSchema: False
    enforceSourceSchemaOnStandard: 'False'

- name: policy_policy
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Policy
    Table: Policy
    Partitions:
      - ES
      - ES_historic
      - ES_OutsideSisNet

- name: exchange_rate
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: test_mintdatalake_02
      uat: test_mintdatalake_02
      prod: prod_mintdatalake_02
    schema: rdm_dbo
    table: mint_mapping_roe
    sourceSystem: MINT_RAW_DATALAKE

- name: policy_limit
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Policy
    Table: Limit

ColumnSpecs:
  ClaimsSectionPolicyID:  # The same content of PolicySectionID;
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  PolicySectionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  SectionEffectiveFromDate:
    dateTimeFormat: ISO
  SectionEffectiveToDate:
    dateTimeFormat: ISO
  KeyReserving:
    NotInSource: True
