SELECT DISTINCT
    A<PERSON>NUMESINI AS 'KeyInternSchadenummer',
    CASE
        WHEN (CASE WHEN B.NPOLICY = '' THEN B.POLIORIG ELSE B.NPOLICY END) IS NOT NULL THEN
            CONCAT(
                B.CODPROD
                ,' '
                ,(SELECT MIN(CAST(Z.YEAREFEC AS float)) FROM NTJDWHMRK..MEDLMAPS Z WHERE D.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK)
                ,'/'
                ,CASE WHEN B.NPOLICY = '' THEN B.POLIORIG ELSE B.NPOLICY END
                ,'_'
                ,(CAST(D.YEAREFEC AS float) - (SELECT MIN(CAST(Z.YEAREFEC AS float)) FROM NTJDWHMRK..MEDLMAPS Z WHERE D.ID_MEDLMAPO_FK = Z.ID_MEDLMAPO_FK) + 1)
            )
        WHEN (CASE WHEN B.NPOLICY = '' THEN B.POLIORIG ELSE B.NPOLICY END) IS NULL THEN C.POLICODE
    END AS 'KeyIdPolis',
    CASE
        WHEN (CASE WHEN B.NPOLICY = '' THEN B.POLIORIG ELSE B.NPOLICY END) IS NOT NULL THEN
            CONCAT (
                TRIM(CASE WHEN B.NPOLICY = '' THEN B.POLIORIG ELSE B.NPOLICY END),
                CASE
                    WHEN B.SECREGRU = '1. Construction Professional Risks' THEN '/MA1'
                    WHEN B.SECREGRU = '2. Financial Services Professional Risks' THEN '/MA2'
                    WHEN B.SECREGRU = '3. Miscellaneous Professional Risks' THEN '/MA3'
                    WHEN B.SECREGRU = '4. Professions Professional Risks' THEN '/MA4'
                    when B.SECREGRU = '6. D and O' THEN '/MA5'
                    WHEN B.SECREGRU = '11. Liability' THEN '/MA6'
                    WHEN B.SECREGRU = 'Surety' THEN '/MA7'
                    WHEN B.SECREGRU = 'Personal Accident' THEN '/MA8'
                    WHEN B.SECREGRU = 'Private Clinics' THEN '/MA9'
                ELSE '/MA' END
            )
        WHEN (CASE WHEN B.NPOLICY = '' THEN B.POLIORIG ELSE B.NPOLICY END) IS NULL THEN
            CONCAT(
                (
                    SELECT DISTINCT LEFT(X.POLIREFE, CHARINDEX('_',X.POLIREFE)-1)
                    FROM NTJDWHMRK..MEDFPOLI X
                    WHERE A.NUMEPOLI = (LEFT(X.POLIREFE, CHARINDEX('_',X.POLIREFE)-1))
                    AND A.YEAREFEC = X.YEAOFACC
                ),
                SUBSTRING(W.SECTREFE,CHARINDEX('/',W.SECTREFE),LEN(W.SECTREFE))
            )
    END AS 'KeyDekkingsNummer',
    F.ID_MEDLMASS AS 'KeySchadeBoekingsNummer',
    '' AS 'Payee',
    '1' AS 'RateOfExchange',
    '' AS 'TransactionAuthorisationDate',
    'EUR' AS 'TransactionCurrencyCode',
    F.FECHMOVI AS 'TransactionDate',
    F.DESCMOVI AS 'TransactionNarrative',
    'None' AS 'TransactionReference',
    'None' AS 'TransactionSequenceNumber',
    CASE
        WHEN F.TIPOMOVI = 'Reserva' THEN 'M'
        WHEN F.TIPOMOVI = 'Pago' THEN 'PAGO_EMITIDO'
        WHEN F.TIPOMOVI = 'Recobro' THEN 'PAGO_EMITIDO'
    END AS 'TransactionTypeCode',
    F.TIPOMOVI AS 'TransactionTypeDescription'
FROM NTJDWHMRK..MEDLMASI A
LEFT JOIN NTJDWHMRK..MEDLMASS F ON A.ID_MEDLMASI = F.ID_MEDLMASI_FK
LEFT JOIN NTJDWHMRK..MEDLMAPO B ON A.NUMEPOLI = (CASE WHEN B.NPOLICY = '' THEN B.POLIORIG ELSE B.NPOLICY END)
LEFT JOIN NTJDWHMRK..MEDLMAPS D ON B.ID_MEDLMAPO = D.ID_MEDLMAPO_FK AND CAST(D.YEAREFEC AS FLOAT) = CAST(A.YEAREFEC AS FLOAT)
LEFT JOIN NTJDWHMRK..MEDFPOLI C ON A.NUMEPOLI = LEFT(C.POLIREFE, CHARINDEX('_',C.POLIREFE) - 1) AND A.YEAREFEC = C.YEAOFACC
LEFT JOIN NTJDWHMRK..MEDFPOSE W ON C.ID_MEDFPOLI = W.ID_MEDFPOLI_FK
