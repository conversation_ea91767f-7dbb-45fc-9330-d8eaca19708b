from pyspark.sql import DataFrame

from models_scripts.transformations.common.misc import enforce_nulls_type
from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.add_columns import (
    get_german_country_using_policy_table,
)


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["Section_main"]
    policy_df = df_dict["Claim_Policy"]
    output_df = get_german_country_using_policy_table(
        main_df, policy_df, "KeyInternSchadenummer"
    )
    output_df = enforce_nulls_type(output_df)

    return output_df
