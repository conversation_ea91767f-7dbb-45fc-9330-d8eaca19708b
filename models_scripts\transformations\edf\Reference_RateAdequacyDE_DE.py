import pyspark.sql.functions as F
from pyspark.sql import DataFrame

from models_scripts.transformations.common.add_columns import (
    add_column_from_match_table,
)
from models_scripts.transformations.common.misc import pandas_to_spark, spark_to_pandas
from models_scripts.transformations.common.traits import business_logic


def drop_duplicates_based_on_pk(main_df: DataFrame) -> DataFrame:
    """Drops duplicate rows from the DataFrame based on the primary key columns."""
    main_pdf = spark_to_pandas(main_df)

    pk_columns = ["POLICY_NR", "CONTRACT_NR", "RISK_NR", "YEAROFACCOUNT"]
    main_pdf = main_pdf.sort_values(by=pk_columns)
    main_pdf = main_pdf.drop_duplicates(subset=pk_columns, keep="last")

    return pandas_to_spark(main_pdf)


def set_tarifversion(
    df: DataFrame, df_sev_vertrag: DataFrame, df_sev_police: DataFrame
) -> DataFrame:
    # Select columns:
    df_sev_vertrag = df_sev_vertrag.select(
        F.col("POL_POLICEN_NR").alias("POLICY_NR"),
        F.col("VER_VERTRAG_NR").alias("CONTRACT_NR"),
        F.col("VER_TARIFVERSION").alias("Tarifversion"),
        "VER_STATUS",
        "VER_HISTORIE",
    )
    df_sev_police = df_sev_police.select(
        F.col("POL_POLICEN_NR").alias("POLICY_NR"), "POL_HISTORIE"
    )

    # Apply filters:
    df_sev_vertrag = df_sev_vertrag.filter(
        'VER_STATUS != "Angebot" AND VER_HISTORIE = "AN"'
    )
    df_sev_police = df_sev_police.filter('POL_HISTORIE = "AN"')

    # Apply joins:
    df_sev = add_column_from_match_table(
        df_sev_vertrag, df_sev_police, "POLICY_NR", {"POL_HISTORIE": None}
    )
    df_sev = df_sev.filter('POL_HISTORIE = "AN" AND VER_HISTORIE = "AN"')
    df = add_column_from_match_table(
        df, df_sev, ["POLICY_NR", "CONTRACT_NR"], {"Tarifversion": None}
    )

    return df


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["Silver_RateAdequacyDE"]
    df_sev_vertrag = df_dict["RateAdequacyDE_sev_vertrag"]
    df_sev_police = df_dict["RateAdequacyDE_sev_police"]

    output_df = drop_duplicates_based_on_pk(main_df)
    output_df = set_tarifversion(output_df, df_sev_vertrag, df_sev_police)
    return output_df
