from pyspark.sql import DataFrame
from pyspark.sql import functions as F

from models_scripts.transformations.common.misc import enforce_nulls_type
from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.add_columns import (
    get_german_country_using_policy_table,
)


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["Transaction_main"]
    policy_df = df_dict["Claim_Policy"]

    # Filter out '2017-09-30', '2017-10-31', as to not have duplicates when adding de_historic
    dates_to_be_filtered = ["2017-09-30", "2017-10-31"]
    filter_cond = ~F.col("TransactionDate").isin(dates_to_be_filtered)

    main_filtered_df = main_df.filter(filter_cond)

    output_df = get_german_country_using_policy_table(
        main_filtered_df, policy_df, "KeyInternSchadenummer"
    )
    output_df = enforce_nulls_type(output_df)

    return output_df
