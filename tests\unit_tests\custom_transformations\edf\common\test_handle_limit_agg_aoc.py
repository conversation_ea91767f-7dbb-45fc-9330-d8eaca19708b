import pytest
from mines2.core.constants import SOURCE_SYSTEM
from mines2.core.extensions.misc import assert_dataframe_equality

from models_scripts.transformations.edf.common.handle_limit_agg_aoc import (
    calculate_limits,
    handle_null_limits_spark,
)


def test_calculate_limits(spark):
    """Test calculate_limits function."""
    input_df = spark.createDataFrame(
        [
            ("1", "1", "USD", "AGG", 1000, ""),
            ("1", "1", "USD", "AOC", 2000, ""),
            ("1", "2", "USD", "AOC", 3000, ""),
            ("2", "2", "EUR", "AGG", 3000, ""),
            ("2", "2", "EUR", "AOC", 4000, ""),
        ],
        [
            "KeyIdPolis",
            "KeyDekkingsNummer",
            "LimitCurrencyCode",
            "LimitBasisCode",
            "Limit",
            SOURCE_SYSTEM,
        ],
    )
    expected_df = spark.createDataFrame(
        [
            ("1", "1", "USD", "", 1000, 2000),
            ("1", "2", "USD", "", None, 3000),
            ("2", "2", "EUR", "", 3000, 4000),
        ],
        [
            "KeyIdPolis",
            "KeyDekkingsNummer",
            "LimitCurrencyCode",
            SOURCE_SYSTEM,
            "LimitAGG",
            "LimitAOC",
        ],
    )
    output_df = calculate_limits(input_df)
    assert_dataframe_equality(output_df, expected_df)

    # test with different LimitBasisCode:
    input_df_other = spark.createDataFrame(
        [
            ("1", "1", "EUR", "LIMISINI", 1000.0, ""),
            ("1", "2", "EUR", "LIMISINI", 2000.0, ""),
            ("2", "2", "EUR", "LIMISINI", 3000.0, ""),
        ],
        [
            "KeyIdPolis",
            "KeyDekkingsNummer",
            "LimitCurrencyCode",
            "LimitBasisCode",
            "Limit",
            SOURCE_SYSTEM,
        ],
    )
    expected_df = spark.createDataFrame(
        [
            ("1", "1", "EUR", "", 1000.0),
            ("1", "2", "EUR", "", 2000.0),
            ("2", "2", "EUR", "", 3000.0),
        ],
        schema=(
            "KeyIdPolis: string, KeyDekkingsNummer: string, LimitCurrencyCode: string, "
            "MINES_SourceSystem: string, LIMISINI: double"
        ),
    )
    output_df = calculate_limits(input_df_other)
    assert_dataframe_equality(output_df, expected_df)


def test_handle_null_limits_spark(spark):
    """Test handle_null_limits_spark function."""
    input_df = spark.createDataFrame(
        [
            ("1", "1", "USD", None, 2000),
            ("1", "2", "USD", 1000, None),
            ("2", "2", "EUR", 3000, 4000),
            ("2", "3", "EUR", None, None),
        ],
        [
            "KeyIdPolis",
            "KeyDekkingsNummer",
            "LimitCurrencyCode",
            "LimitAGG",
            "LimitAOC",
        ],
    )
    expected_df = spark.createDataFrame(
        [
            ("1", "1", "USD", 2000, 2000),
            ("1", "2", "USD", 1000, 1000),
            ("2", "2", "EUR", 3000, 4000),
            ("2", "3", "EUR", None, None),
        ],
        [
            "KeyIdPolis",
            "KeyDekkingsNummer",
            "LimitCurrencyCode",
            "LimitAGG",
            "LimitAOC",
        ],
    )
    output_df = handle_null_limits_spark(input_df)
    assert_dataframe_equality(output_df, expected_df)

    input_df = spark.createDataFrame(
        [
            ("1", "1", "USD", None),
            ("2", "2", "EUR", 3000),
        ],
        [
            "KeyIdPolis",
            "KeyDekkingsNummer",
            "LimitCurrencyCode",
            "Limit",
        ],
    )
    with pytest.raises(AssertionError) as excinfo:
        output_df = handle_null_limits_spark(input_df)
    assert "Missing LimitAGG or LimitAOC columns" in str(excinfo.value)
