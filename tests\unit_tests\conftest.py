import os
import uuid

import mines2.core.base as base
import mines2.core.constants as const
import pytest
from mines2.core.extensions.misc import delete_mounted_dir, file_exists

TEST_ROOT_PATH = "test_mines"
os.environ["PYARROW_IGNORE_TIMEZONE"] = "1"


@pytest.fixture(scope="session")
def spark():
    yield base.spark
    base.spark.catalog.clearCache()


@pytest.fixture(scope="session", autouse=True)
def set_timezone(spark):
    spark.conf.set("spark.sql.session.timeZone", "UTC")
    spark.sql("SET TIME ZONE 'UTC'")
    os.environ["TZ"] = "UTC"


def remove_tmp_test_paths(root_path: str):
    clean_path = os.path.join("/mnt", const.LAKE_LAYER_CLEAN, root_path)
    quarantine_path = os.path.join("/mnt", const.LAKE_LAYER_QUARANTINE, root_path)
    system_path = os.path.join("/mnt", "system", root_path)
    paths_to_remove = [clean_path, quarantine_path, system_path]
    for path in paths_to_remove:
        if file_exists(path):
            delete_mounted_dir(path)


def remove_tmp_test_databases(root_path: str):
    database_pattern = root_path.replace("-", "_").replace("/", "_")
    for db_row in base.spark.sql(f"SHOW DATABASES from {const.CATALOG_NAME}").collect():
        database = db_row.databaseName
        if database.startswith(database_pattern):
            base.spark.sql(
                f"DROP DATABASE IF EXISTS {const.CATALOG_NAME}.{database} CASCADE"
            )


@pytest.fixture(scope="session")
def base_root_path():
    root_path = os.path.join(TEST_ROOT_PATH, str(uuid.uuid4())[:8])
    yield root_path
    remove_tmp_test_paths(root_path)
    remove_tmp_test_databases(root_path)
