Columns:
  ClaimsID:
    dataType: string
  KeyInternSchadenummer:
    dataType: string
  AttritionalLargeFlag:
    dataType: string
  BackgroundNarrative:
    dataType: string
    PII: true
  CatastropheCode:
    dataType: string
  CatastropheDescription:
    dataType: string
  ClaimCode:
    dataType: string
  ClaimCountry:
    dataType: string
  ClaimDeniedIndicator:
    dataType: string
  ClaimDescription:
    dataType: string
    PII: true
  ClaimDiaryDate:
    dataType: date
  ClaimEventCode:
    dataType: string
  ClaimEventDescription:
    dataType: string
  ClaimHandler:
    dataType: string
    PII: true
  ClaimHandlerCode:
    dataType: string
  ClaimInsured:
    dataType: string
  ClaimLastModifiedDate:
    dataType: date
  ClaimLeadIndicator:
    dataType: string
  ClaimLocationState:
    dataType: string
  ClaimOpenDate:
    dataType: date
  ClaimReference:
    dataType: string
  ClaimReportDate:
    dataType: date
  ClaimStatus:
    dataType: string
  ClaimYearOfAccount:
    dataType: int
    RestrictMaxValue: year(add_months(current_date(), 3 * 12))
    RestrictMinValue: 1970
  CloseDate:
    dataType: date
  CoverageNarrative:
    dataType: string
  CoverholderWithClaimsAuthority:
    dataType: string
  DateOfDeclinature:
    dataType: date
  DateOfLoss:
    dataType: date
  GeographicalOriginOfTheClaim:
    dataType: string
  LineageReference:
    dataType: string
  LitigationCode:
    dataType: string
  LitigationDescription:
    dataType: string
  MaximumPotentialLoss:
    dataType: float
  MaximumPotentialLossRounded:
    dataType: currency
  MaximumPotentialLossCurrency:
    dataType: string
    conformCurrency: True
  MaximumPotentialLossPercentage:
    dataType: float
  MaximumPotentialLossGBP:
    dataType: double
  MaximumPotentialLossEUR:
    dataType: double
  MaximumPotentialLossUSD:
    dataType: double
  MaximumPotentialLossCAD:
    dataType: double
  MaximumPotentialLossRoundedGBP:
    dataType: currency
  MaximumPotentialLossRoundedEUR:
    dataType: currency
  MaximumPotentialLossRoundedUSD:
    dataType: currency
  MaximumPotentialLossRoundedCAD:
    dataType: currency
  OriginalCurrencyCode:
    dataType: string
    conformCurrency: True
  PolicyYearOfAccount:
    dataType: int
    RestrictMaxValue: year(add_months(current_date(), 3 * 12))
    RestrictMinValue: 1970
  PreviousClaimReference:
    dataType: string
  PreviousSourceSystem:
    dataType: string
  PreviousSourceSystemDescription:
    dataType: string
  ReasonDeclined:
    dataType: string
  ReserveNarrative:
    dataType: string
  ServiceProviderReference:
    dataType: string
  SettlementCurrencyCode:
    dataType: string
    conformCurrency: True
  SubrogationSalvageIndicator:
    dataType: string
  TacticsNarrative:
    dataType: string
  TPAHandleIndicator:
    dataType: string
  TriageCode:
    dataType: string
  XCSClaimRef:
    dataType: string
  XCSClaimCode:
    dataType: string
primaryKey:
- ClaimsID
