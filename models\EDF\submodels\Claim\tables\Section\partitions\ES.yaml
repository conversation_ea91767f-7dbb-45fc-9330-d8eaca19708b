Country: ES
existCustomTransformation: 'True'

dataSource:
- name: main
  type: SourceSisnetES
  parameters:
    sqlFileName: Section.sql
    querySourceType: SQL_FILE
- name: mapping_table
  type: EuropeanDatalake
  parameters:
      Layer: clean
      subModel: Support
      Table: SisnetSCSMappingTableES

ColumnSpecs:
  ClaimsID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyInternSchadenummer
        sep: ':'
  ClaimsSectionPolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  ClaimsSectionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyInternSchadenummer
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  KeyInternSchadenummer:
    locale: en_US.utf8
  KeyIdPolis:
    locale: en_US.utf8
  KeyDekkingsNummer:
    locale: en_US.utf8
  SectionProductCode:
    locale: en_US.utf8
  SectionReference:
    locale: en_US.utf8
