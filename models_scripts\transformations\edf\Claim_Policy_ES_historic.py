import mines2.core.constants as const
import pyspark.sql.functions as F
from pyspark.sql import DataFrame, Window

from models_scripts.transformations.common.add_columns import (
    add_column_from_match_table,
)
from models_scripts.transformations.common.misc import enforce_nulls_type
from models_scripts.transformations.common.traits import business_logic


def deduplicate_ids(df, match_column):
    """Deduplicate ID for ES and ES_historic, priority for ES"""
    df = df.withColumn("Priority", F.when(F.col("Partition") == "ES", 1).otherwise(2))
    df = df.withColumn(
        "rnum",
        F.row_number().over(Window.partitionBy(match_column).orderBy("Priority")),
    )
    df = df.filter(F.col("rnum") == 1)
    df = df.drop("rnum", "Priority")

    return df


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["Policy_main"].drop(const.SOURCE_SYSTEM)
    policy_df = df_dict["Policy_Policy"].drop(const.SOURCE_SYSTEM)
    policy_df = deduplicate_ids(policy_df, "KeyIdPolis")
    output_df = add_column_from_match_table(
        main_df,
        policy_df,
        "KeyIdPolis",
        {"YearOfAccount": "PolicyYearOfAccount"},
    )
    output_df = enforce_nulls_type(output_df)

    return output_df.withColumn(
        const.SOURCE_SYSTEM, F.lit("SISNET_SCS_TEMP_SOURCE_TO_BE_REPLACED")
    )
