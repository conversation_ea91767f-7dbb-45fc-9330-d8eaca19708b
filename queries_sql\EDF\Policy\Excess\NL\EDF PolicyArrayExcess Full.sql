select distinct p.idpolis                                      					as "KeyIdPolis"
, d.dekkingsnummer                                                      as "KeyDekkingsNummer"
, d.vrijveld6                                                           as "Excess"
, 'AOC'                                                                 as "ExcessBasisCode"
, 'ANY OTHER CLAIM'                                                     as "ExcessBasisDescription"
, 'EURO'                                                                as "ExcessCurrencyCode"
from pub.dekking d
inner join pub.polisversie p on p.internpolisnummer = d.internpolisnummer
where d.dek<PERSON><PERSON> in (160, 260, 360)
union all
select distinct p.idpolis                                      					as "KeyIdPolis"
, d.dekkingsnummer                                                      as "KeyDekkingsNummer"
, d.vrijveld6                                                           as "Excess"
, 'AOC'                                                                 as "ExcessBasisCode"
, 'ANY OTHER CLAIM'                                                     as "ExcessBasisDescription"
, 'EURO'                                                                as "ExcessCurrencyCode"
from pub.histdekking d
inner join pub.histpolisversie p on p.idpolis = d.idpolis
where p.internschadenummer = 0 and d.dekkingscode in (160, 260, 360)
