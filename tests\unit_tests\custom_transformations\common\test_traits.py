from unittest.mock import Mock

import mines2.core.constants as const
from pyspark.sql.functions import col

from models_scripts.transformations.common.traits import RecordsMarker, mark_records


def test_mark_records_decorator(spark):
    # noinspection PyTestUnpassedFixture
    @mark_records
    def dummy_function(dummy_df, records_marker):
        return records_marker.mark_records(dummy_df, df["value"] > 5)

    df = spark.createDataFrame([(1,), (10,), (20,)], ["value"])
    result_df = dummy_function(df)
    assert const.MARKED_RECORD_COLUMN_NAME in result_df.columns
    assert (
        result_df.filter(col(const.MARKED_RECORD_COLUMN_NAME).isNotNull()).count() == 2
    )


def test_records_marker_mark_records(spark):
    marker = RecordsMarker("dummy_function", Mock())
    df = spark.createDataFrame([(1,), (10,), (20,)], ["value"])
    result_df = marker.mark_records(df, df["value"] > 5)
    assert const.MARKED_RECORD_COLUMN_NAME in result_df.columns
    assert (
        result_df.filter(col(const.MARKED_RECORD_COLUMN_NAME).isNotNull()).count() == 2
    )


def test_records_marker_no_records_affected(spark):
    marker = RecordsMarker("dummy_function", Mock())
    marker.no_records_affected()
    assert marker.is_function_called


def test_records_marker_mark_records_no_affected_records(spark):
    marker = RecordsMarker("dummy_function", Mock())
    df = spark.createDataFrame([(1,), (10,), (20,)], ["value"])
    result_df = marker.mark_records(df, df["value"] > 100)
    assert const.MARKED_RECORD_COLUMN_NAME in result_df.columns
    assert (
        result_df.filter(col(const.MARKED_RECORD_COLUMN_NAME).isNotNull()).count() == 0
    )


def test_records_marker_mark_records_correct_message_with_value(spark):
    marker = RecordsMarker("dummy_function", Mock())
    df = spark.createDataFrame([(1,), (10,), (20,)], ["value"])
    result_df = marker.mark_records(df, df["value"] > 15, "test", "value")
    assert const.MARKED_RECORD_COLUMN_NAME in result_df.columns
    assert (
        result_df.filter(
            col(const.MARKED_RECORD_COLUMN_NAME) == "dummy_function:test:value=20"
        ).count()
        == 1
    )


def test_records_marker_mark_records_correct_message_without_value(spark):
    marker = RecordsMarker("dummy_function", Mock())
    df = spark.createDataFrame([(1,), (10,), (20,)], ["value"])
    result_df = marker.mark_records(df, df["value"] > 15, "test")
    assert const.MARKED_RECORD_COLUMN_NAME in result_df.columns
    assert (
        result_df.filter(
            col(const.MARKED_RECORD_COLUMN_NAME) == "dummy_function:test"
        ).count()
        == 1
    )


def test_records_marker_mark_records_trim_start(spark):
    marker = RecordsMarker("dummy_function", Mock())
    df = spark.createDataFrame([(1,), (10,), (20,)], ["value"])
    result_df = marker.mark_records(df, df["value"] == 20, "test")
    result_df = marker.mark_records(result_df, df["value"] == 20, "test_2", "value")
    result_df = marker.mark_records(result_df, df["value"] == 1, "test_3")
    assert const.MARKED_RECORD_COLUMN_NAME in result_df.columns
    assert (
        result_df.filter(
            col(const.MARKED_RECORD_COLUMN_NAME)
            == "dummy_function:test && dummy_function:test_2:value=20"
        ).count()
        == 1
    )
    assert (
        result_df.filter(
            col(const.MARKED_RECORD_COLUMN_NAME) == "dummy_function:test_3"
        ).count()
        == 1
    )
