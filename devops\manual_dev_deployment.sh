#!/bin/bash

set -e -o pipefail

for f in $(ls dist/); do if [[ "$f" == *.whl ]]; then rm dist/"$f"; fi; done
for f in $(ls dist/); do if [[ "$f" == *.tar.gz ]]; then rm dist/"$f"; fi; done

poetry build

# databricks configure -token

BranchName=$(git symbolic-ref --short HEAD)
ModelName="EDF"

python3 -m devops.generate_model_json

echo "Updating files of ${BranchName} on datalake"
if az storage fs directory exists -f models -n $BranchName --account-name minteuwestaideveudl01 --sas-token $SAS_TOKEN_MODELS| jq -r '.exists' | grep -q 'true'; then
    az storage fs directory delete -f models -n $BranchName --account-name minteuwestaideveudl01 --sas-token $SAS_TOKEN_MODELS --yes
fi
az storage fs directory upload -f models --account-name minteuwestaideveudl01 -s "models/*" -d $BranchName --sas-token $SAS_TOKEN_MODELS --recursive

if [ -d "queries_sql" ] && [ "$(ls -A queries_sql)" ]; then
    echo "Uploading sql queries folder"
    az storage fs directory upload -f models --account-name minteuwestaideveudl01 -s "queries_sql" -d $BranchName --recursive --sas-token $SAS_TOKEN_MODELS
else
    echo "Sql queries folder does not present"
fi

az storage fs directory upload -f models --account-name minteuwestaideveudl01 -s "dist/*" -d $BranchName/package/$ModelName --recursive --sas-token $SAS_TOKEN_MODELS --recursive
az storage fs file upload -f models --account-name minteuwestaideveudl01 -s "run_mines2.py" -p $BranchName/package/$ModelName/run_mines2.py --sas-token $SAS_TOKEN_MODELS --overwrite

cd edf_databricks
databricks bundle deploy --target dev
