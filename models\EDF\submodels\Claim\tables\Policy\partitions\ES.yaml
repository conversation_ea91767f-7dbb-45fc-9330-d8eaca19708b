Country: ES
existCustomTransformation: 'True'
dataSource:
- name: main
  type: SourceSisnetES
  parameters:
    sqlFileName: Policy.sql
    querySourceType: SQL_FILE

- name: mapping_table
  type: EuropeanDatalake
  parameters:
      Layer: clean
      subModel: Support
      Table: SisnetSCSMappingTableES

- name: policy
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Policy
    Table: Policy
    Partitions:
      - ES
      - ES_historic

ColumnSpecs:
  ClaimsPolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  ClaimsID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: ES
        - source: COLUMN
          name: KeyInternSchadenummer
        sep: ':'
  KeyInternSchadenummer:
    locale: en_US.utf8
  PolicyCode:
    locale: en_US.utf8
  PolicyReference:
    locale: en_US.utf8
  PolicyYearOfAccount:
    NotInSource: True
