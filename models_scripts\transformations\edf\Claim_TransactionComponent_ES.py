import mines2.core.constants as const
import pyspark.sql.functions as F
from pyspark.sql import DataFrame, Window

from models_scripts.transformations.common.add_columns import (
    add_column_from_match_table,
)
from models_scripts.transformations.common.traits import business_logic, transformation
from models_scripts.transformations.edf.common.currency import ClaimCurrencyConversor


class ExtraColumnsClaimTransactionComponentMerger:
    """Merge extra columns to main df. Will Copy or calculate missing columns."""

    join_cols = ("KeyInternSchadenummer", "KeyDekkingsNummer")

    copy_cols = ("KeyIdPolis",)

    prefix = "EXTRA_RECORDS_SOURCE01:"

    def __init__(self, main_df: DataFrame, extra_records_df: DataFrame):
        self.main_df = main_df
        self.extra_records_df = extra_records_df
        self.main_df_columns = set(main_df.columns)

    def _copy_cols(self) -> DataFrame:
        """Copy columns from ClaimTransactionComponent to extra_records_df."""
        not_in_join_cols = self.main_df_columns - set(self.join_cols)
        deduplication_window = Window.partitionBy(*self.join_cols).orderBy(
            *not_in_join_cols
        )
        deduplicated_df = (
            self.main_df.withColumn(
                "row_number", F.row_number().over(deduplication_window)
            )
            .filter("row_number = 1")
            .drop("row_number")
        )
        preprocessed_df = deduplicated_df.select(*self.join_cols, *self.copy_cols)

        joined_df = self.extra_records_df.join(
            preprocessed_df, on=list(self.join_cols), how="left"
        )
        return joined_df

    def _union_dfs(self, extra_df: DataFrame) -> DataFrame:
        """Union df and extra_records_df."""
        return self.main_df.unionByName(extra_df.select(*self.main_df_columns))

    @transformation
    def append_new_records_to_main_df(self) -> DataFrame:
        """Append new records to main df."""
        with_copied_cols_df = self._copy_cols()
        union_df = self._union_dfs(with_copied_cols_df)
        return union_df


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["TransactionComponent_main"].drop(const.SOURCE_SYSTEM)
    extra_records_df = df_dict["Support_ExtraTransactionsAdjustmentsES"].drop(
        const.SOURCE_SYSTEM
    )
    sisnet_mapping_table_df = df_dict["Support_SisnetSCSMappingTableES"].drop(
        const.SOURCE_SYSTEM
    )
    exchange_rate_df = df_dict["TransactionComponent_exchange_rate"].drop(
        const.SOURCE_SYSTEM
    )

    merger = ExtraColumnsClaimTransactionComponentMerger(main_df, extra_records_df)
    main_with_new_records_df = merger.append_new_records_to_main_df()

    # Add new Claims from mapping SCS SISNET table
    new_claims_df = add_column_from_match_table(
        main_with_new_records_df,
        sisnet_mapping_table_df,
        ["KeyInternSchadenummer", "KeyIdPolis"],
        {
            "SCS_KeyInternSchadenummer": "KeyInternSchadenummer",
            "SCS_KeyIdPolis": "KeyIdPolis",
            "SCS_KeyDekkingsNummer": "KeyDekkingsNummer",
        },
        coalesce_existing=True,
        how="right",
    )

    component_with_converted_currencies_df = (
        ClaimCurrencyConversor.add_new_columns_using_single_currency(
            new_claims_df, exchange_rate_df, "EUR"
        )
    )

    # Add source system
    output_df = _add_temp_source_system(component_with_converted_currencies_df)
    output_df = _fill_missing_typecodes(output_df)
    return output_df


def _add_temp_source_system(df: DataFrame) -> DataFrame:
    return df.withColumn(
        const.SOURCE_SYSTEM, F.lit("SISNET_SCS_TEMP_SOURCE_TO_BE_REPLACED")
    )


def _fill_missing_typecodes(df: DataFrame) -> DataFrame:
    return df.withColumn(
        "TransactionComponentTypeCode", F.lit("__MINES__TEMP_TYPECODE__BUG_36455")
    )
