Country: custom
existCustomTransformation: 'True'

dataSource:
- name: main
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: dev_europeandatalake_01
      uat: uat_europeandatalake_01
      prod: prod_europeandatalake_01
    schema: DE_Direct
    table: Policy_Policy
    sourceSystem: SPARK_EDF_EXPORT

- name: re_underwriting
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Reference
    Table: ReUnderwriting

- name: exchange_rate
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: test_mintdatalake_02
      uat: test_mintdatalake_02
      prod: prod_mintdatalake_02
    schema: rdm_dbo
    table: mint_mapping_roe
    sourceSystem: MINT_RAW_DATALAKE

ColumnSpecs:
  OrderPercentage:
    locale: en_US.utf8
  AssuredAnnualTurnover:
    locale: en_US.utf8
  ClaimsPolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: DE
        - source: COLUMN
          name: PolicyCode
        sep: ':'
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: DE
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  BrokerCodeID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: DE
        - source: COLUMN
          name: BrokerCode
        sep: ':'
  ExpiryDate:
    dateTimeFormat: ISO
  InceptionDate:
    dateTimeFormat: ISO
  PeerReview1Date:
    dateTimeFormat: ISO
  PeerReview2Date:
    dateTimeFormat: ISO
  PolicyLastModifiedDate:
    dateTimeFormat: ISO
    needsEpochConversion: True
  UnderwriterCodeID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: DE
        - source: COLUMN
          name: UnderwriterCode
        sep: ':'
  WrittenDate:
    dateTimeFormat: ISO
  ReUnderwritingExercise:
    NotInSource: True
  BusinessClassification:
    NotInSource: True
  PolicySequenceNumber:
    NotInSource: True
  IPTLiable:
    NotInSource: True
