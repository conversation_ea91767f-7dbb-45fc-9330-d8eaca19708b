from pyspark.sql import DataFrame

from models_scripts.transformations.common.misc import cast_to_int_and_to_string
from models_scripts.transformations.common.traits import business_logic


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["Section_main"]
    main_df = cast_to_int_and_to_string(main_df, "KeyIdPolis")

    return main_df
