select distinct
    p.Idpolis
    , p.InternP<PERSON>nummer
    , p.Polisnummer
    , year(ifnull(p.histhoofdpremievervaldatum, p.ingangsdatum))    AS 'UWYr'
    , ifnull(p.histhoofdpremievervaldatum, p.ingangsdatum)          AS 'UWDt'
    , p.Expiratiedatum
    , case when p.expiratiedatum is not null and p.soortpolis <> 0 then p.expiratiedatum else p.contractvervaldatum end as 'Expiry'
    , 'POLIS'                                                       AS 'Source'
    , p.<PERSON>reatie<PERSON>tum
    --, p.Mutatiedatum
    , case when p.Mutatiedatum >= ifnull(p.histhoofdpremievervaldatum, p.ingangsdatum) then p.Mutatiedatum else ifnull(p.histhoofdpremievervaldatum, p.ingangsdatum) end as 'Mutatiedatum'
    , p.<PERSON>
    --, p.Lopendepolis
    , p.Termijn
    , p.Soortpolis
    , p.Productcode
    , d.<PERSON>
    , d.<PERSON>num<PERSON>
    , ltrim(to_char(d.<PERSON>nummer)) + ltrim(to_char(p.productcode)) + ltrim(to_char(d.dekkingscode)) AS 'SectionId'
    , d.BrutoPremie                                                 AS 'GPRM'
    , d.DoorlProvisieTp                                             AS 'BKR'
    , d.BrutoPremie - d.DoorlProvisieTp                             AS 'NPRM'
    , d.Assurantiebelasting                                         AS 'IPT'
from pub.dekking d
inner join pub.polisversie p on p.internpolisnummer = d.internpolisnummer
