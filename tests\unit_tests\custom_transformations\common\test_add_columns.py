import pyspark.sql.functions as F
import pytest
from mines2.core.extensions.misc import assert_dataframe_equality
from pyspark.sql import DataFrame
from pyspark.sql.types import StringType, StructField, StructType

from models_scripts.transformations.common.add_columns import (
    add_column_from_match_table,
)


class TestAddColumnsFromMatchTable:
    def input_dataframes(self, spark) -> tuple[DataFrame, DataFrame]:
        df = spark.createDataFrame(
            [
                ("1", "a"),
                ("2", "b"),
                ("3", "c"),
                ("4", "d"),
                ("5", "e"),
                ("6", "f"),
            ],
            ["id", "value"],
        )
        match_df = spark.createDataFrame(
            [
                ("1", "a", "x", 1),
                ("2", "b", "y", 1),
                ("3", "c", "z", 1),
                ("4", "d", "w", 1),
                ("5", "e", "v", 1),
                ("7", "g", "t", 1),
            ],
            ["id", "value", "new_value", "const"],
        )
        return df, match_df

    def test_add_column_with_one_join_col(self, spark):
        df, match_df = self.input_dataframes(spark)
        expected_df = spark.createDataFrame(
            [
                ("1", "a", "x"),
                ("2", "b", "y"),
                ("3", "c", "z"),
                ("4", "d", "w"),
                ("5", "e", "v"),
                ("6", "f", None),
            ],
            ["id", "value", "new_value"],
        )
        output_df = add_column_from_match_table(
            df, match_df, "id", {"new_value": "new_value"}
        )
        assert_dataframe_equality(output_df, expected_df)

    def test_add_column_with_dict_join_col(self, spark):
        """Test add_column_from_match_table with a dict join_col."""
        df, match_df = self.input_dataframes(spark)
        expected_df = spark.createDataFrame(
            [
                ("1", "a", "x"),
                ("2", "b", "y"),
                ("3", "c", "z"),
                ("4", "d", "w"),
                ("5", "e", "v"),
                ("6", "f", None),
            ],
            ["id", "value", "new_value"],
        )
        output_df = add_column_from_match_table(
            df, match_df, {"id": "id"}, {"new_value": "new_value"}
        )
        assert_dataframe_equality(output_df, expected_df)

    def test_add_column_with_two_join_col(self, spark):
        df, match_df = self.input_dataframes(spark)

        expected_df = spark.createDataFrame(
            [
                ("1", "a", "x"),
                ("2", "b", "y"),
                ("3", "c", "z"),
                ("4", "d", "w"),
                ("5", "e", "v"),
                ("6", "f", None),
            ],
            ["id", "value", "new_value"],
        )
        output_df = add_column_from_match_table(
            df, match_df, ["id", "value"], {"new_value": None}
        )
        assert_dataframe_equality(output_df, expected_df)

    def test_replace_column(self, spark):
        df, match_df = self.input_dataframes(spark)
        expected_df = spark.createDataFrame(
            [
                ("1", "x"),
                ("2", "y"),
                ("3", "z"),
                ("4", "w"),
                ("5", "v"),
                ("6", None),
            ],
            ["id", "value"],
        )
        output_df = add_column_from_match_table(
            df, match_df, "id", {"new_value": "value"}
        )
        assert_dataframe_equality(output_df, expected_df)

    def test_replace_column_with_the_same_name(self, spark):
        df, match_df = self.input_dataframes(spark)
        match_df = match_df.select(
            F.col("id"), F.col("new_value").alias("value"), F.col("const")
        )
        expected_df = spark.createDataFrame(
            [
                ("1", "x"),
                ("2", "y"),
                ("3", "z"),
                ("4", "w"),
                ("5", "v"),
                ("6", None),
            ],
            ["id", "value"],
        )
        output_df = add_column_from_match_table(df, match_df, "id", {"value": "value"})
        assert_dataframe_equality(output_df, expected_df)

    def test_replace_col_and_add_column(self, spark):
        df, match_df = self.input_dataframes(spark)
        expected_df = spark.createDataFrame(
            [
                ("1", "x", 1),
                ("2", "y", 1),
                ("3", "z", 1),
                ("4", "w", 1),
                ("5", "v", 1),
                ("6", None, None),
            ],
            ["id", "value", "const"],
        )
        output_df = add_column_from_match_table(
            df, match_df, "id", {"new_value": "value", "const": None}
        )
        assert_dataframe_equality(output_df, expected_df)

    def test_empty_column_to_replicate(self, spark):
        """Return the same dataframe."""
        df, match_df = self.input_dataframes(spark)
        output_df = add_column_from_match_table(df, match_df, "id", {})
        assert_dataframe_equality(output_df, df)

    def test_duplicated_rows_in_match_table(self, spark):
        df, match_df = self.input_dataframes(spark)
        match_df = match_df.union(match_df)  # Add duplicated rows.
        with pytest.raises(AssertionError) as excinfo:
            add_column_from_match_table(df, match_df, "id", {"new_value": "value"})
        assert "match_df must be 0" in str(excinfo.value)

    def test_assertion_errors(self, spark):
        df, match_df = self.input_dataframes(spark)
        with pytest.raises(AssertionError) as excinfo:
            add_column_from_match_table(
                df, match_df, ["id", "id"], {"new_value": "value"}
            )
        assert "must be unique" in str(excinfo.value)
        with pytest.raises(AssertionError) as excinfo:
            add_column_from_match_table(df, match_df, ["id_"], {"new_value": "value"})
        assert "must be a subset" in str(excinfo.value)

    def test_join_with_empty_match_df(self, spark):
        df, match_df = self.input_dataframes(spark)
        match_df = match_df.filter("id = 0")  # Empty match_df.

        schema = StructType(
            [
                StructField("id", StringType(), True),
                StructField("value", StringType(), True),
                StructField("new_value", StringType(), True),
            ]
        )
        data = [
            ("1", "a", None),
            ("2", "b", None),
            ("3", "c", None),
            ("4", "d", None),
            ("5", "e", None),
            ("6", "f", None),
        ]
        expected_df = spark.createDataFrame(data, schema)

        output_df = add_column_from_match_table(
            df, match_df, "id", {"new_value": "new_value"}
        )
        assert_dataframe_equality(output_df, expected_df)

    def test_join_with_empty_df(self, spark):
        df, match_df = self.input_dataframes(spark)
        df = df.filter("id = 0")
        output_df = add_column_from_match_table(
            df, match_df, "id", {"new_value": "value"}
        )
        assert_dataframe_equality(output_df, df)
