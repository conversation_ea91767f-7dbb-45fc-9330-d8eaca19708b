from pyspark.sql import DataFrame

from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.add_columns import (
    add_key_reserving_and_german_country_col,
)


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["Section_main"]
    policy_df = df_dict["Policy_Policy"]

    output_df = add_key_reserving_and_german_country_col(main_df, policy_df)
    return output_df
