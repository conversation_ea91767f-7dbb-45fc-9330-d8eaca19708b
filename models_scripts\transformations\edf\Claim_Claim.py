from pyspark.sql import DataFrame

from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.currency import (
    ClaimClaimCurrencyConversor,
)


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["main_conformed_df"]
    exchange_rate_df = df_dict["Claim_exchange_rate"]

    # Drop the extra currency columns since the add_currency_columns method does not replace them:
    amount_cols = ["MaximumPotentialLoss", "MaximumPotentialLossRounded"]
    suffixes = ["GBP", "EUR", "USD", "CAD"]
    columns_to_drop = []
    for amount_col in amount_cols:
        columns_to_drop += [amount_col + suffix for suffix in suffixes]
    columns_to_drop.append("MaximumPotentialLossRounded")
    main_df = main_df.drop(*columns_to_drop)

    df_with_extra_currencies = ClaimClaimCurrencyConversor.add_currency_columns(
        main_df, exchange_rate_df
    )

    return df_with_extra_currencies
