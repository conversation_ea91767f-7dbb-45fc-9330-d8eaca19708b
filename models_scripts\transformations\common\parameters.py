from datetime import date, datetime

import pyspark.sql.functions as F
from pyspark.sql import DataFrame


def get_policy_number_parameters(
    policy_parameters_df: DataFrame, pipeline_id: str
) -> list[int]:
    """Get starting_policy_nr and end_policy_nr parameters.

    Args:
        policy_parameters_df (DataFrame): Dataframe with policy number parameters.
        pipeline_id (int): Pipeline run ID.

    Returns:
        List: starting_policy_nr, end_policy_nr parameters.
    """
    run_parameters_df = policy_parameters_df.filter(F.col("RunId") == pipeline_id)

    if run_parameters_df.isEmpty():
        raise ValueError(
            f"Parameters StartingPolicyNr and EndPolicyNr are not found for RunId '{pipeline_id}'. Please verify the 'support_policyparameters' table."
        )
    else:
        return [
            run_parameters_df.first()["StartingPolicyNr"],
            run_parameters_df.first()["EndPolicyNr"],
        ]


def get_date_parameter(
    custom_parameters: dict, param_name: str, is_optional: bool = False
) -> date:
    """Get date parameters.

    Args:
        custom_parameters (dict): Dictionary with custom parameters.
        param_name (str): Name of the date parameter.
        is_optional (bool): True - optional parameter, False - required

    Returns:
        date: Date parameter value.
    """
    if param_name not in custom_parameters and is_optional:
        return None
    elif param_name not in custom_parameters and not is_optional:
        raise KeyError(
            f"Required parameter '{param_name}' was not provided in the CustomParameters."
        )
    else:
        try:
            return datetime.fromisoformat(custom_parameters[param_name]).date()
        except:
            raise ValueError(
                f"Parameter '{param_name}' does not match the ISO format (YYYY-MM-DD)."
            )
