--per claim limit, current policy version
SELECT
    DISTINCT T1.<PERSON><PERSON><PERSON><PERSON><PERSON>LIS AS "KeyIdPolis",
    T1.<PERSON><PERSON><PERSON><PERSON>KKINGSNUMMER AS "KeyDekkingsNummer",
    CASE
        WHEN T1.LIMIT = '' THEN
            NULL
        WHEN T1.<PERSON><PERSON><PERSON> IS NULL THEN
            NULL
        ELSE
            TO_NUMBER(RTRIM(LTRIM( REPLACE( REPLACE(T1.LIMIT, '.', ''), ',00', '') )))
    END AS 'Limit',
    T1.LIMITBASISCODE AS "LimitBasisCode",
    T1.LIMITBASISDESCRIPTION AS "LimitBasisDescription",
    T1.<PERSON><PERSON><PERSON>CURRENCYCODE AS "LimitCurrencyCode",
    T1.TOPLIMIT AS "TopLimit"
FROM
    (
 --per claim limit, current policy version------------
        SELECT
            DISTINCT P.IDPOLIS AS "KeyIdPolis",
            D<PERSON>DEKKINGSNUMMER AS "KeyDekkingsNummer",
            CASE
                WHEN D.DEKKINGSCODE IN (800, 820) THEN
                    DV.RUBRIEK8
                ELSE
                    DV.RUBRIEK6
            END AS "Limit",
            'AOC' AS "LimitBasisCode",
            'ANY ONE CLAIM' AS "LimitBasisDescription",
            'EURO' AS "LimitCurrencyCode",
            'Y' AS "TopLimit"
        FROM
            PUB.DEKKING D
            INNER JOIN PUB.POLISVERSIE P
            ON P.INTERNPOLISNUMMER = D.INTERNPOLISNUMMER
            LEFT OUTER JOIN PUB.DEKKINGVRIJ DV
            ON D.INTERNPOLISNUMMER = DV.INTERNPOLISNUMMER
            AND D.DEKKINGSNUMMER = DV.DEKKINGSNUMMER UNION ALL
 --aggregate limit, current policy version------------
            SELECT
                DISTINCT P.IDPOLIS AS "KeyIdPolis",
                D.DEKKINGSNUMMER AS "KeyDekkingsNummer",
                CASE
                    WHEN D.DEKKINGSCODE IN (800, 820) THEN
                        DV.RUBRIEK8
                    ELSE
                        CASE
                            WHEN DV.RUBRIEK7 IS NULL THEN
                                DV.RUBRIEK6
                            WHEN DV.RUBRIEK7 = 0 THEN
                                DV.RUBRIEK6
                            ELSE
                                DV.RUBRIEK7
                END END AS "Limit",
                'AGG' AS "LimitBasisCode",
                'AGGREGATE LIMIT FOR PERIOD' AS "LimitBasisDescription",
                'EURO' AS "LimitCurrencyCode",
                'Y' AS "TopLimit"
            FROM
                PUB.DEKKING D
                INNER JOIN PUB.POLISVERSIE P
                ON P.INTERNPOLISNUMMER = D.INTERNPOLISNUMMER
                LEFT OUTER JOIN PUB.DEKKINGVRIJ DV
                ON D.INTERNPOLISNUMMER = DV.INTERNPOLISNUMMER
                AND D.DEKKINGSNUMMER = DV.DEKKINGSNUMMER UNION ALL
 --per claim limit, historic policy version------------
                SELECT
                    DISTINCT P.IDPOLIS AS "KeyIdPolis",
                    D.DEKKINGSNUMMER AS "KeyDekkingsNummer",
                    CASE
                        WHEN D.DEKKINGSCODE IN (800, 820) THEN
                            DV.RUBRIEK8
                        ELSE
                            DV.RUBRIEK6
                    END AS "Limit",
                    'AOC' AS "LimitBasisCode",
                    'ANY ONE CLAIM' AS "LimitBasisDescription",
                    'EURO' AS "LimitCurrencyCode",
                    'Y' AS "TopLimit"
                FROM
                    PUB.HISTDEKKING D
                    INNER JOIN PUB.HISTPOLISVERSIE P
                    ON P.IDPOLIS = D.IDPOLIS
                    LEFT OUTER JOIN PUB.HISTDEKKINGVRIJ DV
                    ON D.IDPOLIS = DV.IDPOLIS
                    AND D.DEKKINGSNUMMER = DV.DEKKINGSNUMMER
                WHERE
                    P.INTERNSCHADENUMMER = 0 UNION ALL
 --aggregate limit, historic policy version------------
                    SELECT
                        DISTINCT P.IDPOLIS AS "KeyIdPolis",
                        D.DEKKINGSNUMMER AS "KeyDekkingsNummer",
                        CASE
                            WHEN D.DEKKINGSCODE IN (800, 820) THEN
                                DV.RUBRIEK8
                            ELSE
                                CASE
                                    WHEN DV.RUBRIEK7 IS NULL THEN
                                        DV.RUBRIEK6
                                    WHEN DV.RUBRIEK7 = 0 THEN
                                        DV.RUBRIEK6
                                    ELSE
                                        DV.RUBRIEK7
                        END END AS "Limit",
                        'AGG' AS "LimitBasisCode",
                        'AGGREGATE LIMIT FOR PERIOD' AS "LimitBasisDescription",
                        'EURO' AS "LimitCurrencyCode",
                        'Y' AS "TopLimit"
                    FROM
                        PUB.HISTDEKKING D
                        INNER JOIN PUB.HISTPOLISVERSIE P
                        ON P.IDPOLIS = D.IDPOLIS
                        LEFT OUTER JOIN PUB.HISTDEKKINGVRIJ DV
                        ON D.IDPOLIS = DV.IDPOLIS
                        AND D.DEKKINGSNUMMER = DV.DEKKINGSNUMMER
                    WHERE
                        P.INTERNSCHADENUMMER = 0
    ) AS T1