"""
The logic for Proposal 1 is as follows:
    * For Claims with Paid and Outstanding Reserve transactions in one Section only, there is no issue. It is
   straightforward to select the Section Number and Section Code from the Claims data.
    * At the time of the validation exercise, there were 115 claims with Paid and Outstanding Reserve transactions in more
   than one Section. This lead to duplicate records being created. The logic to resolve this is now as follows:
    * If the Total Incurred value on the claim across all sections is zero, then take the smallest Section Number.
    * If the Total Incurred value on the claim across all sections is not zero AND there are Sections with the same
    Incurred value (e.g. opening reserve of EUR1), take the smallest Section Number.
    * If the Total Incurred value on the claim across all sections is not zero, then take the Section where the Incurred
     value is highest.

The code below has a number of helper functions to apply the logic, namely:
 | Function              | Purpose
       | | --------------------- |  --------------------- | |
       <b>new_base_query</b> | The amended SQL Claims Section message for the Netherlands. | |
       <b>duplicate_reference</b> | Returns the claim references with more than one
       Section.
       | | <b>amts_by_section</b> | Returns the number of payments, outstanding reserve movemens,
       as well as the Paid, Outstanding and Incurred Amounts by Section.
       | | <b>amts_by_claim</b> | Returns the Total
       Paid, Outstanding and Incurred Amounts by Claim.
       | | <b>count_sections_same_incurred</b> | Returns the claim
       references where there is more than one section with the same incurred amount. Used to identify claims with
       the same opening reserve on multiple sections for example.
       | | <b>min_section_number_zero_incurred</b> |
       Returns the minimum section number on Claims where the Incurred is zero.
       | | <b>section_number_max_incurred</b> | Returns the section number with the highest Incurred amount.  |

The final SQL query is at the end of the code block, with the logic for SectionNumber being applied in this CASE WHEN statement.

```
  CASE WHEN min_section_number_zero_incurred.InternalClaimsNumber IS NULL
    THEN CASE WHEN count_sections_same_incurred.InternalClaimsNumber IS NULL
      THEN section_number_max_incurred.SectionNumber
      ELSE count_sections_same_incurred.Min_SectionNumber
      END
    ELSE min_section_number_zero_incurred.MinSectionNumber
  END AS SectionNumber
```
"""

import pyspark.sql.functions as F
from pyspark.sql import DataFrame, Window

from models_scripts.transformations.common.traits import (
    RecordsMarker,
    business_logic,
    mark_records,
    transformation,
)


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    claims_df = df_dict["SupportNL_Claims"]
    hist_policy_version_df = df_dict["SupportNL_HistPolicyVersion"]
    hist_section_df = df_dict["SupportNL_HistSection"]
    claims_payments_df = df_dict["SupportNL_ClaimPayments"]
    claim_reserves_df = df_dict["SupportNL_ClaimReserves"]

    base_df = new_base_query(
        claims_df,
        claims_payments_df,
        hist_policy_version_df,
        hist_section_df,
        claim_reserves_df,
    )
    duplicated_reference_df = duplicated_reference(base_df)
    amts_by_section_df = calculate_amounts_by_section(
        claims_df, claims_payments_df, claim_reserves_df
    )
    amts_by_claims_df = calculate_amounts_by_claim(
        claims_df, claims_payments_df, claim_reserves_df
    )
    sections_same_incurred_df = count_sections_same_incurred(amts_by_section_df)
    min_section_number_df = min_section_number_zero_incurred(
        amts_by_section_df, amts_by_claims_df
    )
    max_section_incurred_df = section_number_max_incurred(
        amts_by_section_df, amts_by_claims_df
    )
    output_df = main_dedup_logic(
        base_df,
        duplicated_reference_df,
        min_section_number_df,
        max_section_incurred_df,
        sections_same_incurred_df,
        amts_by_section_df,
    )

    return output_df


@transformation
def new_base_query(
    claims_df: DataFrame,
    claims_payments_df: DataFrame,
    hist_policy_version_df: DataFrame,
    hist_section_df: DataFrame,
    claim_reserves_df: DataFrame,
) -> DataFrame:
    """Create base query to get Claims data."""

    # Preparing distinct entries for payments and reserves DataFrames
    distinct_payments_df = claims_payments_df.select(
        "CompanyInternalClaimNumber", "SectionCode", "SectionNumber"
    ).distinct()
    distinct_reserves_df = claim_reserves_df.select(
        "CompanyInternalClaimNumber", "SectionCode", "SectionNumber"
    ).distinct()

    # Prepare the minimal section of claims
    min_hist_section_df = hist_section_df.select(
        "idpolis",
        "SectionCode",
        F.min("SectionNumber")
        .over(Window.partitionBy("idpolis"))
        .alias("Min_SectionNumber"),
        F.min("SectionCode")
        .over(Window.partitionBy("idpolis"))
        .alias("Min_SectionCode"),
    ).filter(F.col("SectionNumber") == F.col("Min_SectionNumber"))

    # Join operations
    joined_df = (
        claims_df.alias("s")
        .join(hist_policy_version_df.alias("h"), "CompanyInternalClaimNumber", "inner")
        .join(hist_section_df.alias("hd"), "idpolis", "inner")
        .join(
            distinct_payments_df.alias("sb"), "CompanyInternalClaimNumber", "left_outer"
        )
        .join(
            distinct_reserves_df.alias("sr"), "CompanyInternalClaimNumber", "left_outer"
        )
        .join(min_hist_section_df.alias("minHD"), "idpolis", "left_outer")
    )

    # Column transformations with better organization
    key_section_number_col = F.trim(
        F.coalesce("sb.SectionNumber", "sr.SectionNumber", "minHD.Min_SectionNumber")
    ).cast("string")

    product_code_trimmed_col = F.trim(F.col("s.ProductCode").cast("string"))
    section_product_code_col = F.concat(
        product_code_trimmed_col,
        F.trim(
            F.coalesce("sb.SectionCode", "sr.SectionCode", "minHD.SectionCode")
        ).cast("string"),
    )

    section_reference_col = F.concat(key_section_number_col, section_product_code_col)

    # Select with simplified column variables
    result_df = joined_df.select(
        F.col("s.internalclaimsnumber").alias("KeyInternSchadenummer"),
        F.col("h.idpolis").alias("KeyIdPolis"),
        key_section_number_col.alias("KeySectionNumber"),
        product_code_trimmed_col.alias("ProductCode"),
        section_product_code_col.alias("SectionProductCode"),
        section_reference_col.alias("SectionReference"),
    ).distinct()

    return result_df


@transformation
def duplicated_reference(new_base_query_df: DataFrame) -> DataFrame:
    return (
        new_base_query_df.groupBy("KeyInternSchadenummer")
        .agg(F.count("KeyIdPolis").alias("DuplicateCount"))
        .filter(F.col("DuplicateCount") > 1)
    )


@transformation
def calculate_amounts_by_section(
    claims_df: DataFrame, claims_payments_df: DataFrame, claim_reserves_df: DataFrame
) -> DataFrame:
    # Aggregate claim payments
    payments_agg_df = claims_payments_df.groupBy(
        "InternalClaimsNumber", "SectionNumber", "SectionCode"
    ).agg(
        F.count("SectionNumber").alias("CountPayments"),
        F.lit(0).alias("CountReserveMovements"),
        F.sum("Amount").alias("Paid"),
        F.lit(0).alias("OS"),
    )

    # Aggregate claim reserves
    reserves_agg_df = (
        claim_reserves_df.withColumn(
            "InternalClaimsNumber",
            F.expr("CAST(SUBSTRING_INDEX(CompanyInternalClaimNumber, '/', -1) AS INT)"),
        )
        .groupBy("InternalClaimsNumber", "SectionNumber", "SectionCode")
        .agg(
            F.lit(0).alias("CountPayments"),
            F.count("SectionNumber").alias("CountReserveMovements"),
            F.lit(0).alias("Paid"),
            F.sum("Amount").alias("OS"),
        )
    )

    # Combine the results of payments and reserves
    combined_df = payments_agg_df.unionByName(reserves_agg_df)

    # Join combined results back with claims data
    amts_by_section_df = (
        claims_df.alias("clm")
        .join(combined_df.alias("clm_amt"), "InternalClaimsNumber", "left_outer")
        .groupBy(
            "InternalClaimsNumber", "ClaimsNumberOffice", "SectionNumber", "SectionCode"
        )
        .agg(
            F.sum("CountPayments").alias("CountPayments"),
            F.sum("CountReserveMovements").alias("CountReserveMovements"),
            F.sum("Paid").alias("Paid_by_Section"),
            F.sum("OS").alias("Outstanding_by_Section"),
            (F.sum("Paid") + F.sum("OS")).alias("Incurred_by_Section"),
        )
    )

    return amts_by_section_df


@transformation
def calculate_amounts_by_claim(
    claims_df: DataFrame, claims_payments_df: DataFrame, claim_reserves_df: DataFrame
) -> DataFrame:
    # Aggregate claim payments
    payments_agg_df = claims_payments_df.groupBy("InternalClaimsNumber").agg(
        F.sum("Amount").alias("Paid"), F.lit(0).alias("OS")
    )

    # Aggregate claim reserves
    reserves_agg_df = (
        claim_reserves_df.withColumn(
            "InternalClaimsNumber",
            F.expr("CAST(SUBSTRING_INDEX(CompanyInternalClaimNumber, '/', -1) AS INT)"),
        )
        .groupBy("InternalClaimsNumber")
        .agg(F.lit(0).alias("Paid"), F.sum("Amount").alias("OS"))
    )

    # Combine the results of payments and reserves
    combined_df = payments_agg_df.unionByName(reserves_agg_df)

    # Join combined results back with claims data
    claims_totals_df = (
        claims_df.alias("clm")
        .join(combined_df.alias("clm_amt"), "InternalClaimsNumber", "left_outer")
        .groupBy("InternalClaimsNumber", "ClaimsNumberOffice")
        .agg(
            F.sum("Paid").alias("TotalPaid"),
            F.sum("OS").alias("TotalOutstanding"),
            (F.sum("Paid") + F.sum("OS")).alias("TotalIncurred"),
        )
    )

    return claims_totals_df


@transformation
def count_sections_same_incurred(amts_by_section_df: DataFrame) -> DataFrame:
    # Aggregating data
    aggregated_df = amts_by_section_df.groupBy("InternalClaimsNumber").agg(
        F.min("SectionNumber").alias("Min_SectionNumber"),
        F.count("Incurred_by_Section").alias("count_sections"),
        F.sum("Incurred_by_Section").alias("Total_Incurred"),
        F.countDistinct("Incurred_by_Section").alias("distinct_incurred_count"),
        F.count("SectionNumber").alias("section_count"),
    )

    # Applying the HAVING clause
    filtered_df = aggregated_df.filter(
        (F.col("distinct_incurred_count") == 1) & (F.col("section_count") > 1)
    ).select(
        "InternalClaimsNumber", "Min_SectionNumber", "count_sections", "Total_Incurred"
    )

    # Ordering by InternalClaimsNumber descending
    result_df = filtered_df.orderBy(F.col("InternalClaimsNumber").desc())

    return result_df


@transformation
def min_section_number_zero_incurred(
    amts_by_section_df: DataFrame, amts_by_claims_df: DataFrame
) -> DataFrame:
    """Find the minimum SectionNumber for each InternalClaimsNumber where TotalIncurred is 0."""

    # Join amts_by_section and amts_by_claim on ClaimsNumberOffice
    joined_df = (
        amts_by_section_df.join(
            amts_by_claims_df,
            amts_by_section_df["ClaimsNumberOffice"]
            == amts_by_claims_df["ClaimsNumberOffice"],
            "left_outer",
        )
        .select(
            amts_by_section_df["*"],
            amts_by_claims_df["TotalPaid"],
            amts_by_claims_df["TotalOutstanding"],
            amts_by_claims_df["TotalIncurred"],
        )
        .filter(amts_by_claims_df["TotalIncurred"] == 0)
    )

    # Group by InternalClaimsNumber and ClaimsNumberOffice, and find the minimum SectionNumber
    result_df = joined_df.groupBy("InternalClaimsNumber", "ClaimsNumberOffice").agg(
        F.min("SectionNumber").alias("MinSectionNumber")
    )

    return result_df


@transformation
def section_number_max_incurred(
    amts_by_section_df: DataFrame, amts_by_claims_df: DataFrame
) -> DataFrame:
    # Step 1: Join amts_by_section and amts_by_claim on ClaimsNumberOffice and filter TotalIncurred != 0
    joined_df = (
        amts_by_section_df.join(
            amts_by_claims_df,
            amts_by_section_df["ClaimsNumberOffice"]
            == amts_by_claims_df["ClaimsNumberOffice"],
            "left_outer",
        )
        .select(
            amts_by_section_df["*"],
            amts_by_claims_df["TotalPaid"],
            amts_by_claims_df["TotalOutstanding"],
            amts_by_claims_df["TotalIncurred"],
        )
        .filter(amts_by_claims_df["TotalIncurred"] != 0)
    )

    # Step 2: Aggregate to find MAX Incurred_by_Section for each InternalClaimsNumber and ClaimsNumberOffice
    max_incurred_df = joined_df.groupBy(
        "InternalClaimsNumber", "ClaimsNumberOffice"
    ).agg(F.max("Incurred_by_Section").alias("MaxIncurred_bySection"))

    # Step 3: Join back with amts_by_section to get the SectionNumber
    result_df = (
        max_incurred_df.alias("T2")
        .join(
            amts_by_section_df.alias("amts_by_section"),
            (
                F.col("T2.ClaimsNumberOffice")
                == F.col("amts_by_section.ClaimsNumberOffice")
            )
            & (
                F.abs(
                    F.col("T2.MaxIncurred_bySection")
                    - F.col("amts_by_section.Incurred_by_Section")
                )
                < 0.0001
            ),
            "left_outer",
        )
        .select("T2.*", "amts_by_section.SectionNumber")
    )

    return result_df


@transformation
@mark_records
def main_dedup_logic(
    new_base_query_df: DataFrame,
    duplicate_reference_df: DataFrame,
    min_section_number_zero_incurred_df: DataFrame,
    section_number_max_incurred_df: DataFrame,
    count_sections_same_incurred_df: DataFrame,
    amts_by_section_df: DataFrame,
    records_marker: RecordsMarker,
) -> DataFrame:
    # Step 1: Creating F1 DataFrame with CASE logic, the logic is explained in the docstring of the script.
    f1_df = (
        duplicate_reference_df.alias("duplicate_reference")
        .join(
            min_section_number_zero_incurred_df.alias(
                "min_section_number_zero_incurred"
            ),
            F.col("duplicate_reference.KeyInternSchadenummer")
            == F.col("min_section_number_zero_incurred.InternalClaimsNumber"),
            "left_outer",
        )
        .join(
            section_number_max_incurred_df.alias("section_number_max_incurred"),
            F.col("duplicate_reference.KeyInternSchadenummer")
            == F.col("section_number_max_incurred.InternalClaimsNumber"),
            "left_outer",
        )
        .join(
            count_sections_same_incurred_df.alias("count_sections_same_incurred"),
            F.col("duplicate_reference.KeyInternSchadenummer")
            == F.col("count_sections_same_incurred.InternalClaimsNumber"),
            "left_outer",
        )
        .select(
            F.col("duplicate_reference.KeyInternSchadenummer"),
            F.when(
                F.col("min_section_number_zero_incurred.InternalClaimsNumber").isNull(),
                F.when(
                    F.col("count_sections_same_incurred.InternalClaimsNumber").isNull(),
                    F.col("section_number_max_incurred.SectionNumber"),
                ).otherwise(F.col("count_sections_same_incurred.Min_SectionNumber")),
            )
            .otherwise(F.col("min_section_number_zero_incurred.MinSectionNumber"))
            .alias("SectionNumber"),
        )
    )

    # Step 2: Joining F1 with amts_by_section to create F2 step, the step to get the SectionCode
    f2_df = (
        f1_df.alias("F1")
        .join(
            amts_by_section_df.alias("amts_by_section"),
            (
                F.col("F1.KeyInternSchadenummer")
                == F.col("amts_by_section.InternalClaimsNumber")
            )
            & (F.col("F1.SectionNumber") == F.col("amts_by_section.SectionNumber")),
            "left_outer",
        )
        .select(
            F.col("F1.KeyInternSchadenummer"),
            F.col("F1.SectionNumber"),
            F.col("amts_by_section.SectionCode"),
        )
    )

    # Step 3: Joining new_base_query with duplicate_reference and F2
    joined_df = (
        new_base_query_df.alias("new_base_query")
        .join(
            duplicate_reference_df.alias("duplicate_reference"),
            F.col("new_base_query.KeyInternSchadenummer")
            == F.col("duplicate_reference.KeyInternSchadenummer"),
            "left_outer",
        )
        .join(
            f2_df.alias("F2"),
            F.col("new_base_query.KeyInternSchadenummer")
            == F.col("F2.KeyInternSchadenummer"),
            "left_outer",
        )
        .filter(F.col("duplicate_reference.KeyInternSchadenummer").isNotNull())
        .selectExpr(
            "new_base_query.KeyInternSchadenummer",
            "new_base_query.KeyIdPolis",
            "F2.SectionNumber AS KeyDekkingsNummer",
            "CONCAT(new_base_query.ProductCode, F2.SectionCode) AS SectionProductCode",
            "CONCAT(F2.SectionNumber, new_base_query.ProductCode, F2.SectionCode) AS SectionReference",
        )
        .distinct()
    )

    # Step 4: Handling the UNION ALL part of the SQL
    non_duplicated_df = (
        new_base_query_df.alias("new_base_query")
        .join(
            duplicate_reference_df.alias("duplicate_reference"),
            F.col("new_base_query.KeyInternSchadenummer")
            == F.col("duplicate_reference.KeyInternSchadenummer"),
            "left_anti",
        )
        .selectExpr(
            "new_base_query.KeyInternSchadenummer",
            "new_base_query.KeyIdPolis",
            "new_base_query.KeySectionNumber AS KeyDekkingsNummer",
            "new_base_query.SectionProductCode",
            "new_base_query.SectionReference",
        )
    )

    # Step 5: Union the two DataFrames
    is_new_logic_col = "SufferedNewLogic"
    joined_df = joined_df.withColumn(is_new_logic_col, F.lit(True))
    non_duplicated_df = non_duplicated_df.withColumn(is_new_logic_col, F.lit(False))

    final_df = joined_df.unionByName(non_duplicated_df)
    final_df = records_marker.mark_records(final_df, final_df[is_new_logic_col]).drop(
        is_new_logic_col
    )

    return final_df
