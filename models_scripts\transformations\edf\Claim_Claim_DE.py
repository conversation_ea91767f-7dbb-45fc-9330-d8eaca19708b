from pyspark.sql import DataFrame

from models_scripts.transformations.common.add_columns import (
    add_column_from_match_table,
)
from models_scripts.transformations.common.misc import (
    COUNTRY_COLUMN_NAME,
    TEMP_COUNTRY_COLUMN,
)
from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.add_attritional_large_flag import (
    AttritionalLargeFlagCalculator,
)


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    claim_df = df_dict["Claim_main"]
    claim_policy_df = df_dict["Claim_Policy"]
    claim_section_df = df_dict["Claim_Section"]
    claim_transaction_df = df_dict["Claim_Transaction"]
    claim_transactioncomponent_df = df_dict["Claim_TransactionComponent"]
    policy_section_df = df_dict["Policy_Section"]
    reservingclass_df = df_dict["Reference_ReservingClasses"]

    enhanced_claim_df = add_column_from_match_table(
        claim_df,
        claim_policy_df,
        "KeyInternSchadenummer",
        {
            COUNTRY_COLUMN_NAME: TEMP_COUNTRY_COLUMN,
            "PolicyYearOfAccount": "PolicyYearOfAccount",
        },
    )

    output_df = AttritionalLargeFlagCalculator(
        enhanced_claim_df,
        claim_section_df,
        claim_transaction_df,
        claim_transactioncomponent_df,
        policy_section_df,
        reservingclass_df,
        partition="DE",
    ).add_flag()

    return output_df
