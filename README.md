# Introduction

MINT Etl Solution (MINES), is a flexible and robust solution for populate the DataLake.

# Getting Started

Check de DevOps Wiki page for more information:

https://dev.azure.com/markelcorp/European%20Data%20Lake/_wiki/wikis/European-Data-Lake.wiki/1769/MINES-(MINt-Etl-Solution)

# Setup Steps

# If you are using the devcontainer setup, you can follow the steps below:

1. Install Devpod, Docker, and JetBrains Gateway.

2. Ensure the existence of the ~/.ssh folder by generating an RSA key.

   1. Run ( Run ssh-keygen -t rsa -b 4096 -C "YOUR MARKEL EMAIL @markel.com"  )
   2. Run ( eval "$(ssh-agent -s)" )
   3. Run ( ssh-add ~/.ssh/id_rsa )

3.  RUN  “git config --global credential.helper cache”

4. RUN  “git config --global credential.helper store”

5. Upload the public key to https://dev.azure.com/markelcorp/_usersSettings/keys.

7. GET SAS tokens in Microsoft Azure Storage Explore

8. Populate ~/.bashrc and ~/.zshrc with the SAS_TOKEN_MODELS and SAS_TOKEN_SCRIPTS and (eval "keychain --eval id_rsa”).
   {EXAMPLE}
    export SAS_TOKEN_MODELS=your_sas_token_for_models
    export SAS_TOKEN_SCRIPTS=your_sas_token_for_scripts
    eval "$(keychain --eval id_rsa)"

7. GET host, token and cluster_id in Databricks

8. Populate ~/.databrickscfg as bellow:
    {EXAMPLE}
       host = your_databricks_host
       token = your_databricks_token
       cluster_id = your_cluster_id


# If you are using not using the devcontainer setup, you can follow the steps below:
1. Install Python and Git
1. Install [poetry](https://python-poetry.org/docs/#installing-with-the-official-installer)
1. Install Azure CLI: `curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash`
1. Install [Databricks CLI](https://docs.databricks.com/en/dev-tools/cli/install.html) extension and configure:
    1. Run `curl -fsSL https://raw.githubusercontent.com/databricks/setup-cli/main/install.sh | sudo sh`
    1. Run `databricks configure`
1. Clone the [Models](https://dev.azure.com/markelcorp/European%20Data%20Lake/_git/EuropeanDataLake-Models) repository.
1. Run `git submodule update --init --recursive`
1. (Optional) Run `poetry config virtualenvs.in-project true --local`
1. Run `poetry install --with dev --with tests`
1. Run `poetry shell`.
1. Run `pre-commit install`
1. Install the jq package: `sudo apt-get install jq`
1. Define variable SAS_TOKEN_MODELS: For dev_deployment script. Should be provided by the DevOps team
1. Define variable SAS_TOKEN_SCRIPTS: For dev_deployment script. Should be provided by the DevOps team

## Local Debug

Local debug is possible using the following steps:
### Configure

Documentation: https://learn.microsoft.com/en-us/azure/databricks/dev-tools/databricks-connect/python/pycharm

1. Make sure you have databricks-connect installed and configured. (is in the pyproject.toml)
2. Make sure you have the databricks cli installed and configured.
3. Make sure you have the databricks configurations on databrickscfg defined.

## Run

After configuring you will be able to run the code as usual, the spark jobs will be sent to the databricks cluster.
The DataFactory and the databricks test pipeline make available the arguments that will enable to execute the exactly
same run on local debug.

When debugging locally, the following environment variables can be set to control the execution:
1. MINES_CHECKPOINT_RULES_DATAFRAMES: If exist and equal to 'True' the single partition will checkpoint after each rule
2. MINES_CHECKPOINT_TRANSFORMATIONS: If exist and equal to 'True' the single partition will checkpoint after each
call of a function decorated with @transformation decorator.
3. and after execution of any function decorated with @checkpoint_debug decorator.
4. MINES_TABLE_TO_PROCESS: If Exists should be a list of table names, like 'table1,table2,table3'. If not exists,
5. all tables will be processed.
6. MINES_SUBMODELS_TO_PROCESS: If Exists should be a list of submodel names, like 'submodel1,submodel2,submodel3'.
7. If not exists, all submodels will be processed .
8. MINES_DISABLE_MULTI_THREADING: If Exists and equal to 'True' will disable the multi threading.

A good practice would be to set the environment variable in the run configuration of the IDE,
so it will be available only when debugging.
