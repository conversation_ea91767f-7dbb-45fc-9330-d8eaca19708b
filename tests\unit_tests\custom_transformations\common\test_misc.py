import pandas as pd
from mines2.core.extensions.misc import assert_dataframe_equality

from models_scripts.transformations.common.misc import (
    enforce_nulls_type,
    filter_by_max_value,
    reformat_datetime_col,
)


def test_filter_by_max_value(spark):
    main_df = spark.createDataFrame(
        [(1, "2021-01-01"), (2, "2023-01-01"), (3, "2023-01-01")],
        ["ID", "FileNameDate_yyyyMMdd_"],
    )
    expected_df = spark.createDataFrame(
        [(2, "2023-01-01"), (3, "2023-01-01")], ["ID", "FileNameDate_yyyyMMdd_"]
    )
    output_df = filter_by_max_value(main_df, "FileNameDate_yyyyMMdd_")
    assert_dataframe_equality(output_df, expected_df)

    main_df = spark.createDataFrame([(1, 1.1), (2, 2.21), (3, 3.333)], ["ID", "value"])
    expected_df = spark.createDataFrame([(3, 3.333)], ["ID", "value"])
    output_df = filter_by_max_value(main_df, "value")
    assert_dataframe_equality(output_df, expected_df)


def test_enforce_nulls_type(spark):
    pdf = pd.DataFrame([(1, None), (2, None)], columns=["ID", "column"])
    df = spark.createDataFrame(pdf)
    output_df = enforce_nulls_type(df)
    assert output_df.schema["column"].dataType.typeName() == "string"


class TestReformatDatetimeCol:
    def test_multiple_patterns(self, spark):
        patterns = [
            "yyyy-MM-dd h:mm:ss a",
            "yyyy-MM-dd HH:mm:ss",
            "yyyy-MM-dd",
            "ddMMMyyyy",
            "d/M/yyyy",
            "dd/M/yyyy",
            "dd/MM/yyyy",
            "M/dd/yyyy",
            "MM/dd/yyyy",
            "dd-MMM-yy",
            "dd-MM-yyyyy",
        ]

        # Create a test DataFrame
        data = [
            (1, "01AUG2023 "),
            (2, " 2023-12-31"),
            (3, "2023-09-16 00:00:00"),
            (4, "2023-05-25 2:58:51 PM "),
            (5, "2023-06-13 10:21:27 AM"),
            (6, "2023|06|13"),  # Pattern not found
            (7, None),
            (8, "31/01/2023"),
            (9, "31/1/2023"),
            (10, "01/31/2023"),
            (11, "1/31/2023"),
            (12, "01/02/2023"),
            (13, "1/2/2023"),
            (14, "any_string"),
            (15, "31-JAN-23"),
            (16, "01/02/02023"),  # Pattern not found
            (17, "01-02-02023"),  # Pattern "dd-MM-yyyyy"
        ]
        df = spark.createDataFrame(data, ["ID", "date_column"])

        # Apply the reformat_datetime_col function
        output_df = df.withColumn(
            "reformatted_column", reformat_datetime_col("date_column", patterns)
        )

        # Define the expected result
        expected_data = [
            (1, "01AUG2023 ", "2023-08-01 00:00:00"),
            (2, " 2023-12-31", "2023-12-31 00:00:00"),
            (3, "2023-09-16 00:00:00", "2023-09-16 00:00:00"),
            (4, "2023-05-25 2:58:51 PM ", "2023-05-25 14:58:51"),
            (5, "2023-06-13 10:21:27 AM", "2023-06-13 10:21:27"),
            (6, "2023|06|13", "2023|06|13"),
            (7, None, None),
            (8, "31/01/2023", "2023-01-31 00:00:00"),
            (9, "31/1/2023", "2023-01-31 00:00:00"),
            (10, "01/31/2023", "2023-01-31 00:00:00"),
            (11, "1/31/2023", "2023-01-31 00:00:00"),
            (12, "01/02/2023", "2023-02-01 00:00:00"),
            (13, "1/2/2023", "2023-02-01 00:00:00"),
            (14, "any_string", "any_string"),
            (15, "31-JAN-23", "2023-01-31 00:00:00"),
            (16, "01/02/02023", "01/02/02023"),
            (17, "01-02-02023", "2023-02-01 00:00:00"),
        ]
        expected_df = spark.createDataFrame(
            expected_data, ["ID", "date_column", "reformatted_column"]
        )

        assert_dataframe_equality(output_df, expected_df)

    def test_case_dd_mm_yy_pattern(self, spark):
        patterns = ["dd/MM/yy"]
        df = spark.createDataFrame([(17, "31/01/23")], ["ID", "date_column"])
        output_df = df.withColumn(
            "reformatted_column", reformat_datetime_col("date_column", patterns)
        )
        expected_df = spark.createDataFrame(
            [(17, "31/01/23", "2023-01-31 00:00:00")],
            ["ID", "date_column", "reformatted_column"]
        )
        assert_dataframe_equality(output_df, expected_df)

    def test_case_yy_mm_dd_pattern(self, spark):
        patterns = ["yy/MM/dd"]
        df = spark.createDataFrame([(17, "23/01/31")], ["ID", "date_column"])
        output_df = df.withColumn(
            "reformatted_column", reformat_datetime_col("date_column", patterns)
        )
        expected_df = spark.createDataFrame(
            [(17, "23/01/31", "2023-01-31 00:00:00")],
            ["ID", "date_column", "reformatted_column"]
        )
        assert_dataframe_equality(output_df, expected_df)

    def test_case_yy_mixed_patterns(self, spark):
        patterns = [
            "dd/MM/yy",
            "dd/MM/yyyy",
        ]
        data = [
            (1, "30/01/23"),
            (2, "31/01/2023"),
        ]
        df = spark.createDataFrame(data, ["ID", "date_column"])

        output_df = df.withColumn(
            "reformatted_column", reformat_datetime_col("date_column", patterns)
        )
        expected_data = [
            (1, "30/01/23", "2023-01-30 00:00:00"),
            (2, "31/01/2023", "2023-01-31 00:00:00"),
        ]
        expected_df = spark.createDataFrame(
            expected_data, ["ID", "date_column", "reformatted_column"]
        )
        assert_dataframe_equality(output_df, expected_df)
