select distinct p.idpolis                                      		as "KeyIdPolis"
, d.dekkingsnummer                                                      as "KeyDekkingsNummer"
, 'CIA'        			              				as "CostBasisCode"
, 'COST IN ADDITION'  							as "CostBasisDescription"
, case when p.productcode IN (500, 505, 510, 515, 900, 905, 910, 915, 920, 930) then 'ENGINEERING' else 'LIABILITY' end as "CoverageType"
, case when ltrim(to_char(p.productcode)) = '950' then
        case when left(ltrim(to_char(d.dekkingscode)), 1) = '2' then 0
                when left(ltrim(to_char(d.dekkingscode)), 1) = '1' then 1000
                when left(ltrim(to_char(d.dekkingscode)), 1) = '3' then 2000
                else d.EigenRisico
          end
  else d.EigenRisico
  end                                                                   as "Deductible"
, ''                                                                    as "EstSignedDown"
, ''                                                                    as "IBCCoverageCode"
, case when d.poolnummer = 0 then p.maatschappijnummer else tp.maatschappijnummer end as "InsurerCarrierCode"
, case when d.poolnummer = 0 then m.naam else mp.naam end as "InsurerCarrierDescription"
, '100'                                                                 as "InsurerCarrierPercentage"
, 'NL'                                                                  as "Jurisdiction"
, ''                                                                    as "NoClaimsBonus"
, 'NL'                                                                  as "OperatingTerritory"
, ''                                                                    as "PremiumBasisCode"
, ''                                                                    as "ProfitCommission"
, d.PrijsPerEenheid                                                     as "RateOnExposure"
, p.mutatiedatum                                                        as "SectionEffectiveFromDate"
, ifnull(p.expiratiedatum, p.contractvervaldatum)                       as "SectionEffectiveToDate"
, ltrim(to_char(p.productcode)) + ltrim(to_char(d.dekkingscode))        as "SectionProductCode"
, po.omschrijving + '/' + do.omschrijving                               as "SectionProductDescription"
, d.dekkingsnummer                                                      as "SectionReference"
, ''                                                                    as "SectionStatusCode"
, ''                                                                    as "SignedLine"
, ''                                                                    as "SignedOrder"
, case when (ltrim(d.vrijveld3) <> '0' and d.vrijveld3 <> '') or (ltrim(d.vrijveld4) <> '0' and d.vrijveld4 <> '') then 'Y' else 'N' end as "SubLimitsIndicator"
, 'Defined in wordings'                                                as "TerritorialScope"
, ''                                                                    as "WrittenLine"
, ''                                                                    as "WrittenOrder"
from pub.dekking d
inner join pub.polisversie p on p.internpolisnummer = d.internpolisnummer
left outer join pub.tabelpoolelement tp on tp.poolnummer = d.poolnummer
left outer join pub.maatschappij m on m.maatschappijnummer = p.maatschappijnummer
left outer join pub.maatschappij mp on mp.maatschappijnummer = tp.maatschappijnummer
left outer join pub.tabelproduct po on po.soortverzekeringproductcode = p.soortverzekeringproductcode
left outer join pub.tabeldekking do on do.soortverzekeringdekkingscode = d.soortverzekeringdekkingscode
union all
select distinct p.idpolis                                      		as "KeyIdPolis"
, d.dekkingsnummer                                                      as "KeyDekkingsNummer"
, 'CIA'        			              				as "CostBasisCode"
, 'COST IN ADDITION'  							as "CostBasisDescription"
, case when p.productcode IN (500, 505, 510, 515, 900, 905, 910, 915, 920, 930) then 'ENGINEERING' else 'LIABILITY' end as "CoverageType"
, case when ltrim(to_char(p.productcode)) = '950' then
        case when left(ltrim(to_char(d.dekkingscode)), 1) = '2' then 0
                when left(ltrim(to_char(d.dekkingscode)), 1) = '1' then 1000
                when left(ltrim(to_char(d.dekkingscode)), 1) = '3' then 2000
                else d.EigenRisico
          end
  else d.EigenRisico
  end                                                                   as "Deductible"
, ''                                                                    as "EstSignedDown"
, ''                                                                    as "IBCCoverageCode"
, case when d.poolnummer = 0 then p.maatschappijnummer else tp.maatschappijnummer end as "InsurerCarrierCode"
, case when d.poolnummer = 0 then m.naam else mp.naam end as "InsurerCarrierDescription"
, '100'                                                                 as "InsurerCarrierPercentage"
, 'NL'                                                                  as "Jurisdiction"
, ''                                                                    as "NoClaimsBonus"
, 'NL'                                                                  as "OperatingTerritory"
, ''                                                                    as "PremiumBasisCode"
, ''                                                                    as "ProfitCommission"
, d.PrijsPerEenheid                                                     as "RateOnExposure"
, p.mutatiedatum                                                        as "SectionEffectiveFromDate"
, ifnull(p.expiratiedatum, p.contractvervaldatum)                       as "SectionEffectiveToDate"
, ltrim(to_char(p.productcode)) + ltrim(to_char(d.dekkingscode))        as "SectionProductCode"
, po.omschrijving + '/' + do.omschrijving                               as "SectionProductDescription"
, d.dekkingsnummer                                                      as "SectionReference"
, ''                                                                    as "SectionStatusCode"
, ''                                                                    as "SignedLine"
, ''                                                                    as "SignedOrder"
, case when (ltrim(d.vrijveld3) <> '0' and d.vrijveld3 <> '') or (ltrim(d.vrijveld4) <> '0' and d.vrijveld4 <> '') then 'Y' else 'N' end as "SubLimitsIndicator"
, 'Defined in wordings'                                                as "TerritorialScope"
, ''                                                                    as "WrittenLine"
, ''                                                                    as "WrittenOrder"
from pub.histdekking d
inner join pub.histpolisversie p on p.idpolis = d.idpolis
left outer join pub.tabelpoolelement tp on tp.poolnummer = d.poolnummer
left outer join pub.maatschappij m on m.maatschappijnummer = p.maatschappijnummer
left outer join pub.maatschappij mp on mp.maatschappijnummer = tp.maatschappijnummer
left outer join pub.tabelproduct po on po.soortverzekeringproductcode = p.soortverzekeringproductcode
left outer join pub.tabeldekking do on do.soortverzekeringdekkingscode = d.soortverzekeringdekkingscode
where p.internschadenummer = 0

union all

/* Add records related to policy versions with Claims, i.e. internschadenummer <> 0 in HistPolisversie*/

select distinct p.idpolis                                      		as "KeyIdPolis"
, d.dekkingsnummer                                                      as "KeyDekkingsNummer"
, 'CIA'        			              				as "CostBasisCode"
, 'COST IN ADDITION'  							as "CostBasisDescription"
, case when p.productcode IN (500, 505, 510, 515, 900, 905, 910, 915, 920, 930) then 'ENGINEERING' else 'LIABILITY' end as "CoverageType"
, case when ltrim(to_char(p.productcode)) = '950' then
        case when left(ltrim(to_char(d.dekkingscode)), 1) = '2' then 0
                when left(ltrim(to_char(d.dekkingscode)), 1) = '1' then 1000
                when left(ltrim(to_char(d.dekkingscode)), 1) = '3' then 2000
                else d.EigenRisico
          end
  else d.EigenRisico
  end                                                                   as "Deductible"
, ''                                                                    as "EstSignedDown"
, ''                                                                    as "IBCCoverageCode"
, case when d.poolnummer = 0 then p.maatschappijnummer else tp.maatschappijnummer end as "InsurerCarrierCode"
, case when d.poolnummer = 0 then m.naam else mp.naam end as "InsurerCarrierDescription"
, '100'                                                                 as "InsurerCarrierPercentage"
, 'NL'                                                                  as "Jurisdiction"
, ''                                                                    as "NoClaimsBonus"
, 'NL'                                                                  as "OperatingTerritory"
, ''                                                                    as "PremiumBasisCode"
, ''                                                                    as "ProfitCommission"
, d.PrijsPerEenheid                                                     as "RateOnExposure"
, p.mutatiedatum                                                        as "SectionEffectiveFromDate"
, ifnull(p.expiratiedatum, p.contractvervaldatum)                       as "SectionEffectiveToDate"
, ltrim(to_char(p.productcode)) + ltrim(to_char(d.dekkingscode))        as "SectionProductCode"
, po.omschrijving + '/' + do.omschrijving                               as "SectionProductDescription"
, d.dekkingsnummer                                                      as "SectionReference"
, ''                                                                    as "SectionStatusCode"
, ''                                                                    as "SignedLine"
, ''                                                                    as "SignedOrder"
, case when (ltrim(d.vrijveld3) <> '0' and d.vrijveld3 <> '') or (ltrim(d.vrijveld4) <> '0' and d.vrijveld4 <> '') then 'Y' else 'N' end as "SubLimitsIndicator"
, 'Defined in wordings'                                                as "TerritorialScope"
, ''                                                                    as "WrittenLine"
, ''                                                                    as "WrittenOrder"
from pub.histdekking d
inner join pub.histpolisversie p on p.idpolis = d.idpolis
left outer join pub.tabelpoolelement tp on tp.poolnummer = d.poolnummer
left outer join pub.maatschappij m on m.maatschappijnummer = p.maatschappijnummer
left outer join pub.maatschappij mp on mp.maatschappijnummer = tp.maatschappijnummer
left outer join pub.tabelproduct po on po.soortverzekeringproductcode = p.soortverzekeringproductcode
left outer join pub.tabeldekking do on do.soortverzekeringdekkingscode = d.soortverzekeringdekkingscode
where p.internschadenummer <> 0