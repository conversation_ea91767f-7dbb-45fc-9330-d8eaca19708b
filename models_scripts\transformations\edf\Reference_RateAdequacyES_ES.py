import re

import numpy as np
import pandas as pd
from babel.numbers import NumberFormatError, parse_decimal
from mines2.core.constants import SOURCE_SYSTEM
from pyspark.sql import DataFrame

from models_scripts.transformations.common.misc import pandas_to_spark, spark_to_pandas
from models_scripts.transformations.common.traits import business_logic


def parse_number(number: str, locale, errors="raise") -> float:
    # noinspection PyTypeChecker
    """Parse number of given locale into float.
    Examples:
    >>> parse_number('1', 'en_GB')
    1.0
    >>> parse_number('1.0', 'en_GB')
    1.0
    >>> parse_number('1,0', 'en_GB') # doctest: +IGNORE_EXCEPTION_DETAIL
    Traceback (most recent call last):
    NumberFormatError: Invalid number format
    >>> parse_number('1,0', 'en_GB', errors='ignore')
    0.0
    >>> parse_number('1,1004', 'de_DE')
    1.1004
    >>> parse_number(np.nan, 'de_DE')
    nan
    """
    if locale == "en_GB":
        expected_format_regex = r"^-?\d{1,3}(,\d{3})*(\.\d+)?$"
        thousand_sep = ","
    elif locale == "de_DE":
        expected_format_regex = r"^-?\d{1,3}(\.\d{3})*,\d+$"
        thousand_sep = "."
    else:
        raise ValueError(f"Locale {locale} not supported")

    try:
        if number is not None and (pd.isna(number) == False):
            if thousand_sep in number and not re.match(expected_format_regex, number):
                raise NumberFormatError("Invalid number format")
            parsed_number = float(parse_decimal(number, locale=locale))
        else:
            parsed_number = np.nan
    except NumberFormatError:
        if errors == "raise":
            raise
        else:
            parsed_number = 0.0
    return parsed_number


def parse_number_unkown_locale(number: str) -> float:
    # noinspection PyTypeChecker
    """Parse number of Unknown format into german or uk format.
    Examples:
    >>> parse_number_unkown_locale('1')
    1.0
    >>> parse_number_unkown_locale('1.0')
    1.0
    >>> parse_number_unkown_locale('1,0')
    1.0
    >>> parse_number_unkown_locale('1.1004') #since its clear . isn't thousand separator. Go to UK
    1.1004
    >>> parse_number_unkown_locale('1,1004')
    1.1004
    >>> parse_number_unkown_locale('100.109') #if has . the priority go to uk, if both are valid
    100.109
    >>> parse_number_unkown_locale('100,109')
    100.109
    >>> parse_number_unkown_locale('100.109,10')
    100109.1
    >>> parse_number_unkown_locale('100.109,10')
    100109.1
    >>> parse_number_unkown_locale('100.109,100.10') #should be none since no valid parse on both locale
    nan
    >>> parse_number_unkown_locale(np.nan)
    nan
    >>> parse_number_unkown_locale(None)
    nan
    """

    # get possible formats
    try:
        parsed_uk_format = parse_number(number, locale="en_GB", errors="raise")
    except NumberFormatError:
        parsed_uk_format = None
    try:
        parsed_german_format = parse_number(number, locale="de_DE", errors="raise")
    except NumberFormatError:
        parsed_german_format = None

    if pd.isna(parsed_uk_format) and pd.isna(parsed_german_format):
        parsed_number = np.nan
    elif (
        parsed_uk_format and parsed_german_format  # type: ignore
    ):  # both formats are valid # type: ignore
        if parsed_uk_format == parsed_german_format:
            parsed_number = parsed_uk_format  # both formats are the same value
        elif (
            "." in number
        ):  # First untie rule: if the number has a dot, it is in UK format
            parsed_number = parsed_uk_format
        else:  # default untie rule: Give priority to German format
            parsed_number = parsed_german_format
    elif parsed_uk_format:  # only UK format is valid # type: ignore
        parsed_number = parsed_uk_format
    elif parsed_german_format:  #    only German format is valid
        parsed_number = parsed_german_format
    else:
        parsed_number = np.nan  #    no valid format

    return parsed_number  # type: ignore


def deduplicate_records(df: pd.DataFrame) -> pd.DataFrame:
    # Calculate how many NAs by line
    latest_df = (
        df.groupby("PolicyID")
        .apply(lambda x: x.sort_values(["ID_DPOLSCON", "ID_DPOLIZAS"]).tail(1))
        .reset_index(drop=True)
    )
    return latest_df


def apply_business_rules(df: pd.DataFrame) -> pd.DataFrame:
    df["FACTOCOM"] = df["FACTOCOM"].fillna(0)
    df["FACTOTEC"] = df["FACTOTEC"].fillna(0)
    df["SGREDSTE"] = df["SGREDSTE"].fillna("none")
    df["SGNRECDS"] = df["SGNRECDS"].fillna("none")
    df["adj_tec"] = np.where(df["SGREDSTE"] == "-", -df["FACTOTEC"], df["FACTOTEC"])
    df["adj_com"] = np.where(df["SGNRECDS"] == "-", -df["FACTOCOM"], df["FACTOCOM"])

    return df


def parse_numeric_cols(df: pd.DataFrame, columns: list, parse_function) -> pd.DataFrame:
    for col in columns:
        df[col] = df[col].apply(parse_function)
        df[col] = pd.to_numeric(df[col], errors="raise")
    return df


def process_spain_rate_adequacy(df_spain_lim_adj: pd.DataFrame) -> pd.DataFrame:
    # pivot the table
    #  logger.info("Processing Base Rate Adequacy.")
    df = pd.pivot_table(
        df_spain_lim_adj,
        index=["ID_DPOLIZAS", "POL_VERSION", "ID_DPOLSCON", SOURCE_SYSTEM],
        columns="NOMBDATO",  # type: ignore
        values="VALOR",
        aggfunc="first",  # type: ignore
    ).reset_index()
    df["PolicyID"] = "ES:" + df["POL_VERSION"]

    # Convert values to numeric
    ensured_only_german_locale = ["IMPOFRAN", "LIMIPOLI"]
    multiple_locale_columns = ["FACTOCOM", "FACTOTEC", "LIMISINI"]

    df = parse_numeric_cols(
        df, ensured_only_german_locale, lambda x: parse_number(x, locale="de_DE")
    )
    df = parse_numeric_cols(df, multiple_locale_columns, parse_number_unkown_locale)
    df = deduplicate_records(df)
    df = apply_business_rules(df)

    return df


def add_prima_and_brk(
    df: pd.DataFrame, df_policy_component: pd.DataFrame
) -> pd.DataFrame:
    """Add prima( premium) and brk (brokerage) columns to the dataframe, this information comes from the EDF Policy.TransactionComponent,
    using the following business logic:
    PRIMA = SUM('Policy Trans Component'[TransactionComponentAmount]) WHERE 'Policy Trans Component'[TransactionComponentTypeCode] = ' PRIMA'
    BRK= SUM('Policy Trans Component'[TransactionComponentAmount]) WHERE 'Policy Trans Component'[TransactionComponentTypeCode] IN { "COM_COMPA", "COM_MEDI"}
    """

    # Filter only the rows with the right TransactionComponentTypeCode
    # logger.info("Add prima and brk info.")
    df_policy_component_prima = df_policy_component[
        df_policy_component["TransactionComponentTypeCode"].str.upper() == "PRIMA"
    ]
    df_policy_component_brk = df_policy_component[
        df_policy_component["TransactionComponentTypeCode"]
        .str.upper()
        .isin(["COM_COMPA", "COM_MEDI"])
    ]

    # Sum the TransactionComponentAmount
    df_policy_component_prima = (
        df_policy_component_prima.groupby("PolicyID")
        .agg({"TransactionComponentAmount": "sum"})
        .reset_index()
    )
    df_policy_component_brk = (
        df_policy_component_brk.groupby("PolicyID")
        .agg({"TransactionComponentAmount": "sum"})
        .reset_index()
    )

    # Merge the dataframes
    df = df.merge(
        df_policy_component_prima,
        how="left",
        on="PolicyID",
        suffixes=("", "_prima"),
    )
    df = df.merge(
        df_policy_component_brk,
        how="left",
        on="PolicyID",
        suffixes=("", "_brk"),
    )

    # Rename the columns
    df.rename(
        columns={
            "TransactionComponentAmount": "prima",
            "TransactionComponentAmount_brk": "brk",
        },
        inplace=True,
    )

    return df


def calc_tech_ratio(df: pd.DataFrame) -> pd.DataFrame:
    """Select the columns to be included in the final dataframe. Columns definitions:
    adj_tec: Already in table.
    adj_com: Already in table.
    tec_prem: Technical Premium, net of commission = (PRIMA-BRK)/(1+adj_tec/100)
    com_prem: (PRIMA-BRK)/(1+adj_com/100)
    adj_total: adj_tec + adj_com
    final_tech_prem: (PRIMA-BRK)/(1+adj_total/100)
    final_net_prem: (PRIMA-BRK)
    technical_ratio (tech_ratio): 100 * (final_net_prem / final_tech_prem).
    """
    # logger.info("Calculating Technical ratio.")
    df["adj_total"] = df["adj_tec"] + df["adj_com"]
    df = df.assign(
        **{
            "tec_prem": (df["prima"] - df["brk"]) / (1 + df["adj_tec"] / 100),
            "com_prem": (df["prima"] - df["brk"]) / (1 + df["adj_com"] / 100),
            "final_tech_prem": (df["prima"] - df["brk"]) / (1 + df["adj_total"] / 100),
            "final_net_prem": (df["prima"] - df["brk"]),
        }
    )
    df = df.assign(tech_ratio=100 * (df["final_net_prem"] / df["final_tech_prem"]))
    return df


def select_columns(df: pd.DataFrame) -> pd.DataFrame:
    # logger.info("Selecting Final columns.")
    return df[
        [
            "PolicyID",
            "adj_tec",
            "adj_com",
            "tec_prem",
            "com_prem",
            "final_tech_prem",
            "final_net_prem",
            "adj_total",
            "tech_ratio",
            SOURCE_SYSTEM,
        ]
    ]


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    """Custom transformation for the RateAdequacyES_main.parquet file steps:
    1. Pivot the table
    2. Convert values to numeric
    3. Deduplicate records
    4. Apply business rules to calculate adj_tec and adj_com
    5. Add prima and brk columns
    6. Calculate rate adequacy.
    """
    # logger.info("Read input data")

    main_pdf = spark_to_pandas(df_dict["RateAdequacyES_main"])
    policy_component_pdf = spark_to_pandas(df_dict["Policy_TransactionComponent"])

    assert len(main_pdf) > 0, "Dataframe main shouldn't be empty!"
    assert (
        len(policy_component_pdf) > 0
    ), "Dataframe policy_component shouldn't be empty!"
    main_pdf = process_spain_rate_adequacy(main_pdf)
    main_pdf = add_prima_and_brk(main_pdf, policy_component_pdf)
    main_pdf = calc_tech_ratio(main_pdf)
    main_pdf = select_columns(main_pdf)

    output_df = pandas_to_spark(main_pdf)

    return output_df
