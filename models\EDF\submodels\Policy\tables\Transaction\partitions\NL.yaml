Country: NL
existCustomTransformation: 'True'

dataSource:
- name: main
  type: SourceProgressNL
  parameters:
    sqlFileName: EDF PolicyArrayTransaction Full.sql
    querySourceType: SQL_FILE

- name: extra_records
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Support
    Table: NewTransactionComponents

- name: dates_gaap
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Reference
    Table: DatesGAAP
    Partitions:
      - UN

- name: policy
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Policy
    Table: Policy

ColumnSpecs:
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  PolicySectionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        sep: ':'
  PolicyTransactionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        - source: COLUMN
          name: KeyFactuurnummer
        sep: ':'
  KeyIdPolis:
    locale: en_US.utf8
  KeyDekkingsNummer:
    locale: en_US.utf8
  KeyFactuurnummer:
    locale: en_US.utf8
  OriginalCurrencyCode:
    locale: en_US.utf8
  RateOfExchange:
    locale: en_US.utf8
  SettlementCurrencyCode:
    locale: en_US.utf8
  TransactionDate:
    dateTimeFormat: ISO
    locale: en_US.utf8
  TransactionReference:
    locale: en_US.utf8
  TransactionTypeCode:
    locale: en_US.utf8
  TransactionTypeDescription:
    locale: en_US.utf8
  TransactionSequenceNumber:
    locale: en_US.utf8
  USGAAP_Date:
    NotInSource: True
