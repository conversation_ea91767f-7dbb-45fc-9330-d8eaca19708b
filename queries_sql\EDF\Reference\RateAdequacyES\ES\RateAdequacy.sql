SELECT
    DISTINCT A.ID_DPOLIZAS,
    A.ID_DPOLSCON_FK,
    A.POLIZTRA,
    B.DPODATOS_SCON,
    B.ID_DPOLSCON,
    CAST(A.PRODUCTO AS VARCHAR)+' '+ CAST(A.<PERSON><PERSON><PERSON><PERSON>ANN AS VARCHAR)+'/'+ CAST(A.<PERSON>OLIZSEC AS VARCHAR) + '_' + CAST(C.NCICLORE+1 AS VARCHAR) POL_VERSION,
    L.NUMEDATO,
    L.<PERSON>LOR,
    L.NOMBDATO
FROM
    [NTJDATMRK].DBO.DPOLIZAS A
    LEFT JOIN [NTJDATMRK].DBO.DPOLSCON B
    ON A.ID_DPOLIZAS = B.ID_DPOLIZAS_FK
    LEFT JOIN [NTJDATMRK].DBO.DPOLPER C
    ON B.DPOLPER_SCON = C.ID_DPOLSCON_FK
    LEFT JOIN (
        SELECT
            [ID_DPODATOS],
            [ID_DPOLSCON_FK],
            [ID_DTODICCIO_FK],
            [NUMEDATO],
            [VALOR],
            [NOMBDATO]
        FROM
            [NTJDATMRK].[DBO].[DPODATOS]
        WHERE
            NOMBDATO IN ('LIMISINI',
            'LIMIPOLI',
            'IMPOFRAN',
            'FACTOCOM',
            'SGREDSTE',
            'SGNRECDS',
            'FACTOTEC')
    ) L
    ON L.ID_DPOLSCON_FK=B.DPODATOS_SCON --and l.ID_DPOLSCON_FK=b.ID_DPOLSCON
 --and cast(A.PRODUCTO as varchar)+' '+ cast(A.POLIZANN as varchar)+'/'+ cast(a.POLIZSEC as varchar) + '_' + cast(C.NCICLORE+1 as varchar)='RC01 2018/100_5'
ORDER BY
    ID_DPOLIZAS,
    POL_VERSION