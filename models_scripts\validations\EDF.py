from datetime import datetime
from typing import Dict, List

import mines2.core.base as base
import mines2.core.constants as const
import pyspark.sql.functions as F
from mines2.core.extensions.io import TableInfo, read_dataframe
from pyspark.sql import DataFrame

MAX_VARIATIONS = {
    "Policy": {"UP": 0.1, "DOWN": 0.05},
    "Claim": {"UP": 0.15, "DOWN": 0.15},
}

CROSSTABLE_SCHEMA_NAME = "EDF_clean_crosstable"
PUBLISHED_SCHEMA_NAME = "EDF_Published"


def _read_from_crosstable(submodel: str, table: str, root_path: str) -> DataFrame:
    table_info = TableInfo(
        layer=const.LAKE_LAYER_CLEAN,
        sublayer=const.LAKE_SUB_LAYER_CLEAN_CROSSTABLE,
        model="EDF",
        submodel=submodel,
        table=table,
        root_path=root_path,
    )
    return read_dataframe(table_info)


def _read_from_published(submodel: str, table: str, root_path: str) -> DataFrame:
    table_info = TableInfo(
        layer=const.LAKE_LAYER_PUBLISHED,
        model="EDF",
        submodel=submodel,
        table=table,
        root_path=root_path,
    )
    return read_dataframe(table_info)


def get_combined_df(published_df, new_df, join_cols):
    return new_df.join(published_df, join_cols, "left")


# TODO: Refactor this function, it is too long. Could be turned in a class
def validate_by_year(validation_type: str, root_path: str):
    """Validate by year of account."""
    base.logger.info(f"Validating {validation_type} by year of account")
    assert validation_type in ["Policy", "Claim"], "Invalid validation type"

    def read_from_published(table_name: str):
        return _read_from_published(validation_type, table_name, root_path)

    def read_from_crosstable(table_name: str):
        return _read_from_crosstable(validation_type, table_name, root_path)

    section_id = "ClaimsSectionID" if validation_type == "Claim" else "PolicySectionID"
    main_id = "ClaimsID" if validation_type == "Claim" else "PolicyID"
    transaction_id = (
        "ClaimsSectionTransID"
        if validation_type == "Claim"
        else "PolicySectionTransactionID"
    )
    year_of_account = (
        "PolicyYearOfAccount" if validation_type == "Claim" else "YearOfAccount"
    )

    published_df = (
        read_from_published("TransactionComponent")
        .alias("TransactionComponent")
        .join(read_from_published("Transaction"), transaction_id, "left")
        .join(read_from_published("Section"), section_id, "left")
        .join(
            read_from_published(validation_type),
            main_id,
            "left",
        )
    ).select("TransactionComponent.*", year_of_account)

    new_df = (
        read_from_crosstable("TransactionComponent")
        .alias("TransactionComponent")
        .join(read_from_crosstable("Transaction"), transaction_id, "left")
        .join(read_from_crosstable("Section"), section_id, "left")
        .join(
            read_from_crosstable(validation_type),
            main_id,
            "left",
        )
    ).select("TransactionComponent.*", year_of_account)

    group_by_columns = [
        year_of_account,
        const.COUNTRY_COLUMN_NAME,
        const.PARTITION_COLUMN_NAME,
    ]

    grouped_published_df = published_df.groupBy(group_by_columns).agg(
        F.sum("TransactionComponentAmount").alias("Agg_Published"),
        F.count("TransactionComponentAmount").alias("Count_Published"),
    )

    grouped_new_df = new_df.groupBy(group_by_columns).agg(
        F.sum("TransactionComponentAmount").alias("Agg_New"),
        F.count("TransactionComponentAmount").alias("Count_New"),
    )

    combined_df = get_combined_df(
        grouped_published_df, grouped_new_df, group_by_columns
    ).cache()

    country_partition_pairs = set(
        [
            record
            for record in combined_df.select(
                const.COUNTRY_COLUMN_NAME, const.PARTITION_COLUMN_NAME
            )
            .distinct()
            .collect()
        ]
    )

    # noinspection PyTypeChecker
    comparison_df = (
        combined_df.withColumn(
            f"Agg_Variation",
            F.abs(F.col("Agg_Published") - F.col("Agg_New")) / F.col("Agg_Published"),
        )
        .withColumn(
            "AGG_MAX_VARIATION",
            F.when(
                F.col("Agg_New") > F.col("Agg_Published"),
                F.lit(MAX_VARIATIONS[validation_type]["UP"]),
            ).otherwise(
                F.lit(MAX_VARIATIONS[validation_type]["DOWN"]),
            ),
        )
        .withColumn(
            "Count_Variation",
            F.abs(F.col("Count_Published") - F.col("Count_New"))
            / F.col("Count_Published"),
        )
        .withColumn(
            "COUNT_MAX_VARIATION",
            F.when(
                F.col("Count_New") > F.col("Count_Published"),
                F.lit(MAX_VARIATIONS[validation_type]["UP"]),
            ).otherwise(
                F.lit(MAX_VARIATIONS[validation_type]["DOWN"]),
            ),
        )
    )

    cond_high_premium_variation = F.col("Agg_Variation") > F.col("AGG_MAX_VARIATION")
    cond_high_count_variation = F.col("Count_Variation") > F.col("COUNT_MAX_VARIATION")
    cond_high_variation = cond_high_premium_variation | cond_high_count_variation

    is_before_april = datetime.now().month < 4
    cond_not_too_early_date = ~(
        (F.col(year_of_account) == F.lit(datetime.now().year)) & F.lit(is_before_april)
    )
    cond_is_not_future_date = ~(F.col(year_of_account) > F.lit(datetime.now().year))

    invalid_df = comparison_df.filter(
        cond_not_too_early_date & cond_is_not_future_date & cond_high_variation
    )
    invalid_country_partition_pairs = set(
        record
        for record in invalid_df.select(
            const.COUNTRY_COLUMN_NAME, const.PARTITION_COLUMN_NAME
        )
        .distinct()
        .collect()
    )
    valid_country_partition_pairs = set(
        country_partition
        for country_partition in country_partition_pairs
        if country_partition not in invalid_country_partition_pairs
    )

    def show_invalid_df(invalid_df) -> None:
        selected_columns = [
            const.COUNTRY_COLUMN_NAME,
            const.PARTITION_COLUMN_NAME,
            "Agg_Published",
            "Agg_New",
            "Agg_Variation",
            "AGG_MAX_VARIATION",
            "Count_Published",
            "Count_New",
            "Count_Variation",
            "COUNT_MAX_VARIATION",
        ]
        other_columns = [
            column for column in invalid_df.columns if column not in selected_columns
        ]
        base.logger.warning(f"Invalid dataframe ({validation_type=}):")
        invalid_df.select(other_columns + selected_columns).show(truncate=False)

    if invalid_country_partition_pairs:
        show_invalid_df(invalid_df)

    return valid_country_partition_pairs, invalid_country_partition_pairs


def validation(root_path: str) -> List[Dict[str, str]] | None:
    valid_policy_pairs, invalid_policy_pairs = validate_by_year("Policy", root_path)
    valid_claim_pairs, invalid_claim_pairs = validate_by_year("Claim", root_path)

    invalid_pairs = invalid_policy_pairs | invalid_claim_pairs
    valid_pairs = (valid_policy_pairs | valid_claim_pairs) - invalid_pairs

    assert valid_pairs, "No valid country-partition pairs found"

    if invalid_pairs:
        base.logger.error(f"Invalid country partition pairs found: {invalid_pairs}")
        specific_partition_to_promote = [record.asDict() for record in valid_pairs]
    else:
        specific_partition_to_promote = None  # all partitions should be promoted

    return specific_partition_to_promote
