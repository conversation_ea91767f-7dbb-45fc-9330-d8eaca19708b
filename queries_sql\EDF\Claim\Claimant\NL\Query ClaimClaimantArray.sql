select s.internschadenummer                                           as "KeyInternSchadenummer"
, ''                                                                  as "ClaimantAddressArea"
, t.woon<PERSON><PERSON>ats                                                        as "ClaimantAddressCity"
, t.adres                                                             as "C<PERSON>mantAddressStreet"
, t.tegenpartijguid                                                   as "<PERSON><PERSON>mant<PERSON>ode"
, t.landcode                                                          as "<PERSON><PERSON>mantCountry"
, replace(t.naam, '|', '[')                                           as "<PERSON><PERSON>mantName"
, t.postcode                                                          as "ClaimantPostCode"
from pub.schade s
inner join pub.tegenpartij t on t.bedrijfinternschadenummer = s.bedrijfinternschadenummer
left outer join (
select a.bedrijfinternschadenummer, max(a.boekdatum) as boekdatum
from pub.schadeboeking a
  group by a.bedrijfinternschadenummer) b
  on b.bedrijfinternschadenummer = s.bedrijfinternschadenummer
left outer join (
select x.bedrijfinternschadenummer, max(x.boekdatum) as boekdatum
from pub.schadereserve x
  group by x.bedrij<PERSON>ternschadenummer) m
  on m.bedrijfinternschadenummer = s.bedrijfinternschadenummer
