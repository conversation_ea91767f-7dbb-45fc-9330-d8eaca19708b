from pyspark.sql import DataFrame

from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.currency import (
    PolicyExcessCurrencyConversor,
)
from models_scripts.transformations.edf.common.sisnet_scs_migration import (
    exclude_matched_policies_in_scs_and_sisnet,
)


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["Excess_main"]
    exchange_rate_df = df_dict["Excess_exchange_rate"]
    mapping_df = df_dict["scs_sisnet_compare"]

    output_df = exclude_matched_policies_in_scs_and_sisnet(main_df, mapping_df)
    output_df = PolicyExcessCurrencyConversor.add_currency_columns(
        main_df, exchange_rate_df
    )

    return output_df
