Columns:
  SettledSectionTransactionID:
    dataType: int
    calculatedColumn: True
  SettledTransactionID:
    dataType: string
  PolicySectionID:
    dataType: string
  PolicyID:
    dataType: string
  KeyIdPolis:
    dataType: string
  KeyDekkingsNummer:
    dataType: string
  KeyFactuurnummer:
    dataType: string
  OriginalCurrencyCode:
    dataType: string
    conformCurrency: True
  RateOfExchange:
    dataType: float
  SettlementCurrencyCode:
    dataType: string
    conformCurrency: True
  Sort:
    dataType: string
  TransactionDate:
    dataType: date
  TransactionReference:
    dataType: string
  TransactionTypeCode:
    dataType: string
  TransactionTypeDescription:
    dataType: string
  TransactionSequenceNumber:
    dataType: string
  PreviousSourceSystem:
    dataType: string
  PreviousSourceSystemDescription:
    dataType: string
  PreviousTransactionReference:
    dataType: string
  USGAAP_Date:
    dataType: date
primaryKey:
  - SettledSectionTransactionID
