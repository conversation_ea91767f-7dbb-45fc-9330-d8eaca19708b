from mines2.core.extensions.misc import assert_dataframe_equality

import models_scripts.transformations.edf.Policy_Section_ES as transform


class TestPolicySectionES:
    def test_fill_key_reserving(self, spark):
        """Test the fill_key_reserving function."""
        main_df = spark.range(8).selectExpr("CAST(id AS STRING) AS KeyIdPolis")
        policy_df = spark.createDataFrame(
            [
                ("ES:0", "0", "RC01", "GRCP00"),
                ("ES:1", "1", "RC02", "GRCP001"),
                ("ES:2", "2", "RC02", "GRCP002"),
                ("ES:3", "3", "RC02", None),
                ("ES:4", "4", "RC04", "GRCP004"),
                ("ES:5", "5", "RC05", None),
                ("ES:7", "7", "RC02", "GRCP001"),
            ],
            ["PolicyID", "KeyIdPolis", "PolicyProductCode", "AssuredMainActivityCode"],
        )
        activity_code_df = spark.createDataFrame(
            [
                ("ES:0", "ACTP0000"),
                ("ES:1", "ACTP0741"),
                ("ES:2", "ACTP0112"),
                ("ES:3", "ACTP0113"),
                ("ES:4", "ACTP0114"),
                ("ES:5", None),
                ("ES:7", None),
            ],
            ["PolicyID", "ActivityCode"],
        )

        df_output = transform.add_key_reserving_sisnet(
            main_df, policy_df, activity_code_df
        )
        df_expected = spark.createDataFrame(
            [
                ("0", "ES:RC01"),
                ("1", "ES:RC02:GRCP001:ACTP0741"),
                ("2", "ES:RC02:GRCP002"),
                ("3", "ES:RC02:"),
                ("4", "ES:RC04"),
                ("5", "ES:RC05"),
                ("6", "ES:"),
                ("7", "ES:RC02:GRCP001"),
            ],
            ["KeyIdPolis", "KeyReserving"],
        )
        assert_dataframe_equality(df_output, df_expected)
