from typing import Dict, List, Optional

import pyspark.sql.functions as F
from pyspark.sql import DataFrame

from models_scripts.transformations.common.misc import TEMP_COLUMN_PREFIX


def add_column_from_match_table(
    df: DataFrame,
    match_df: DataFrame,
    join_col: str | list | Dict[str, str],
    column_to_replicate: Dict[str, Optional[str]],
    how: str = "left",
    coalesce_existing: bool = False,
    keep_original_if_not_match: bool = False,
    validate_duplication: bool = True,
    null_safe: bool = False,
) -> DataFrame:
    """Add or replace column from matching table.

    Args:
        df (DataFrame): The main DataFrame to which the columns will be added or replaced.
        match_df (DataFrame): The DataFrame containing the columns to be added or replaced.
        join_col (str | list | dict): The column(s) used for joining the main DataFrame and the match DataFrame.
            if is a list will be used as a composite key. if is a dict will be used as a mapping of the join columns,
            where the key is the left (main) dataframe columns and the value is the right (match) dataframe columns.
        column_to_replicate (Dict[str, Optional[str]]): A dictionary mapping source column names to destination column names.
        how (str, optional): The join type. Defaults to "left". If the records to keep should be on the df or in the
            match_df (usual join how).
        coalesce_existing (bool, optional): Whether to coalesce the existing column with the new column. Defaults to False.
        keep_original_if_not_match (bool, optional): Whether to keep the original value if there is no match. Defaults to False.
        validate_duplication (bool, optional): Whether to validate that the join column is unique in the match DataFrame.
            Defaults to True. Will cause a spark action to be executed on the match DataFrame if set to True.
        null_safe (bool, optional): Whether to use null-safe equality (eqNullSafe) when joining. Defaults to False.

    Returns:
        DataFrame: The main DataFrame with the added or replaced columns.

    Raises:
        AssertionError: If the join column(s) is not a subset of the main DataFrame columns or if it is not unique.
        AssertionError: If there are duplicate rows in the match DataFrame based on the join column(s).
    """
    if isinstance(join_col, str):
        join_dict = {join_col: join_col}
    elif isinstance(join_col, list):
        assert len(join_col) > 0, "join_col must have at least one column"
        assert len(join_col) == len(
            set(join_col)
        ), "columns must be unique, but got duplicates in {join_col=}"
        assert all(
            isinstance(col, str) for col in join_col
        ), "join_col must be a list of strings"

        join_dict = {col: col for col in join_col}
    else:
        join_dict = join_col

    assert not (coalesce_existing and keep_original_if_not_match), (
        "Only one of coalesce_existing or " "keep_original_if_not_match can be True"
    )

    # Check if the join column is a subset of the main DataFrame columns
    src_join_cols = list(join_dict.keys())
    match_join_cols = list(join_dict.values())

    # Check if the join column is unique in the main DataFrame
    assert set(src_join_cols).issubset(
        set(df.columns)
    ), f"{src_join_cols=} must be a subset of {df.columns=}"

    # Check if the join column  and values are unique in the  match DataFrame

    if validate_duplication:
        duplicated_count = (
            match_df.groupBy(match_join_cols).count().filter("count > 1").count()
        )
        assert duplicated_count == 0, f"{duplicated_count=} for match_df must be 0!"

    src_dst_map = {
        src_col: src_col if dst_col is None else dst_col
        for src_col, dst_col in column_to_replicate.items()
    }  # if no new name provide will keep the old name

    NEW_COL_PREFIX = TEMP_COLUMN_PREFIX + "NewCol_"
    cols_to_copy = [
        F.col(src).alias(NEW_COL_PREFIX + src) for src in src_dst_map.keys()
    ]
    renamed_join_cols = [
        F.col(src).alias(NEW_COL_PREFIX + src) for src in match_join_cols
    ]
    match_df = match_df.select(*renamed_join_cols, *cols_to_copy)

    if null_safe:
        join_clause = [
            df[col_source].eqNullSafe(match_df[NEW_COL_PREFIX + col_match])
            for col_source, col_match in join_dict.items()
        ]
    else:
        join_clause = [
            df[col_source] == match_df[NEW_COL_PREFIX + col_match]
            for col_source, col_match in join_dict.items()
        ]

    joined_df = df.join(match_df, on=join_clause, how=how)

    for src_col, dst_col in src_dst_map.items():
        src_col_with_prefix = NEW_COL_PREFIX + src_col
        if coalesce_existing:
            col = F.coalesce(F.col(src_col_with_prefix), F.col(dst_col))
        elif keep_original_if_not_match:
            # The difference is that sometimes we can have a match with a record that is null, so in this if is the
            # case that we want to update with the null if it has a match and the original value if not caring about
            # nulls
            join_col_with_prefix = NEW_COL_PREFIX + match_join_cols[0]
            col = F.when(
                F.col(join_col_with_prefix).isNotNull(), F.col(src_col_with_prefix)
            ).otherwise(F.col(dst_col))
        else:
            col = F.col(src_col_with_prefix)
        joined_df = joined_df.withColumn(dst_col, col).drop(src_col_with_prefix)

    joined_df = joined_df.drop(*[NEW_COL_PREFIX + col for col in match_join_cols])

    return joined_df


def add_prefix_for_join(
    df: DataFrame, non_prefixed_cols: List[str], cols_to_prefix: List[str], prefix: str
) -> DataFrame:
    """Add a prefix to all columns of a DataFrame.

    Args:
        df (DataFrame): The DataFrame to which the prefix will be added.
        non_prefixed_cols (List[str]): The columns that should not be prefixed.
        cols_to_prefix (List[str]): The columns to be prefixed.
        prefix (str): The prefix to add to the columns.

    Returns:
        DataFrame: The DataFrame with the prefix added to the columns.
    """
    prefixed_cols = [F.col(src).alias(prefix + src) for src in cols_to_prefix]
    return df.select(*non_prefixed_cols, *prefixed_cols)
