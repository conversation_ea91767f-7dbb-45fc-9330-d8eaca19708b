import pyspark.sql.functions as F
from mines2.core.extensions.misc import assert_dataframe_equality

import models_scripts.transformations.edf.Reference_DatesGAAP_UN as transform


class TestDatesGAAP:
    def test_add_gaap_end_date(self, spark):
        """Test the add_gaap_end_date function."""
        main_df = spark.createDataFrame(
            [
                ("01-Jan-19", "Jan-2019"),
                ("02-Mar-20", "Mar-2020"),
                ("03-Oct-23", "Oct-2023"),
                ("01-Oct-23", "Oct-2023"),
                ("02-Dec-24", "Dec-2024"),
                ("04-Dec-24", "Dec-2024"),
            ],
            ["EndDate", "GAAPMonthYear"],
        )
        df_output = transform.add_gaap_end_date(main_df)
        df_expected = spark.createDataFrame(
            [
                ("01-Jan-19", "Jan-2019", "2019-01-01"),
                ("02-Mar-20", "Mar-2020", "2020-03-02"),
                ("03-Oct-23", "Oct-2023", "2023-10-03"),
                ("04-Dec-24", "Dec-2024", "2024-12-04"),
            ],
            ["EndDate", "GAAPMonthYear", "GAAPEndDate"],
        )
        df_expected = df_expected.withColumn(
            "GAAPEndDate", F.to_date(F.col("GAAPEndDate"), "yyyy-MM-dd")
        )
        assert_dataframe_equality(df_output, df_expected)
