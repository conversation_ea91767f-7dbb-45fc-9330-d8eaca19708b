import pytest
from mines2.core.extensions.misc import assert_dataframe_equality
from pyspark.sql import DataFrame

from models_scripts.transformations.common.traits import transformation


class TestTransformationDecorator:
    def input_df(self, spark):
        return spark.createDataFrame([(1, "a"), (2, "b")], ["ID", "column"])

    @pytest.fixture()
    def dummy_function(self, spark):
        def dummy_function_def() -> DataFrame:
            return self.input_df(spark)

        return dummy_function_def

    @pytest.fixture()
    def mocked_checkpoint_dataframe_func(self, mocker):
        mocked_checkpoint_dataframe = mocker.patch(
            "models_scripts.transformations.common.traits.checkpoint_dataframe"
        )

        def side_effect_func(value):
            return value

        mocked_checkpoint_dataframe.side_effect = side_effect_func
        return mocked_checkpoint_dataframe

    def test_checkpoint_transformations_variable_not_set(
        self, mocked_checkpoint_dataframe_func, dummy_function
    ):
        expected_df = dummy_function()
        output_df = transformation()(dummy_function)()
        assert_dataframe_equality(output_df, expected_df)
        assert mocked_checkpoint_dataframe_func.call_count == 0

    def test_checkpoint_transformations_variable_set(
        self, mocked_checkpoint_dataframe_func, dummy_function, monkeypatch
    ):
        monkeypatch.setenv("MINES_CHECKPOINT_TRANSFORMATIONS", "True")
        expected_df = dummy_function()
        output_df = transformation()(dummy_function)()
        assert_dataframe_equality(output_df, expected_df)
        assert mocked_checkpoint_dataframe_func.call_count == 1

    def test_transformation_decorator_with_force_checkpoint_true(
        self, spark, mocked_checkpoint_dataframe_func
    ):
        @transformation(force_checkpoint=True)
        def dummy_custom_function(df_: DataFrame) -> DataFrame:
            return df_

        df = self.input_df(spark)
        output_df_true = dummy_custom_function(df)
        assert mocked_checkpoint_dataframe_func.call_count == 1
        assert_dataframe_equality(output_df_true, df)

    def test_transformation_decorator_with_force_checkpoint_false(
        self, spark, mocked_checkpoint_dataframe_func
    ):
        @transformation(force_checkpoint=False)
        def dummy_custom_function(df_: DataFrame) -> DataFrame:
            return df_

        df = self.input_df(spark)
        output_df = dummy_custom_function(df)
        assert mocked_checkpoint_dataframe_func.call_count == 0
        assert_dataframe_equality(output_df, df)

    def test_transformation_decorator_without_force_checkpoint(
        self, spark, mocked_checkpoint_dataframe_func
    ):
        @transformation
        def dummy_custom_function(df_: DataFrame) -> DataFrame:
            return df_

        df = self.input_df(spark)
        output_df = dummy_custom_function(df)
        assert mocked_checkpoint_dataframe_func.call_count == 0
        assert_dataframe_equality(output_df, df)

    def test_transformation_decorator_using_class(
        self, spark, mocked_checkpoint_dataframe_func
    ):
        class DummyClass:
            @transformation
            def dummy_custom_function(self, df_: DataFrame) -> DataFrame:
                return df_

            @transformation(force_checkpoint=True)
            def dummy_custom_function_2(self, df_: DataFrame) -> DataFrame:
                return df_

        df = self.input_df(spark)
        dummy_class_obj = DummyClass()

        output_df = dummy_class_obj.dummy_custom_function(df)
        assert mocked_checkpoint_dataframe_func.call_count == 0
        assert_dataframe_equality(output_df, df)

        output_df = dummy_class_obj.dummy_custom_function_2(df)
        assert mocked_checkpoint_dataframe_func.call_count == 1
        assert_dataframe_equality(output_df, df)
