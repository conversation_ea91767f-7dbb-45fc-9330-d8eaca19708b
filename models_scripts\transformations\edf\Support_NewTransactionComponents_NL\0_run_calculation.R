# Control file for initialising libraries, functions
# calling connection to Level, importing data etc.


# 1.  Scripts ####
options(repos = c(CRAN = "https://cloud.r-project.org"))

list.of.packages <- c(
  "foreach",
  "doParallel",
  "data.table",
  "lubridate",
  "dplyr",
  "arrow",
  "purrr",
  "tidyr"
)


new.packages <- list.of.packages[!(list.of.packages %in% installed.packages()[, "Package"])]

if (length(new.packages) > 0) {
  install.packages(new.packages, dep = TRUE)
}

# loading packages
for (package.i in list.of.packages) {
  suppressPackageStartupMessages(
    library(
      package.i,
      character.only = TRUE
    )
  )
}

all_pol <- read.csv("input.csv", header = T)

### convert date type

all_pol$UWDt <- as_date(all_pol$UWDt)
all_pol$Expiratiedatum <- as_date(all_pol$Expiratiedatum)
all_pol$Expiry <- as_date(all_pol$Expiry)
all_pol$CreatieDatum <- as_date(all_pol$CreatieDatum)
all_pol$Mutatiedatum <- as_date(all_pol$Mutatiedatum)
# 1.  Scripts ####

source("1_Add-Fields_EDF.R")

### paralellise the loop
parallel::detectCores()

n.cores <- parallel::detectCores() - 1

### register the cluster
my.cluster <- parallel::makeCluster(
  n.cores,
  type = "PSOCK"
)

# register it to be used by %dopar%

doParallel::registerDoParallel(cl = my.cluster)

# check if it is registered (optional)
foreach::getDoParRegistered()

policy_list <- unique(all_pol$InternPolisnummer)
### to use only in case we need to check which fails


t1 <- now()
Final_pol <- foreach(
  i = 1:length(policy_list),
  .packages = c("data.table", "dplyr", "lubridate")
) %dopar% {
  x <- all_pol %>%
    filter(InternPolisnummer %in% policy_list[i]) %>%
    data.table() %>%
    calc_prem(.)
  return(x)
}
print(now() - t1)
parallel::stopCluster(cl = my.cluster)


calculated_prem <- rbindlist(Final_pol)



EDF_Premium<-calculated_prem[, list(NPREM=sum(deNPREM,na.rm = T)+sum(deBKR,na.rm = T),
                                      BKR=sum(deBKR,na.rm = T),
                                      IPT=sum(deIPT, na.rm = T),
                                      MinKeyIDPolis=max(min_IdPolis_sec),
                                      MaxKeyIDPolis=max(max_IdPolis_sec) ),
                                 list( KeyIdPolis=IdPolis,
                                       KeyDekkingsNummer=Dekkingsnummer)]

output_file<-melt(EDF_Premium,id.vars = c("MinKeyIDPolis","MaxKeyIDPolis","KeyIdPolis",
                                            "KeyDekkingsNummer"),
                    measure.vars = c("NPREM","BKR","IPT"),
                    variable.name = c("TransactionComponentTypeCode"),
                    value.name = "TransactionComponentAmount")

# Create a local temporary folder and save the result of the analysis

write.csv(output_file, "output.csv", row.names = F)
