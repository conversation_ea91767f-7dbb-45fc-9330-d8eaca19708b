from pyspark.sql import DataFrame

from models_scripts.transformations.common.misc import cast_to_int_and_to_string
from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.currency import (
    SettlementCurrencyConversor,
)


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["TransactionComponent_main"]
    transaction_df = df_dict["Settlement_Transaction"]
    exchange_rate_df = df_dict["TransactionComponent_exchange_rate"]

    fixed_main_df = cast_to_int_and_to_string(main_df, "KeyIdPolis")

    component_with_converted_currencies_df = (
        SettlementCurrencyConversor.add_new_columns(
            fixed_main_df, transaction_df, exchange_rate_df
        )
    )

    return component_with_converted_currencies_df
