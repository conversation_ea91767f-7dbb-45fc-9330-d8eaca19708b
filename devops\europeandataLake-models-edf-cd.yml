name: $(TeamProject)_$(Build.DefinitionName)_$(Date:yyyyMMdd)$(Rev:.r)

trigger:
  batch: true
  branches:
    include:
    - "*"

resources:
  repositories:
  - repository: EuropeanDataLake-Models
    type: git
    name: EuropeanDataLake-Models
    ref: main


extends:
  template: devops/generic_deployment.yml@EuropeanDataLake-Models
  parameters:
    ModelName: EDF
    ModelJsonFile: model_edf.json
