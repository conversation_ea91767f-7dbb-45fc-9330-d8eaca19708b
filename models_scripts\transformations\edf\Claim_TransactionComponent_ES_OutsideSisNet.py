from pyspark.sql import DataFrame

from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.currency import ClaimCurrencyConversor


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["TransactionComponent_main"]
    transaction_df = df_dict["Claim_Transaction"]
    exchange_rate_df = df_dict["TransactionComponent_exchange_rate"]

    component_with_converted_currencies_df = ClaimCurrencyConversor.add_new_columns(
        main_df, transaction_df, exchange_rate_df
    )

    return component_with_converted_currencies_df
