from pyspark.sql import DataFrame

from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.backward_populate_records import (
    add_missing_ids,
)
from models_scripts.transformations.edf.common.currency import (
    PolicyExcessCurrencyConversor,
)


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    """The objective of this transformation is to add the ids created on
    TransactionComponent custom transformation into the Transaction Table."""
    main_df = df_dict["Excess_main"]
    extra_records_df = df_dict["Support_NewTransactionComponents"]
    exchange_rate_df = df_dict["Excess_exchange_rate"]

    output_df = add_missing_ids(main_df, extra_records_df)
    output_df = PolicyExcessCurrencyConversor.add_currency_columns(
        output_df, exchange_rate_df
    )

    return output_df
