from mines2.core.extensions.misc import assert_dataframe_equality
from pyspark.sql import Row

import models_scripts.transformations.edf.Policy_Policy_ES as transform


def test_fix_renewal_values_es(spark):
    main_df = spark.createDataFrame(
        [
            Row(PolicyID=1, StatusCode="RENO"),
            Row(PolicyID=2, StatusCode="ANUL"),
            Row(PolicyID=3, StatusCode="NORE"),
            Row(PolicyID=4, StatusCode="RECO"),
            Row(PolicyID=5, StatusCode="REHA"),
            Row(PolicyID=6, StatusCode="VENC"),
            Row(PolicyID=7, StatusCode="VIGE"),
            Row(PolicyID=8, StatusCode=None),
        ]
    )
    expected_df = spark.createDataFrame(
        [
            Row(PolicyID=1, StatusCode="RENO", RenewalPolicyIndicator="renew"),
            Row(PolicyID=2, StatusCode="ANUL", RenewalPolicyIndicator="new"),
            Row(PolicyID=3, StatusCode="NORE", RenewalPolicyIndicator="new"),
            Row(PolicyID=4, StatusCode="RECO", RenewalPolicyIndicator="new"),
            Row(PolicyID=5, StatusCode="REHA", RenewalPolicyIndicator="new"),
            Row(PolicyID=6, StatusCode="VENC", RenewalPolicyIndicator="new"),
            Row(PolicyID=7, StatusCode="VIGE", RenewalPolicyIndicator="new"),
            Row(PolicyID=8, StatusCode=None, RenewalPolicyIndicator="new"),
        ]
    )
    output_df = transform.fix_renewal_values_es(main_df)
    assert_dataframe_equality(output_df, expected_df)


def test_manual_fix_renewal_values_es(spark):
    main_df = spark.createDataFrame(
        [
            Row(KeyIdPolis=1, RenewalPolicyIndicator="new"),
            Row(KeyIdPolis=2, RenewalPolicyIndicator="renew"),
            Row(KeyIdPolis=3, RenewalPolicyIndicator="new"),
            Row(KeyIdPolis=4, RenewalPolicyIndicator="renew"),
            Row(KeyIdPolis=5, RenewalPolicyIndicator="renew"),
            Row(KeyIdPolis=6, RenewalPolicyIndicator=None),
            Row(KeyIdPolis=7, RenewalPolicyIndicator="new"),
            Row(KeyIdPolis=8, RenewalPolicyIndicator=None),
        ]
    )
    df_new_renewal = spark.createDataFrame(
        [
            Row(KeyIdPolis=0, RenewalPolicyIndicator="test"),
            Row(KeyIdPolis=1, RenewalPolicyIndicator="New"),
            Row(KeyIdPolis=2, RenewalPolicyIndicator="Renewal"),
            Row(KeyIdPolis=3, RenewalPolicyIndicator="Renewal"),
            Row(KeyIdPolis=4, RenewalPolicyIndicator="New"),
            Row(KeyIdPolis=6, RenewalPolicyIndicator="New"),
            Row(KeyIdPolis=7, RenewalPolicyIndicator=None),
            Row(KeyIdPolis=8, RenewalPolicyIndicator=None),
        ]
    )
    expected_df = spark.createDataFrame(
        [
            Row(KeyIdPolis=1, RenewalPolicyIndicator="new"),
            Row(KeyIdPolis=2, RenewalPolicyIndicator="renew"),
            Row(KeyIdPolis=3, RenewalPolicyIndicator="renew"),
            Row(KeyIdPolis=4, RenewalPolicyIndicator="new"),
            Row(KeyIdPolis=5, RenewalPolicyIndicator="renew"),
            Row(KeyIdPolis=6, RenewalPolicyIndicator="new"),
            Row(KeyIdPolis=7, RenewalPolicyIndicator="new"),
            Row(KeyIdPolis=8, RenewalPolicyIndicator=None),
        ]
    )
    output_df = transform.manual_fix_renewal_values_es(main_df, df_new_renewal)
    assert_dataframe_equality(output_df, expected_df)


def test_fix_business_classification(spark):
    main_df = spark.createDataFrame(
        [(1, "ESANGOB"), (2, "CPEN151"), (3, "other"), (4, None)],
        ["PolicyID", "UnderwriterCode"],
    )
    expected_df = spark.createDataFrame(
        [
            (1, "ESANGOB", "Tailormade Solutions"),
            (2, "CPEN151", "Tailormade Solutions"),
            (3, "other", "Product Solutions"),
            (4, None, "Product Solutions"),
        ],
        ["PolicyID", "UnderwriterCode", "BusinessClassification"],
    )
    output_df = transform.fix_business_classification(main_df)
    assert_dataframe_equality(output_df, expected_df)
