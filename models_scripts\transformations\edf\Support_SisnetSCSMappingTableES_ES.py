from functools import reduce

import mines2.core.constants as const
import pyspark.sql.functions as F
from pyspark.sql import Column, DataFrame, Window

from models_scripts.transformations.common.traits import business_logic, transformation

dekking_map = {
    "1. Construction Professional Risks": 1,
    "2. Financial Services Professional Risks": 2,
    "3. Miscellaneous Professional Risks": 3,
    "4. Professions Professional Risks": 4,
    "6. D and O": 5,
    "11. Liability": 6,
    "Surety": 7,
    "Personal Accident": 8,
    "Private Clinics": 9,
}


class ValidateMapping:
    """This class contains functions to validate mapping between SISNET and SCS data."""

    @classmethod
    @transformation
    def get_quarantined_mapping(
        cls,
        df: DataFrame,
        unmapped_policy_df: DataFrame,
        enhanced_unmapped_policy_df: DataFrame,
    ) -> DataFrame:
        """This function is the main function that validates mapping between SISNET and SCS data. The validation is
        done in two steps: 1. Check if the records in the original dataframe are in the unmapped_policy_df and not in
        the enhanced_unmapped_policy_df. 2. Check if the PolicyReference column is null in the
        enhanced_unmapped_policy_df. If the validation fails, the record is quarantined.
        """

        quarantined_mapping_df = cls._invalidate_missing_unmapped_records(
            df, unmapped_policy_df, enhanced_unmapped_policy_df
        )
        quarantined_enhanced_unmapped_df = cls._is_missing_policy_ref(
            enhanced_unmapped_policy_df
        )
        return quarantined_mapping_df.unionByName(
            quarantined_enhanced_unmapped_df, allowMissingColumns=True
        )

    @staticmethod
    def _join_dataframes(
        df: DataFrame,
        unmapped_policy_df: DataFrame,
        enhanced_unmapped_policy_df: DataFrame,
    ) -> DataFrame:
        """This function joins the original dataframe with unmapped_policy_df and enhanced_unmapped_policy_df"""
        unmapped_to_join_df = unmapped_policy_df.select(
            F.col("KeyInternSchadenummer").alias("unmapped_KeyInternSchadenummer")
        ).distinct()
        enhanced_to_join_df = enhanced_unmapped_policy_df.select(
            F.col("KeyInternSchadenummer").alias("enhanced_KeyInternSchadenummer")
        ).distinct()

        joined_unmapped_records_df = df.join(
            unmapped_to_join_df,
            on=F.col("KeyInternSchadenummer")
            == F.col("unmapped_KeyInternSchadenummer"),
            how="left",
        )
        joined_enhanced_records_df = joined_unmapped_records_df.join(
            enhanced_to_join_df,
            on=F.col("KeyInternSchadenummer")
            == F.col("enhanced_KeyInternSchadenummer"),
            how="left",
        )

        return joined_enhanced_records_df

    @staticmethod
    def _get_missing_unmapped_cond() -> Column:
        """This function returns a condition to check if the record is in the unmapped_policy_df and not in the
        enhanced_unmapped_policy_df."""
        is_in_unmapped_cond = F.col("unmapped_KeyInternSchadenummer").isNotNull()
        not_in_enhanced_cond = F.col("enhanced_KeyInternSchadenummer").isNull()

        return is_in_unmapped_cond & not_in_enhanced_cond

    @classmethod
    @transformation
    def _invalidate_missing_unmapped_records(
        cls,
        df: DataFrame,
        unmapped_policy_df: DataFrame,
        enhanced_unmapped_policy_df: DataFrame,
    ) -> DataFrame:
        """This function checks if the records in the original dataframe are in the unmapped_policy_df and not in
        the enhanced_unmapped_policy_df. If the validation fails, the record is quarantined.
        """
        df = df.alias("original_df")
        quarantine_reason = (
            f"Record was in csv of records to match, but wasn't in scs data"
        )
        quarantine_col = const.CUSTOM_QUARANTINE_PREFIX + "MappingIsMissing"

        joined_df = cls._join_dataframes(
            df, unmapped_policy_df, enhanced_unmapped_policy_df
        )
        missing_unmapped_cond = cls._get_missing_unmapped_cond()
        quarantined_records = joined_df.filter(missing_unmapped_cond).withColumn(
            quarantine_col, F.lit(quarantine_reason)
        )

        return quarantined_records.select("original_df.*", quarantine_col)

    @classmethod
    def _is_missing_policy_ref(cls, df: DataFrame) -> DataFrame:
        """This function checks if the PolicyReference column is null in the enhanced_unmapped_policy_df.
        If the PolicyReference column is null, the record is quarantined."""
        df = df.alias("original_df")
        quarantine_reason = f"PolicyRef in mapping table is missing"
        quarantine_col = const.CUSTOM_QUARANTINE_PREFIX + "PolicyRefIsMissing"
        quarantined_records_df = df.filter(F.col("PolicyRef").isNull()).withColumn(
            quarantine_col, F.lit(quarantine_reason)
        )

        return quarantined_records_df.select("original_df.*", quarantine_col)


class OrigenCleaner:
    @classmethod
    @transformation
    def apply(cls, df):
        """
        This function applies the following transformations:
        1. If "ORIGEN" value is "2600", it would be transformed into "2600/2020"
        2. If "lengthid" > 4, then shorten the string in 'ORIGEN' column by one character from end.

        """
        df = df.withColumn("ORIGEN", cls._manual_updates_col())
        df = df.withColumn("ORIGEN", cls._update_origen_length_greater_than_4())
        return df

    @staticmethod
    def _manual_updates_col() -> Column:
        return F.when(F.col("ORIGEN") == "2600", "2600/2020").otherwise(F.col("ORIGEN"))

    @staticmethod
    def _get_lengthid_col() -> Column:
        split_col = F.split(F.col("ORIGEN"), "[\\s/]")
        second_element_exists_cond = F.size(split_col) > 1
        length_second_element_col = F.length(F.element_at(split_col, 2))
        return F.when(second_element_exists_cond, length_second_element_col).otherwise(
            0
        )

    @classmethod
    def _update_origen_length_greater_than_4(cls) -> Column:
        """If "lengthid" > 4, then shorten the string in 'ORIGEN' column by one character from end.
        For example, if "ORIGEN" value is "2600/20201", it would be transformed into "2600/2020
        """
        lengthid_gt_4_cond = cls._get_lengthid_col() > 4
        origen_substring_updated_col = F.col("ORIGEN").substr(
            F.lit(1), F.length(F.col("ORIGEN")) - F.lit(1)
        )
        return F.when(lengthid_gt_4_cond, origen_substring_updated_col).otherwise(
            F.col("ORIGEN")
        )


class Deduplicator:
    """This class contains functions to deduplicate full_claim_map_df based on KeyInternSchadenummer,
    SCS_KeyIdPolis and SCS_KeyDekkingsNummer columns."""

    dedup_cols = ["SCS_KeyInternSchadenummer", "SCS_KeyDekkingsNummer"]

    @classmethod
    @transformation
    def apply(
        cls, full_claim_map_df: DataFrame, claim_transaction_component_df: DataFrame
    ) -> DataFrame:
        """
        This function deduplicate full_claim_map_df based on KeyInternSchadenummer, SCS_KeyIdPolis and SCS_KeyDekkingsNummer columns.
        """
        duplicated_ids_df = cls._get_duplicated(full_claim_map_df)
        duplicated_df, non_duplicated_df = cls._split_duplicated(
            full_claim_map_df, duplicated_ids_df
        )
        total_amount_df = cls._get_total_amount(claim_transaction_component_df)
        duplicated_with_amount_df = cls._join_with_total_amount(
            duplicated_df, total_amount_df
        )
        joined_df = cls._join_dataframes(
            duplicated_with_amount_df, claim_transaction_component_df
        )
        descending_schadenummer_clause = cls._get_order_by_clause()
        joined_with_windowed_cols_df = cls._apply_window_functions(
            joined_df, descending_schadenummer_clause
        )
        deduplicated_df = cls._apply_conditional_logic(joined_with_windowed_cols_df)
        return deduplicated_df.unionByName(non_duplicated_df, allowMissingColumns=True)

    @classmethod
    def _split_duplicated(
        cls, full_claim_map_df: DataFrame, duplicated_ids_df: DataFrame
    ) -> tuple:
        """This function splits full_claim_map_df into duplicated_df and non_duplicated_df based on the
        duplicated_ids_df."""
        duplicated_df = full_claim_map_df.join(
            duplicated_ids_df, on=cls.dedup_cols, how="left_semi"
        )
        non_duplicated_df = full_claim_map_df.join(
            duplicated_ids_df, on=cls.dedup_cols, how="left_anti"
        )
        return duplicated_df, non_duplicated_df

    @classmethod
    @transformation
    def _get_duplicated(cls, full_claim_map_df: DataFrame) -> DataFrame:
        """This function returns a dataframe with duplicated KeyIdPolis and KeyDekkingsNummer columns."""
        cols_are_not_null = reduce(
            lambda x, y: x & y, [F.col(col).isNotNull() for col in cls.dedup_cols]
        )
        return (
            full_claim_map_df.filter(cols_are_not_null)
            .groupBy(cls.dedup_cols)
            .count()
            .filter(F.col("count") > 1)
        )

    @staticmethod
    @transformation
    def _get_total_amount(claim_transaction_component_df: DataFrame) -> DataFrame:
        """This function returns a dataframe with total amount of transaction by KeyInternSchadenummer,KeyIdPolis."""
        return claim_transaction_component_df.groupBy(
            "KeyInternSchadenummer", "KeyIdPolis"
        ).agg(F.round(F.sum("TransactionComponentAmount")).alias("TotalAmount"))

    @staticmethod
    def _join_with_total_amount(
        duplicated_df: DataFrame, total_amount_df: DataFrame
    ) -> DataFrame:
        """This function adds total amount of transaction to duplicated_df."""
        return duplicated_df.join(
            total_amount_df, on=["KeyInternSchadenummer", "KeyIdPolis"], how="left"
        )

    @staticmethod
    @transformation
    def _join_dataframes(
        full_claim_map_df: DataFrame, claim_transaction_component_df: DataFrame
    ) -> DataFrame:
        """This function joins full_claim_map_df with claim_transaction_component_df."""
        full_claim_map_df = full_claim_map_df
        claim_transaction_component_df = claim_transaction_component_df
        join_clause = ["KeyInternSchadenummer", "KeyIdPolis"]
        return full_claim_map_df.join(
            claim_transaction_component_df, join_clause, how="left"
        )

    @staticmethod
    def _get_order_by_clause() -> list:
        """This function returns the best order by clause to use for deduplication."""
        maximum_elements_in_split = 10
        split_col = F.split(
            F.col("KeyInternSchadenummer"), r"[\/]", maximum_elements_in_split
        )
        descending_schadenummer_clause = []
        for i in range(1, maximum_elements_in_split + 1):
            substring_col = split_col.getItem(i)
            integer_col = substring_col.cast("integer")
            cleaned_col = F.coalesce(integer_col, substring_col)
            descending_schadenummer_clause.append(cleaned_col.desc())
        return descending_schadenummer_clause

    @staticmethod
    @transformation
    def _apply_window_functions(
        joined_df: DataFrame, order_by_clause: list
    ) -> DataFrame:
        """This function calculates the maximum 'TotalAmount' and marks the last row in each partition."""
        windowSpecGroup = Window.partitionBy("SCS_KeyInternSchadenummer")
        windowSpecOrderByKeyIntern = windowSpecGroup.orderBy(*order_by_clause)
        windowSpecOrderByTotalAmount = windowSpecGroup.orderBy(
            F.col("TotalAmount").desc(), *order_by_clause
        )
        return (
            joined_df.withColumn(
                "max_TotalAmount", F.max("TotalAmount").over(windowSpecGroup)
            )
            .withColumn(
                "row_number_TotalAmount",
                F.row_number().over(windowSpecOrderByTotalAmount),
            )
            .withColumn(
                "row_number_KeyIntern", F.row_number().over(windowSpecOrderByKeyIntern)
            )
        )

    @staticmethod
    @transformation
    def _apply_conditional_logic(joined_with_windowed_cols_df: DataFrame) -> DataFrame:
        """This function applies the conditional logic to deduplicate the dataframe."""
        has_non_zero_total_amount_cond = F.col("max_TotalAmount") > 0
        is_max_total_amount_cond = F.col("row_number_TotalAmount") == F.lit(1)
        is_max_key_intern_cond = F.col("row_number_KeyIntern") == F.lit(1)

        deduped_df = joined_with_windowed_cols_df.filter(
            F.when(has_non_zero_total_amount_cond, is_max_total_amount_cond).otherwise(
                is_max_key_intern_cond
            )
        )
        return deduped_df


@transformation
def get_full_claim_map_df(
    claim_map_df: DataFrame, claim_policy_df: DataFrame, claim_section_scs_df: DataFrame
) -> DataFrame:
    """This function joins claim_map, claim_policy and claim_section_scs dataframes.
    It returns a dataframe with columns: KeyInternSchadenummer, PolicyCode, PolicyReference, KeyIdPolis,
    SCS_KeyInternSchadenummer, SCS_KeyIdPolis, SCS_KeyDekkingsNummer, SectionProductCode, SectionReference
    """

    claim_map_df = claim_map_df.select("SISNET", "ORIGEN").alias("claim_map")
    claim_policy_df = claim_policy_df.select(
        "KeyInternSchadenummer", "PolicyCode", "PolicyReference", "KeyIdPolis"
    ).alias("claim_policy")
    claim_policy_df = claim_policy_df.alias("claim_policy")
    claim_section_scs_df = claim_section_scs_df.select(
        F.col("KeyInternSchadenummer").alias("SCS_KeyInternSchadenummer"),
        F.col("KeyIdPolis").alias("SCS_KeyIdPolis"),
        F.col("KeyDekkingsNummer").alias("SCS_KeyDekkingsNummer"),
        "SectionProductCode",
        "SectionReference",
    )
    claim_section_scs_df = claim_section_scs_df.alias("claim_section_scs")

    claim_policy_joined_df = claim_policy_df.join(
        claim_map_df,
        on=F.col("claim_map.SISNET") == F.col("claim_policy.KeyInternSchadenummer"),
        how="left",
    )
    full_join_df = claim_policy_joined_df.join(
        claim_section_scs_df,
        on=F.col("claim_map.ORIGEN")
        == F.col("claim_section_scs.SCS_KeyInternSchadenummer"),
        how="left",
    )
    output_df = full_join_df.select(
        "KeyInternSchadenummer",
        "PolicyCode",
        "PolicyReference",
        "KeyIdPolis",
        F.coalesce("SCS_KeyInternSchadenummer", "ORIGEN").alias(
            "SCS_KeyInternSchadenummer"
        ),
        "SCS_KeyIdPolis",
        "SCS_KeyDekkingsNummer",
        "SectionProductCode",
        "SectionReference",
    )

    return output_df


@transformation
def add_scs_columns_to_unmapped(
    unmapped_df: DataFrame, original_policy_scs_df: DataFrame
) -> DataFrame:
    """
    This function adds SCS_KeyIdPolis, SCS_KeyDekkingsNummer, SectionProductCode, SectionReference columns to the unmapped dataframe.
    """
    unmapped_df = unmapped_df.alias("unmapped")
    scs_df = original_policy_scs_df.select(
        "PolicyRef",
        "IPN",
        "SectionReportingGroup",
        "OldRiskRef",
        F.monotonically_increasing_id().alias("__original_order__"),
        F.col("InceptionDate").alias("__CreationDate__"),
    ).alias("SCS")
    # setup join clause
    is_newkeyid_cond = F.length(F.col("SCS_KeyIdPolis")) == 6
    is_oldkeyid_cond = F.length(F.col("SCS_KeyIdPolis")) == 12
    join_clause_newkeyid = F.col("SCS.IPN") == F.col("unmapped.SCS_KeyIdPolis")
    join_clause_oldkeyid = F.col("SCS.OldRiskRef") == F.col("unmapped.SCS_KeyIdPolis")
    join_clause = (
        F.when(is_newkeyid_cond, join_clause_newkeyid)
        .when(is_oldkeyid_cond, join_clause_oldkeyid)
        .otherwise(F.lit(False))
    )

    # join
    joined_df = unmapped_df.join(scs_df, join_clause, how="left")

    # dedup
    windowSpec = Window.partitionBy("KeyInternSchadenummer", "SCS_KeyIdPolis").orderBy(
        F.desc("PolicyRef"), F.desc("__CreationDate__"), F.desc("__original_order__")
    )
    deduped_df = joined_df.withColumn("rn", F.row_number().over(windowSpec)).filter(
        F.col("rn") == 1
    )
    return deduped_df.drop("__original_order__", "__CreationDate__", "rn")


@transformation
def add_dekking_nummer(df: DataFrame) -> DataFrame:
    """This function adds a column SCS_KeyDekkingsNummer to the dataframe, based on the values in
    SectionProductDescription column. The mapping is defined in dekking_map dictionary.
    """

    mapping_expr = F.create_map(
        [F.lit(x) for x in sum(dekking_map.items(), ())]
    )  # Recreate dict as mapping column

    with_scs_dekking_df = df.withColumn(
        "SCS_KeyDekkingsNummer", mapping_expr.getItem(F.col("SectionReportingGroup"))
    )
    return with_scs_dekking_df


@transformation
def add_product_description(df: DataFrame) -> DataFrame:
    """This function adds a column SectionProductDescription to the dataframe, based on the values in SCS_KeyDekkingsNummer column.
    The mapping is defined in dekking_map dictionary."""

    inverted_dekking_map = {v: k for k, v in dekking_map.items()}
    mapping_expr = F.create_map(
        [F.lit(x) for x in sum(inverted_dekking_map.items(), ())]
    )  # Recreate dict as mapping column
    df = df.withColumn(
        "SectionProductDescription",
        mapping_expr.getItem(F.col("SCS_KeyDekkingsNummer")),
    )
    return df


@transformation
def union_dynamic_mapping_with_static_mapping(
    dynamic_df: DataFrame, unmapped_df: DataFrame, enhanced_unmapped_df: DataFrame
) -> DataFrame:
    """This function unions the dynamic mapping with the static mapping."""
    dynamic_df = dynamic_df.alias("dynamic_df")
    unmapped_df = unmapped_df.alias("unmapped_df")
    enhanced_unmapped_df = enhanced_unmapped_df.alias("enhanced_unmapped_df")

    # Remove records in dynamic mapping that are already in static mapping
    exclusive_dynamic_df = dynamic_df.join(
        unmapped_df, on=["KeyInternSchadenummer"], how="left_anti"
    )
    cleaned_enhanced_unmapped_df = enhanced_unmapped_df.filter(
        F.col("SCS_KeyIdPolis").isNotNull()
    )
    union_df = exclusive_dynamic_df.unionByName(
        cleaned_enhanced_unmapped_df, allowMissingColumns=True
    )
    return union_df


@transformation
def merge_quarantined_df_with_full_claim_map_df(
    full_claim_map_df: DataFrame, quarantined_df: DataFrame
) -> DataFrame:
    """
    This function merges quarantined_df with full_claim_map_df.
    """
    # Get records that are in full_claim_map_df but not in quarantined_df

    exclusive_full_claim_map_df = full_claim_map_df.join(
        quarantined_df, on=["KeyInternSchadenummer"], how="left_anti"
    )
    return exclusive_full_claim_map_df.unionByName(
        quarantined_df, allowMissingColumns=True
    )


@transformation
def fix_scs_keyidpolis(df: DataFrame) -> DataFrame:
    return df.withColumn("SCS_KeyIdPolis", F.col("PolicyRef"))


@transformation
def apply_hardcoded_fixes(df: DataFrame) -> DataFrame:
    """This function applies the hardcoded fixes to the dataframe."""
    manual_fixes_dict = {
        "KeyInternSchadenummer": {
            "2665/2020": "RC06 2021/157",
            "665/2020": "RC06 2021/141",
        },
        "PolicyCode": {"2665/2020": "RC06 2021/571_1", "665/2020": "RC06 2021/138_1"},
        "PolicyReference": {
            "2665/2020": "021S00571RCS_1",
            "665/2020": "021S00138RCS_1",
        },
        "KeyIdPolis": {"2665/2020": "RC06 2021/571_1", "665/2020": "RC06 2021/138_1"},
    }

    df_columns = df.columns
    query_dict = {col: F.col(col) for col in df_columns}
    for column, fix_dict in manual_fixes_dict.items():
        new_col = None
        for key, value in fix_dict.items():
            if new_col is None:
                new_col = F.when(F.col("SCS_KeyInternSchadenummer") == key, value)
            else:
                new_col = new_col.when(F.col("SCS_KeyInternSchadenummer") == key, value)
        new_col = new_col.otherwise(query_dict[column])
        query_dict[column] = new_col
    query_list = [
        col_value.alias(col_name) for col_name, col_value in query_dict.items()
    ]
    return df.select(query_list)


def unify_null_values(df: DataFrame) -> DataFrame:
    """
    This function replaces all the null values in string columns with None.
    """
    df_schema = df.schema  # for local debug is expensive to call df.schema
    table_cols = {field.name: F.col(field.name) for field in df_schema}
    possible_nulls = ["null", "nan", "none", "na", "nat", "n/a", "<na>"]
    for field in df_schema:
        if field.dataType.typeName() == "string":
            column = field.name
            table_cols[column] = (
                F.when(F.lower(F.col(column)).isin(possible_nulls), F.lit(None))
                .otherwise(F.col(column))
                .alias(column)
            )

    return df.select([col for col in table_cols.values()])


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    # claim
    raw_claim_map_df = unify_null_values(df_dict["SisnetSCSMappingTableES_main"]).drop(
        const.SOURCE_SYSTEM
    )
    claim_policy_sisnet_df = unify_null_values(df_dict["Claim_Policy_main"]).drop(
        const.SOURCE_SYSTEM
    )
    claim_section_scs_df = unify_null_values(
        df_dict["Claim_Section_main_ES_historic"]
    ).drop(const.SOURCE_SYSTEM)
    claim_transaction_component_sisnet_df = unify_null_values(
        df_dict["Claim_TransactionComponent_main"]
    ).drop(const.SOURCE_SYSTEM)
    # policy
    original_policy_scs_df = unify_null_values(
        df_dict["SisnetSCSMappingTableES_original_scs_policy"]
    ).drop(const.SOURCE_SYSTEM)
    unmapped_policy_info = unify_null_values(
        df_dict["SisnetSCSMappingTableES_unmapped_scs"]
    ).drop(const.SOURCE_SYSTEM)
    # claim
    cleaned_claim_map_df = OrigenCleaner.apply(raw_claim_map_df)
    full_claim_map_df = get_full_claim_map_df(
        cleaned_claim_map_df, claim_policy_sisnet_df, claim_section_scs_df
    )

    # policy
    unmapped_policy_with_scs_df = add_scs_columns_to_unmapped(
        unmapped_policy_info, original_policy_scs_df
    )
    unmapped_policy_with_scs_and_dekking_df = add_dekking_nummer(
        unmapped_policy_with_scs_df
    )
    unmapped_policy_final_df = fix_scs_keyidpolis(
        unmapped_policy_with_scs_and_dekking_df
    )

    # validate mapping

    quarantined_df = ValidateMapping.get_quarantined_mapping(
        full_claim_map_df, unmapped_policy_info, unmapped_policy_final_df
    )
    # Union Dynamic Mapping with Static Mapping

    full_claim_map_df = union_dynamic_mapping_with_static_mapping(
        full_claim_map_df, unmapped_policy_info, unmapped_policy_final_df
    )

    # merge quarantined_df with full_claim_map_df

    full_claim_map_df = merge_quarantined_df_with_full_claim_map_df(
        full_claim_map_df, quarantined_df
    )

    # Add SCS_Section_info to full_claim_map_df

    full_claim_map_product_description_df = add_product_description(full_claim_map_df)

    # handle duplications

    deduplicated_full_map_df = Deduplicator.apply(
        full_claim_map_product_description_df, claim_transaction_component_sisnet_df
    )

    # Apply hardcoded fixes

    processed_df = apply_hardcoded_fixes(deduplicated_full_map_df)

    output_df = (
        processed_df.withColumn(
            "SectionProductCode", F.col("SCS_KeyDekkingsNummer")
        ).withColumn(const.SOURCE_SYSTEM, F.lit("SCS_SISNET_MAP_TEMP_SOURCE"))
        # TEMP SOURCE to avoid redo logic
    )
    return output_df
