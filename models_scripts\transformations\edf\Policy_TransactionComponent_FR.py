from functools import reduce

import pyspark.sql.functions as F
from pyspark.sql import DataFrame

from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.currency import PolicyCurrencyConversor


def create_transaction_component_columns(df: DataFrame) -> DataFrame:
    """Generate the Policy.TransactionComponent columns."""
    components = [
        (
            "GWP - IPT + Commission",
            "PRIMEHT",
            "Prime HT annuaelle compagnie y compris",
            100,
        ),
        ("IPT", "IPT", "Taux", 100),
        ("Commission", "COMM", "Commission", 100),
    ]

    # Create an empty list to hold the exploded rows
    exploded_rows = []

    for component, code, description, percentage in components:
        if component == "GWP - IPT + Commission":
            component_df = df.select(
                "KeyIdPolis",
                "KeyDekkingsNummer",
                "KeyFactuurnummer",
                F.lit("EUR").alias("TransactionComponentAdditionsDeductionsIndicator"),
                (<PERSON>.col("GWP") - <PERSON>.col("IPT") + <PERSON>.col("Commission")).alias(
                    "TransactionComponentAmount"
                ),
                F.lit(code).alias("TransactionComponentTypeCode"),
                F.lit(description).alias("TransactionComponentTypeDescription"),
                F.lit(percentage).alias("TransactionComponentPercentage"),
                F.lit("").alias("TransactionComponentTerritory"),
            )
        else:
            component_df = df.select(
                "KeyIdPolis",
                "KeyDekkingsNummer",
                "KeyFactuurnummer",
                F.lit("EUR").alias("TransactionComponentAdditionsDeductionsIndicator"),
                F.col(component).alias("TransactionComponentAmount"),
                F.lit(code).alias("TransactionComponentTypeCode"),
                F.lit(description).alias("TransactionComponentTypeDescription"),
                F.lit(percentage).alias("TransactionComponentPercentage"),
                F.lit("").alias("TransactionComponentTerritory"),
            )
        exploded_rows.append(component_df)

    # Union all the component DataFrames to create the final TransactionComponent table
    transaction_component_df = reduce(DataFrame.unionAll, exploded_rows)

    # Ensure the "TransactionComponentAmount" column is not null:
    transaction_component_df = transaction_component_df.na.fill(
        {"TransactionComponentAmount": 0}
    )

    return transaction_component_df


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["TransactionComponent_main"]
    transaction_df = df_dict["Policy_Transaction"]
    exchange_rate_df = df_dict["TransactionComponent_exchange_rate"]

    main_df = create_transaction_component_columns(main_df)
    component_with_converted_currencies_df = PolicyCurrencyConversor.add_new_columns(
        main_df, transaction_df, exchange_rate_df
    )

    return component_with_converted_currencies_df
