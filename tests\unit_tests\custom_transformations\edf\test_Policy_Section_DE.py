from mines2.core.extensions.misc import assert_dataframe_equality
from pyspark.sql import Row

from models_scripts.transformations.common.misc import TEMP_COUNTRY_COLUMN
import models_scripts.transformations.edf.Policy_Section_DE as transform

def test_add_key_reserving_and_german_country_col(spark):
    """Test the add_key_reserving_and_german_country_col custom function."""
    # Create test data
    policy_data = [
        Row(KeyIdPolis=1, PolicyProductCode="ABC", Country="DE"),
        Row(KeyIdPolis=2, PolicyProductCode="DEF", Country="CH"),
    ]
    policy_df = spark.createDataFrame(policy_data)

    policy_array_section_data = [
        Row(KeyIdPolis=1, SectionProductCode="123"),
        Row(KeyIdPolis=2, SectionProductCode="456"),
    ]
    policy_array_section_df = spark.createDataFrame(policy_array_section_data)

    # Call the function being tested
    input_data = {
        "Policy_Policy": policy_df,
        "Section_main": policy_array_section_df,
    }
    output_df = transform.add_key_reserving_and_german_country_col(policy_array_section_df, policy_df)

    # Assert the output is as expected
    expected_df = spark.createDataFrame(
        [
            (1, "123", "DE", "DE:ABC:123"),
            (2, "456", "CH", "DE:DEF:456"),
        ],
        [
            "KeyIdPolis",
            "SectionProductCode",
            TEMP_COUNTRY_COLUMN,
            "KeyReserving",
        ],
    )
    assert_dataframe_equality(output_df, expected_df)
