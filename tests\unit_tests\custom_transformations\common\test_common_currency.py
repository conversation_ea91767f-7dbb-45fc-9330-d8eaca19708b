import pyspark.sql.functions as F
import pytest
from mines2.core.extensions.misc import assert_dataframe_equality
from pyspark.sql.types import *

from models_scripts.transformations.edf.common.currency import CurrencyConversorBase


class TestCurrencyConversor:
    def test_latest_closing_rate_returns_correct_rate(self, spark):
        data = [
            ("Closing", "USD", "EUR", 0.85, "2022-01-01"),
            ("Closing", "USD", "EUR", 0.84, "2022-01-02"),
            ("Closing", "USD", "GBP", 0.75, "2022-01-01"),
            ("Average", "USD", "GBP", 0.85, "2022-01-02"),
        ]
        df = spark.createDataFrame(
            data, ["RateType", "FromCcy", "ToCcy", "FXRate", "RateDate"]
        )

        # noinspection PyProtectedMember
        result_df = CurrencyConversorBase("undefined_col")._get_latest_closing_rate(df)

        expected_data = [("USD", "EUR", 0.84), ("USD", "GBP", 0.75)]
        expected_df = spark.createDataFrame(
            expected_data, ["FromCcy", "ToCcy", "FXRate"]
        )

        assert_dataframe_equality(result_df, expected_df)

    def test_pivoted_exchange_rate_returns_correct_format(self, spark):
        data = [("USD", "EUR", 0.86), ("USD", "GBP", 0.75)]
        df = spark.createDataFrame(data, ["FromCcy", "ToCcy", "FXRate"])

        # noinspection PyProtectedMember
        result_df = CurrencyConversorBase("undefined_col")._get_pivoted_exchange_rate(
            df
        )

        expected_data = [("USD", 0.86, 0.75, None, None)]
        expected_schema = StructType(
            [
                StructField("FromCcy", StringType()),
                StructField("EUR", DoubleType()),
                StructField("GBP", DoubleType()),
                StructField("USD", DoubleType()),
                StructField("CAD", DoubleType()),
            ]
        )
        expected_df = spark.createDataFrame(expected_data, expected_schema)

        assert_dataframe_equality(result_df, expected_df)

    def test_create_new_column_mapping(self, spark):
        # noinspection PyProtectedMember
        result = CurrencyConversorBase("Amount")._create_new_column_select_query()
        expected = {
            "AmountRounded": F.round(F.col("Amount"), 2),
            "AmountEUR": F.col("Amount") / F.col("EUR"),
            "AmountGBP": F.col("Amount") / F.col("GBP"),
            "AmountUSD": F.col("Amount") / F.col("USD"),
            "AmountCAD": F.col("Amount") / F.col("CAD"),
            "AmountRoundedEUR": F.round(F.col("Amount") / F.col("EUR"), 2),
            "AmountRoundedGBP": F.round(F.col("Amount") / F.col("GBP"), 2),
            "AmountRoundedUSD": F.round(F.col("Amount") / F.col("USD"), 2),
            "AmountRoundedCAD": F.round(F.col("Amount") / F.col("CAD"), 2),
        }
        expected_query = [
            value.alias(name).__repr__() for name, value in expected.items()
        ]
        result_query = [expr.__repr__() for expr in result]

        assert set(result_query) == set(expected_query)

    def test_add_converted_without_transaction(self, spark):
        transaction_component_df = spark.createDataFrame(
            [
                ("KeyIdPolis1", "KeyDekkingsNummer1", 100.0),
                ("KeyIdPolis2", "KeyDekkingsNummer2", 200.0),
                ("KeyIdPolis3", "KeyDekkingsNummer3", 300.5555),
            ],
            ["id1", "id2", "Amount"],
        )

        exchange_rate_df = spark.createDataFrame(
            [
                ("USD", 0.86, 0.75, 1.2, 1.3),
                ("EUR", 1.16, 1.33, 0.92, 0.76),
            ],
            ["FromCcy"] + CurrencyConversorBase.CURRENCIES_TO_CONVERT,
        )

        result_df = CurrencyConversorBase(
            "Amount"
        )._add_converted_column_for_single_currency(
            transaction_component_df, exchange_rate_df, "USD"
        )

        expected_df = spark.createDataFrame(
            [
                (
                    "KeyIdPolis1",
                    "KeyDekkingsNummer1",
                    100.0,
                    100.0,
                    116.27906976744187,
                    116.28,
                    133.33333333333334,
                    133.33,
                    83.33333333333334,
                    83.33,
                    76.92307692307692,
                    76.92,
                ),
                (
                    "KeyIdPolis2",
                    "KeyDekkingsNummer2",
                    200.0,
                    200.0,
                    232.55813953488374,
                    232.56,
                    266.6666666666667,
                    266.67,
                    166.66666666666669,
                    166.67,
                    153.84615384615384,
                    153.85,
                ),
                (
                    "KeyIdPolis3",
                    "KeyDekkingsNummer3",
                    300.5555,
                    300.56,
                    349.4831395348837,
                    349.48,
                    400.74066666666664,
                    400.74,
                    250.46291666666667,
                    250.46,
                    231.19653846153844,
                    231.2,
                ),
            ],
            [
                "id1",
                "id2",
                "Amount",
                "AmountRounded",
                "AmountGBP",
                "AmountRoundedGBP",
                "AmountEUR",
                "AmountRoundedEUR",
                "AmountUSD",
                "AmountRoundedUSD",
                "AmountCAD",
                "AmountRoundedCAD",
            ],
        )

        assert_dataframe_equality(result_df, expected_df)
