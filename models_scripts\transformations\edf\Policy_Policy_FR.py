import pyspark.sql.functions as F
from pyspark.sql import DataFrame
from pyspark.sql.window import Window

from models_scripts.transformations.common.traits import business_logic
from models_scripts.transformations.edf.common.currency import (
    PolicyPolicyCurrencyConversor,
)


def deduplicate_french_data(df: DataFrame) -> DataFrame:
    portfolio_df = df.filter(F.col("BrokerFullName_copy") == "MARKEL")
    other_binders_df = df.filter(F.col("BrokerFullName_copy") != "MARKEL")

    # Summing the premium columns
    aggregated_df = other_binders_df.groupBy("IdPolis", "PolicySequenceNumber").agg(
        F.sum("GWP").alias("GWP"),
        F.sum("NWP").alias("NWP"),
        F.sum("IPT").alias("IPT"),
        F.sum("Commission").alias("Commission"),
        F.max("DateReference").alias("DateReference"),
    )

    # Joining back to get the other columns
    window_spec = Window.partitionBy("IdPolis", "PolicySequenceNumber").orderBy(
        F.desc("DateReference")
    )
    other_binders_df = other_binders_df.drop(
        "Prime_HT", "Prime_TTC", "IPT", "Commission", "GWP", "NWP"
    )
    other_binders_df = other_binders_df.withColumn(
        "row_num", F.row_number().over(window_spec)
    )
    other_binders_df = other_binders_df.filter(F.col("row_num") == 1).drop("row_num")

    result_df = other_binders_df.join(
        aggregated_df,
        on=["IdPolis", "PolicySequenceNumber", "DateReference"],
        how="inner",
    )

    result_df = result_df.unionByName(portfolio_df, allowMissingColumns=True)
    return result_df


@business_logic
def custom_transformation(df_dict: dict[str, DataFrame], **kwargs) -> DataFrame:
    main_df = df_dict["Policy_main"]
    exchange_rate_df = df_dict["Policy_exchange_rate"]

    main_df = deduplicate_french_data(main_df)
    main_df = main_df.withColumns(
        {
            "AssuredAddressArea": F.col("AssuredPostCode"),
            "AssuredProvince": F.col("AssuredPostCode"),
            "ReUnderwritingExercise": F.lit(None),
        }
    )
    main_df = PolicyPolicyCurrencyConversor.add_currency_columns(
        main_df, exchange_rate_df
    )

    return main_df
