Country: FR
existCustomTransformation: 'True'

dataSource:
- name: main
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: dev_europeandatalake_01
      uat: uat_europeandatalake_01
      prod: prod_europeandatalake_01
    schema: FrenchBordereaux
    table: Support_PolicyBase

- name: exchange_rate
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: test_mintdatalake_02
      uat: test_mintdatalake_02
      prod: prod_mintdatalake_02
    schema: rdm_dbo
    table: mint_mapping_roe
    sourceSystem: MINT_RAW_DATALAKE

ColumnSpecs:
  ClaimsPolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: FR
        - source: COLUMN
          name: PolicyCode
        sep: ':'
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: FR
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
  BrokerCodeID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: FR
        - source: COLUMN
          name: BrokerCode
        sep: ':'
  UnderwriterCodeID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: FR
        - source: COLUMN
          name: UnderwriterCode
        sep: ':'
  ReUnderwritingExercise:
    NotInSource: True
  BusinessClassification:
    NotInSource: True
  PolicySequenceNumber:
    NotInSource: True
  IPTLiable:
    NotInSource: True
