Country: NL
existCustomTransformation: 'True'
dataSource:
- name: main
  type: SourceProgressNL
  parameters:
    sqlFileName: EDF PolicyArrayTransactionComponent Full.sql
    querySourceType: SQL_FILE

- name: exchange_rate
  type: DatabricksCatalog
  parameters:
    catalogName:
      dev: test_mintdatalake_02
      uat: test_mintdatalake_02
      prod: prod_mintdatalake_02
    schema: rdm_dbo
    table: mint_mapping_roe
    sourceSystem: MINT_RAW_DATALAKE

- name:  transaction
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Policy
    Table: Transaction

- name: extra_records
  type: EuropeanDatalake
  parameters:
    Layer: clean
    subModel: Support
    Table: NewTransactionComponents

ColumnSpecs:
  KeyIdPolis:
    locale: en_US.utf8
  KeyDekkingsNummer:
    locale: en_US.utf8
  KeyFactuurnummer:
    locale: en_US.utf8
  TransactionComponentAdditionsDeductionsIndicator:
    locale: en_US.utf8
    sourceName: TCAdditionsDeductionsIndicator
  TransactionComponentAmount:
    locale: en_US.utf8
  TransactionComponentAmountGBP:
    NotInSource: True
  TransactionComponentAmountEUR:
    NotInSource: True
  TransactionComponentAmountUSD:
    NotInSource: True
  TransactionComponentAmountCAD:
    NotInSource: True
  TransactionComponentAmountRounded:
    NotInSource: True
  TransactionComponentAmountRoundedGBP:
    NotInSource: True
  TransactionComponentAmountRoundedEUR:
    NotInSource: True
  TransactionComponentAmountRoundedUSD:
    NotInSource: True
  TransactionComponentAmountRoundedCAD:
    NotInSource: True
  TransactionComponentTypeCode:
    locale: en_US.utf8
  TransactionComponentTypeDescription:
    locale: en_US.utf8
    sourceName: TComponentTypeCodeDescription
  TransactionComponentPercentage:
    locale: en_US.utf8
  TransactionComponentTerritory:
    locale: en_US.utf8
  PolicyTransComponentID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        - source: COLUMN
          name: KeyFactuurnummer
        - source: COLUMN
          name: TransactionComponentTypeCode
        sep: ':'
  PolicyTransactionID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: KeyIdPolis
        - source: COLUMN
          name: KeyDekkingsNummer
        - source: COLUMN
          name: KeyFactuurnummer
        sep: ':'
  PolicyID:
    mapType: calculated
    func:
      name: concat
      args:
        columns:
        - source: CONSTANT
          value: NL
        - source: COLUMN
          name: KeyIdPolis
        sep: ':'
