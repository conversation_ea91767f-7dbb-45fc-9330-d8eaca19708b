select distinct pb.idpolis as 'KeyIdPolis'
, ltrim(to_char(pv.dekkingsnummer)) + ltrim(to_char(pb.productcode)) + ltrim(to_char(pv.dekkingscode)) as 'KeyDekkingsnummer'
, ltrim(to_char(pb.productcode)) + ltrim(to_char(pv.dekkingscode)) as 'SectionProductCode'
, pv.dekkingsnummer                                                as 'SectionReference'
from pub.premieboekingverdeling pv
inner join pub.premieboeking pb on pb.bedrijffactuurnummer = pv.bedrijffactuurnummer
left outer join pub.polisversie pyoa on pyoa.idpolis = pb.idpolis
left outer join pub.histpolisversie hyoa on hyoa.idpolis = pb.idpolis

union

select distinct pb.idpolis as 'KeyIdPolis'
, ltrim(to_char(pv.dekkingsnummer)) + ltrim(to_char(pb.productcode)) + ltrim(to_char(pv.dekkingscode)) as 'KeyDekkingsnummer'
, ltrim(to_char(pb.productcode)) + ltrim(to_char(pv.dekkingscode)) as 'SectionProductCode'
, pv.dekkingsnummer                                                as 'SectionReference'
from pub.premieboekingtpverdeling pv
inner join pub.premieboekingtp pb on pb.idpremieboekingtp = pv.idpremieboekingtp
left outer join pub.polisversie pyoa on pyoa.idpolis = pb.idpolis
left outer join pub.histpolisversie hyoa on hyoa.idpolis = pb.idpolis