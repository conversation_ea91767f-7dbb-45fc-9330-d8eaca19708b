SELECT DISTINCT
    A.POLICODE AS 'KeyIdPolis',
    A.ADMO<PERSON> AS 'AdministrationOfficeCode',
    A.ASSADDAR AS 'AssuredAddressArea',
    A.ASSADDCI AS 'AssuredAddressCity',
    A.ASSADDST AS 'AssuredAddressStreet',
    A.ASSANNTU AS 'AssuredAnnualTurnover',
    A.ASANTUCU AS 'AssuredAnnualTurnoverCurrency',
    A.ASSUCODE AS 'AssuredCode',
    A.ASSUCOUN AS 'AssuredCountry',
    A.ASSFULNA AS 'AssuredFullName',
    A.ASMAACCO AS 'AssuredMainActivityCode',
    'None' AS 'AssuredMainActivityDescription',
    A.ASNFUTEM AS 'AssuredNumberOfFullTimeEmployees',
    A.ASSPOSCO AS 'AssuredPostCode',
    A.ASSUPROV AS 'AssuredProvince',
    A.ASSSHONA AS 'AssuredShortName',
    'None' AS 'AssuredState',
    A.ASSUTERR AS 'AssuredTerritory',
    A<PERSON>OA<PERSON> AS 'BrokerAddressArea',
    A.<PERSON> AS 'BrokerAddressCity',
    A.BROADDST AS 'BrokerAddressStreet',
    A.BROKCODE AS 'BrokerCode',
    --'None' AS 'BrokerCodeID',
    A.BROKCOUN AS 'BrokerCountry',
    A.BROFULNA AS 'BrokerFullName',
    A.BROGROCO AS 'BrokerGroupCode',
    A.BROGRONA AS 'BrokerGroupName',
    A.BROPOSCO AS 'BrokerPostcode',
    A.BROKPROV AS 'BrokerProvince',
    'None' AS 'BrokerShortName',
    'None' AS 'ClaimsBasisCode',
    'None' AS 'ClaimsBasisDescription',
    'None' AS 'CoverholderCode',
    'None' AS 'CoverholderName',
    A.CUSTCLAS AS 'CustomerClassification',
    A.CUSCLACO AS 'CustomerClassificationCode',
    'None' AS 'DistributionPartner',
    'None' AS 'DistributionType',
    A.EXPIDATE AS 'ExpiryDate',
    'None' AS 'IBCIndustryCode',
    A.INCEDATE AS 'InceptionDate',
    'None' AS 'InsurerEntityCode',
    'None' AS 'InsurerEntityDescription',
    'None' AS 'LapsedReason',
    'None' AS 'Layer',
    '' AS 'OrderPercentage',
    'None' AS 'OutwardsFACIndicator',
    'None' AS 'PeerReview1',
    'None' AS 'PeerReview1Code',
    'None' AS 'PeerReview1Comment',
    '' AS 'PeerReview1Date',
    'None' AS 'PeerReview2',
    'None' AS 'PeerReview2Code',
    'None' AS 'PeerReview2Comment',
    '' AS 'PeerReview2Date',
    A.PACETYPE AS 'PlacementType',
    A.POLICODE AS 'PolicyCode',
    A.POLAMODA AS 'PolicyLastModifiedDate',
    A.POLPROCO AS 'PolicyProductCode',
    A.POLPRODE AS 'PolicyProductDescription',
    A.POLIREFE AS 'PolicyReference',
    '-' AS 'PreviousPolicyReference',
    'SISnet' AS 'PreviousSourceSystem',
    'SISnet' AS 'PreviousSourceSystemDescription',
    A.PROOFFCO AS 'ProducingOfficeCode',
    'None' AS 'QuotationReference',
    'None' AS 'ReferralUnderwriter',
    'None' AS 'ReinsurancePolicyIndicator',
    'None' AS 'ReinsuranceReference',
    'None' AS 'ReinsuredAddressArea',
    'None' AS 'ReinsuredAddressCity',
    'None' AS 'ReinsuredAddressStreet',
    '' AS 'ReinsuredAnnualTurnover',
    'None' AS 'ReinsuredAnnualTurnoverCurrency',
    'None' AS 'ReinsuredCode',
    'None' AS 'ReinsuredCountry',
    'None' AS 'ReinsuredFullName',
    'None' AS 'ReinsuredNumberOfFullTimeEmployees',
    'None' AS 'ReinsuredPostcode',
    'None' AS 'ReinsuredProvince',
    'None' AS 'ReinsuredShortName',
    'None' AS 'ReinsuredState',
    'None' AS 'ReinsuredTerritory',
    'None' AS 'RenewalPolicyIndicator',
    A.RENPOLRE AS 'RenewalPolicyReference',
    A.RENSEQNU AS 'RenewalSequenceNumber',
    'None' AS 'RiskCode',
    A.STATCODE AS 'StatusCode',
    A.STATDESC AS 'StatusDescription',
    A.TACRENIN AS 'TacitRenewalIndicator',
    A.TERRCODE AS 'TerrorismCode',
    A.TIMEZONE AS 'Timezone',
    'None' AS 'TradeCodeOrIndustry',
    'None' AS 'TrustFund',
    'None' AS 'UnderlyingLimit',
    'None' AS 'UnderlyingLimitCurrency',
    A.UNDECODE AS 'UnderwriterCode',
    'None' AS 'UnderwriterCodeID',
    A.UNDENAME AS 'UnderwriterName',
    A.WRITDATE AS 'WrittenDate',
    A.YEAOFACC AS 'YearOfAccount'
FROM NTJDWHMRK..MEDFPOLI A
WHERE A.ID_MEDFPOLI IN (
    SELECT MAX(Z.ID_MEDFPOLI)
    FROM NTJDWHMRK..MEDFPOLI Z
    WHERE
        A.POLICODE = Z.POLICODE
        AND Z.POLAMODA = (
            SELECT MAX(B.POLAMODA)
            FROM NTJDWHMRK..MEDFPOLI B
            WHERE
                B.FECHALTA >= CONVERT(datetime,'01/01/2000',103)
                AND Z.POLICODE = B.POLICODE
        )
)
