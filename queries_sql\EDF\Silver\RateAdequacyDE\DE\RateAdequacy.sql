/* Formatted on 4/18/2023 4:49:18 PM (QP5 v5.300) (Formatting updated on 2024-12-18) */
SELECT
    POLICYID,
    POLICYSECTIONID,
    POLICY_NR,
    CONTRACT_NR,
    RISK_NR,
    YEAROFACCOUNT,
    SOURCE,
    /* PRM_NO_FEES is nett_premium minus discount_surcharge */
    PRM_NO_FEES,
    RISK_DISCOUNT_OR_SURCHARGE,
    RATE_SURCHARGE,
    ADMIN_COST,
    PRM_ADJ,
    final_net_prem,
    final_tech_prem,
    CASE
        WHEN final_tech_prem <> 0 THEN
            ROUND ( (PRM_NO_FEES - RISK_DISCOUNT_OR_SURCHARGE) /*BASE_PRM_RSK*/
            / final_tech_prem * 100, 4)
        ELSE
            NULL
    END AS tech_ratio,
    MINPREM_ACTIV,
    TECHNICAL_PREMIUM_AVAILABLE,
    USA_CANADA_INSURED,
    ROW_INSURED,
    ISRCF,
    ISQUOTE,
    TARIFSETTOMANUAL
FROM
    (
        SELECT
            POLICYID,
            POLICYSECTIONID,
            POL_POLICEN_NR                                        AS POLICY_NR,
            VER_VERTRAG_NR                                        AS CONTRACT_NR,
            GOR_RISIKO_NR                                         AS RISK_NR,
            YEAROFACCOUNT,
            SOURCE,
            /* PRM_NO_FEES is nett_premium minus discount_surcharge */
            PRM_NO_FEES,
            RISK_DISCOUNT_OR_SURCHARGE,
            RATE_SURCHARGE,
            ADMIN_COST,
            PRM_NO_FEES * (1 + RATE_SURCHARGE) * (1 + ADMIN_COST) AS PRM_ADJ,
            CASE
                WHEN MINPREM = (PRM_NO_FEES - RISK_DISCOUNT_OR_SURCHARGE) THEN
                    'yes'
                ELSE
                    'no'
            END                                                   AS MINPREM_ACTIV,
            PRM_NO_FEES - RISK_DISCOUNT_OR_SURCHARGE              AS final_net_prem,
            CASE
                WHEN SOURCE = 'MARKELNOW'
                /* Exali tarifs are not captured in Winsure; */
                /* MarkelNow tarifs are captured in winsure, but their names differs from those in winsure, so the tech_prm cannot be calculated) */
                THEN
                    PRM_NO_FEES - RISK_DISCOUNT_OR_SURCHARGE - MN_CORRECTION
                WHEN SOURCE = 'EXALI' THEN
                    PRM_NO_FEES - RISK_DISCOUNT_OR_SURCHARGE
                ELSE
                    TECH_PRM_RSK
            END                                                   AS final_tech_prem,
            TECHNICAL_PREMIUM_AVAILABLE,
            USA_CANADA_INSURED,
            ROW_INSURED,
            ISRCF,
            ISQUOTE,
            TARIFSETTOMANUAL,
            MN_CORRECTION
        FROM
            (
                SELECT
                    POLICYID,
                    POLICYSECTIONID,
                    POL_POLICEN_NR,
                    VER_VERTRAG_NR,
                    GOR_RISIKO_NR,
                    SOURCE,
                    TECHNICAL_PREMIUM_AVAILABLE,
                    USA_CANADA_INSURED,
                    ROW_INSURED,
                    ISRCF,
                    ISQUOTE,
                    TARIFSETTOMANUAL,
                    /* PRM_NO_FEES is nett_premium minus discount_surcharge */
                    PRM_NO_FEES,
                    CASE
                        WHEN SOURCE = 'MARKELNOW' THEN
                            RISK_DISCOUNT_OR_SURCHARGE - MN_CORRECTION
                        ELSE
                            RISK_DISCOUNT_OR_SURCHARGE
                    END AS RISK_DISCOUNT_OR_SURCHARGE,
                    RATE_SURCHARGE,
                    ADMIN_COST,
                    TECH_PRM_RSK,
                    YEAROFACCOUNT,
                    MINPREM,
                    MN_CORRECTION
                FROM
                    (
                        SELECT
                            POLICYID,
                            POLICYSECTIONID,
                            WINSURE_DATA.POL_POLICEN_NR,
                            WINSURE_DATA.VER_VERTRAG_NR,
                            WINSURE_DATA.GOR_RISIKO_NR,
                            SOURCE,
                            TECHNICAL_PREMIUM_AVAILABLE,
                            TARIFSETTOMANUAL,
                            SEV.GESAMTPRAEMIE (
                                WINSURE_DATA.POL_POLICEN_NR,
                                WINSURE_DATA.VER_VERTRAG_NR,
                                WINSURE_DATA.GEF_GEFAHR_NR,
                                WINSURE_DATA.OBJ_OBJEKT_NR,
                                WINSURE_DATA.GOR_RISIKO_NR,
                                TO_DATE('$DATE', 'DD-MM-YYYY')
                            )      AS PRM_NO_FEES,
                            SEV.ZAB_SUMME (
                                WINSURE_DATA.POL_POLICEN_NR,
                                WINSURE_DATA.VER_VERTRAG_NR,
                                WINSURE_DATA.GEF_GEFAHR_NR,
                                WINSURE_DATA.OBJ_OBJEKT_NR,
                                WINSURE_DATA.GOR_RISIKO_NR,
                                TO_DATE('$DATE', 'DD-MM-YYYY'),
                                GOR_VERS_SUMME,
                                GOR_NETTOPRAEMIE_ERM,
                                NULL,
                                NULL,
                                GOR_BRUTTOPRAEMIE,
                                GOR_RISIKO_TYP
                            )     AS RISK_DISCOUNT_OR_SURCHARGE,
                            CASE
                                WHEN SOURCE = 'MARKELNOW' THEN
                                    CASE
                                        WHEN ZAB_ZU_ABSCHLAG_TYP IS NOT NULL THEN
                                            NVL (ZAB_BETRAG, ZAB_PRAEMIENSATZ)
                                        ELSE
                                            0
                            END ELSE NULL END  AS MN_CORRECTION,
                            CASE
                                WHEN GOR_TARIF = 'GOR_TARIF_MAN' THEN
                                    GOR_NETTOPRAEMIE_ERM
                                ELSE
                                    (NVL (RHA_TARIFBEITRAG1, 0) + NVL (RHA_TARIFBEITRAG2, 0) + NVL (RHA_TARIFBEITRAG3, 0))
                            END AS TECH_PRM_RSK,
                            SEV.RATENZUSCHLAG (
                                WINSURE_DATA.POL_POLICEN_NR,
                                WINSURE_DATA.VER_VERTRAG_NR,
                                NULL,
                                NULL,
                                NULL,
                                1,
                                TO_DATE('$DATE', 'DD-MM-YYYY'),
                                NULL
                            )               AS RATE_SURCHARGE,
                            SEV.VERWALTUNGSKOSTEN (
                                WINSURE_DATA.POL_POLICEN_NR,
                                WINSURE_DATA.VER_VERTRAG_NR,
                                NULL,
                                NULL,
                                NULL,
                                1,
                                TO_DATE('$DATE', 'DD-MM-YYYY'),
                                NULL
                            )               AS ADMIN_COST,
                            EXP_YEAR_OF_ACCOUNT AS YEAROFACCOUNT,
                            USA_CANADA_INSURED,
                            ROW_INSURED,
                            ISRCF,
                            ISQUOTE,
                            MINPREM
                        FROM
                            (
                                SELECT
                                    CASE
                                        WHEN POL_STATUS = 'Angebot' THEN
                                            ''
                                        ELSE
                                            'DE:'
                                                || MARKEL.PKG_EXPORT_EDF.POLICYCODE ( GOR.POL_POLICEN_NR, GOR.VER_VERTRAG_NR, TO_DATE('$DATE', 'DD-MM-YYYY'))
                                    END AS POLICYID,
                                    CASE
                                        WHEN POL_STATUS = 'Angebot' THEN
                                            ''
                                        ELSE
                                            'DE:'
                                                || MARKEL.PKG_EXPORT_EDF.POLICYCODE ( GOR.POL_POLICEN_NR, GOR.VER_VERTRAG_NR, TO_DATE('$DATE', 'DD-MM-YYYY'))
                                                || ':'
                                                || GOR.OBJ_OBJEKT_NR
                                                || '-'
                                                || GOR.GOR_RISIKO_NR
                                    END AS POLICYSECTIONID,
                                    GOR.POL_POLICEN_NR,
                                    GOR.VER_VERTRAG_NR,
                                    GOR.GEF_GEFAHR_NR,
                                    GOR.OBJ_OBJEKT_NR,
                                    GOR.GOR_RISIKO_NR,
                                    GOR.POL_GUELTIG_AB,
                                    GOR_VERS_SUMME,
                                    GOR_MINDESTPRAEMIE,
                                    GOR_NETTOPRAEMIE_ERM,
                                    GOR_BRUTTOPRAEMIE,
                                    GOR_RISIKO_TYP,
                                    RHA_TARIFBEITRAG1,
                                    RHA_TARIFBEITRAG2,
                                    RHA_TARIFBEITRAG3,
                                    POL.POL_POLICE_TYP,
                                    GOR.GOR_TARIF,
                                    CASE
                                        WHEN POL.POL_POLICE_TYP LIKE '%EXALI%' THEN
                                            'EXALI'
                                        WHEN POL.POL_POLICEN_NR_ALT LIKE '%ON%' THEN
                                            'MARKELNOW'
                                        ELSE
                                            'WINSURE'
                                    END SOURCE,
                                    CASE
                                        WHEN RHA_TARIFBEITRAG1 IS NULL THEN
                                            'no'
                                        ELSE
                                            'yes'
                                    END TECHNICAL_PREMIUM_AVAILABLE,
                                    CASE
                                        WHEN RHA_BEITRAG2 IS NOT NULL THEN
                                            'yes'
                                        ELSE
                                            'no'
                                    END USA_CANADA_INSURED,
                                    CASE
                                        WHEN RHA_BEITRAG3 IS NOT NULL THEN
                                            'yes'
                                        ELSE
                                            'no'
                                    END ROW_INSURED,
                                    CASE
                                        WHEN VER_TARIFVERSION LIKE '%v1%' OR VER_TARIFVERSION LIKE '%Rater%' OR VER_TARIFVERSION LIKE '%RCF%' THEN
                                            'yes'
                                        ELSE
                                            'no'
                                    END ISRCF,
                                    CASE
                                        WHEN POL_STATUS = 'Angebot' THEN
                                            'yes'
                                        ELSE
                                            'no'
                                    END ISQUOTE,
                                    CASE
                                        WHEN GOR_TARIF = 'GOR_TARIF_MAN' THEN
                                            'yes'
                                        ELSE
                                            'no'
                                    END TARIFSETTOMANUAL,
                                    GOR_MINDESTPRAEMIE AS MINPREM
                                FROM
                                    SEV.GEFAHR_OBJEKT_RISIKO GOR
                                    JOIN SEV.RISIKO_HAFTPFLICHT RSK ON (
                                        RSK.POL_POLICEN_NR = GOR.POL_POLICEN_NR
                                        AND RSK.VER_VERTRAG_NR = GOR.VER_VERTRAG_NR
                                        AND RSK.GEF_GEFAHR_NR = GOR.GEF_GEFAHR_NR
                                        AND RSK.OBJ_OBJEKT_NR = GOR.OBJ_OBJEKT_NR
                                        AND RSK.GOR_RISIKO_NR = GOR.GOR_RISIKO_NR
                                        AND RSK.POL_GUELTIG_AB = GOR.POL_GUELTIG_AB
                                        AND RSK.POL_AENDERUNG = GOR.POL_AENDERUNG
                                    )
                                    JOIN SEV.POLICE POL ON (POL.POL_POLICEN_NR = GOR.POL_POLICEN_NR)
                                    JOIN SEV.VERTRAG VER ON (
                                        VER.POL_POLICEN_NR = GOR.POL_POLICEN_NR
                                        AND VER.VER_VERTRAG_NR = GOR.VER_VERTRAG_NR
                                    )
                                WHERE
                                    POL.POL_HISTORIE IN ('AN', 'HN')
                                    AND POL.POL_ZUSTAND = 'lebend'
                                    AND VER.VER_HISTORIE IN ('AN', 'HN')
                                    AND VER.VER_ZUSTAND = 'lebend'
                                    AND GOR.GOR_HISTORIE IN ('AN', 'HN')
                                    AND GOR.GOR_ZUSTAND = 'lebend'
                                    AND GOR.POL_GUELTIG_AB <= TO_DATE('$DATE', 'DD-MM-YYYY')
                                    AND GOR.GOR_UNGUELTIG_AB >= TO_DATE('$DATE', 'DD-MM-YYYY')
                                    AND VER.POL_GUELTIG_AB <= TO_DATE('$DATE', 'DD-MM-YYYY')
                                    AND VER.VER_UNGUELTIG_AB >= TO_DATE('$DATE', 'DD-MM-YYYY')
                                    AND POL.POL_GUELTIG_AB <= TO_DATE('$DATE', 'DD-MM-YYYY')
                                    AND POL.POL_UNGUELTIG_AB >= TO_DATE('$DATE', 'DD-MM-YYYY')
                            )                             WINSURE_DATA
                            /* To get the correct year_of_account of a policyid */
                            JOIN MARKEL.EDF_EXPORT_POLICY_CODE EXP
                            ON (POLICYID = 'DE:'
                                || EXP_POLICY_CODE) LEFT JOIN
                            /* To get the discount/ surcharge which is used as an adjustment to the base_prm for MarkelNow Contracts */
                            (
                                SELECT * FROM SEV.ZU_ABSCHLAG
                                WHERE
                                    ZAB_HISTORIE IN ('AN',
                                    'HN')
                                    AND ZAB_ZUSTAND = 'lebend'
                                    AND POL_GUELTIG_AB <= TO_DATE('$DATE', 'DD-MM-YYYY')
                                    AND ZAB_UNGUELTIG_AB >= TO_DATE('$DATE', 'DD-MM-YYYY')
                                    AND ZAB_ZU_ABSCHLAG_TYP IN ('Rahmennachlass_MN',
                                    'Rahmenzuschlag_MN')
                            ) ZAB
                            ON (
                                ZAB.POL_POLICEN_NR = WINSURE_DATA.POL_POLICEN_NR
                                AND ZAB.VER_VERTRAG_NR = WINSURE_DATA.VER_VERTRAG_NR
                                AND ZAB.GEF_GEFAHR_NR = WINSURE_DATA.GEF_GEFAHR_NR
                                AND ZAB.OBJ_OBJEKT_NR = WINSURE_DATA.OBJ_OBJEKT_NR
                                AND ZAB.GOR_RISIKO_NR = WINSURE_DATA.GOR_RISIKO_NR
                            )
                    )
            )
    )